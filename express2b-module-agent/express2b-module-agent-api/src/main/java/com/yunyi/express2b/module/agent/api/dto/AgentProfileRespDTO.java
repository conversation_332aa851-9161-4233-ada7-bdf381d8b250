package com.yunyi.express2b.module.agent.api.dto;

import com.yunyi.express2b.framework.common.pojo.PageParam;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/5/15 9:53
 */


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AgentProfileRespDTO extends PageParam {

    /**
     * 代理商唯一标识
     */
    private Long id;
    /**
     * 关联用户中心的用户ID（SSOUSERID）
     */
    private Long memberId;
    /**
     * 代理商名称/联系人
     */
    private String name;
    /**
     * 手机号，唯一，用于登录和联系
     */
    private String mobile;
    /**
     * 邮箱（可选）
     */
    private String email;
    /**
     * 态（0-待审核, 1-正常, 2-冻结）对应AgentStatusEnum
     */
    private Integer status;
    /**
     * 当前等级ID，关联agent_level_config.id
     */
    private Long levelId;
    /**
     * 代理商等级名称
     */
    private String levelName;
    /**
     * 推荐人代理商ID（上级）
     */
    private Long referrerAgentId;
    /**
     * 代理商专属自定义价格模板ID
     */
    private Long customPricingTemplateId;
    /**
     * 关联钱包模块的钱包ID
     */
    private Long walletId;
    /**
     * 关联系统模块的租户ID
     */
    private Long tenantId;
    /**
     * 直接下级数量
     */
    private Integer directDownlineCount;
    /**
     * 团队总下级数量
     */
    private Integer totalDownlineCount;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;




}
