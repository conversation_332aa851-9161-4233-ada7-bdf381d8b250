package com.yunyi.express2b.module.agent.api.dto;

import com.yunyi.express2b.framework.common.pojo.PageParam;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 查询最近上级返回的DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.8
 * @since 2025/5/22 15:13
 */
@Data
public class RelationShipDTO extends PageParam {
    /**
     * id
     */
    private Long ancestorId;
    /**
     * 距离当前代理商最近的某一等级上级的 ID
     */
    private Long NearestSpecificLevelAncestorId;

    /**
     * 距离当前代理商最近的某一等级上级的名称
     */
    private String name;

    /**
     * 等级名称
     */
    private String levelName;

    /**
     * 与当前代理商的距离层级
     */
    private Integer depth;

    private LocalDateTime createTime;

}
