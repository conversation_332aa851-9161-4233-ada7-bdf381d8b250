package com.yunyi.express2b.module.agent.api;

import com.yunyi.express2b.module.agent.api.dto.*;

import java.util.List;

/**
 * 代理商模块 API 接口
 *
 * <AUTHOR>
 */
public interface AgentApi {
    
    /**
     * 获取代理商信息
     *
     * @param id 代理商编号
     * @return 代理商信息
     */
    // AgentDTO getAgent(Long id);

    /**
     * 查询代理商的所有上级（带层级距离)
     * @param agentId
     * @return
     */
    List<RelationShipDTO> getListByAgentId(Long agentId);

    /**
     * 通过代理商Id获取代理商档案以及levelName
     * @param agentId
     * @return
     */
    AgentProfileRespDTO getAgentProfile(Long agentId);

    /**
     * 查询距离当前代理商最近的特定级别上级
     * @param agentId
     * @param levelId
     * @return
     */
    RelationShipDTO getAgentProfileByAgentIdAndLevelId(Long agentId, Long levelId);

    /**
     * 根据代理商id获取代理商信息
     * @param agentId
     * @return
     */
    AgentInfoDTO getAgentInfo(Long agentId);


    /**
     * 获取代理商级别(testAccess)
     * @return
     */
    Long getAgentLevelId();

    /**
     * 根据memberId获取代理商
     */
    AgentInfoDTO getAgentByMemberId(Long memberId);

    /**
     * 获取代理商MemberId
     * @return
     */
    Long getAgentIdMemberId();

    /**
     * 代理商等级提升
     */
    int checkLevelUp();

    /**
     * 根据指定的等级id获取代理商id列表
     * @param id
     * @return
     */
    List<Long>  getAgentIdListByLevelId(Long id);

    /**
     * 通过代理商id查询团队代理商id
     * @param agentId
     * @return
     */
    List<Long> getTeamIdByAgentId(Long agentId);

    /**
     * 通过代理商id获取代理商等级提升规则参数
     * @param agentId
     * @return
     */
    UpLevelRuleDTO  getUpLevelRule(Long agentId);

    /**
     * 获取代理商直接下级
     * @param agentId
     * @return
     */
    List<Long> getAgentDirectIdList(Long agentId);

    /**
     * 获取代理商推广人数
     * @param agentActivityDTO
     * @return
     */
    AgentActivityDTO getAgentPromotersCount(AgentActivityDTO agentActivityDTO,List<Long> teamIds);
}