package com.yunyi.express2b.module.agent.api.dto;

import lombok.*;

/**
 * 定价模板 DO
 *这个就是从数据库拿值的对象
 * <AUTHOR>
 */
// 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
public class TemplateDTO{
    /**
     * 主键
     */
    private Long id;
    /**
     * 模板名称
     */
    private String templateName;
    /**
     * 加价类型_v1 (1-比例加价，2-固定金额加价-默认)
     */
    private Integer markupTypeV1;
    /**
     * 加价类型_v2 (1-比例加价，2-固定金额加价-默认)
     */
    private Integer markupTypeV2;
    /**
     * 首重重量v3
     */
    private Integer firstWeightMarkupV2;
    /**
     * 续重重量v3
     */
    private Integer additionalWeightMarkupV2;
    /**
     * 平台零售首重价_v2 (单位:分)
     */
    private Integer platformFirstPriceV2;
    /**
     * 平台零售续重价_v2 (单位:分)
     */
    private Integer platformAdditionalPriceV2;
    /**
     * 加价类型_v3 (1-比例加价，2-固定金额加价-默认)
     */
    private Integer markupTypeV3;
    /**
     * 首重重量v2
     */
    private Integer firstWeightMarkupV3;
    /**
     * 续重重量v2
     */
    private Integer additionalWeightMarkupV3;
    /**
     * 平台零售首重价_v3 (单位:分)
     */
    private Integer platformFirstPriceV3;
    /**
     * 平台零售续重价_v3 (单位:分)
     */
    private Integer platformAdditionalPriceV3;
    /**
     * 首重重量v1
     */
    private Integer firstWeightMarkupV1;
    /**
     * 续重重量v1
     */
    private Integer additionalWeightMarkupV1;
    /**
     * 平台零售首重价_v1 (单位:分)
     */
    private Integer platformFirstPriceV1;
    /**
     * 平台零售续重价_v1 (单位:分)
     */
    private Integer platformAdditionalPriceV1;
    /**
     * 状态 (0-禁用，1-启用)
     */
    private Integer status;
    /**
     * 描述
     */
    private String description;

}