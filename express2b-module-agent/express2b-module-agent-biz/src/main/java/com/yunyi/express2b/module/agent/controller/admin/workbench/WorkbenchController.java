package com.yunyi.express2b.module.agent.controller.admin.workbench;

import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.module.agent.controller.admin.workbench.vo.AgentIncomeTrendVo;
import com.yunyi.express2b.module.agent.controller.admin.workbench.vo.AgentOrderTrendVo;
import com.yunyi.express2b.module.agent.controller.admin.workbench.vo.AgentTotalVo;
import com.yunyi.express2b.module.agent.service.workbench.WorkbenchService;
import com.yunyi.express2b.module.express.api.order.dto.WorkbenchInterfaceDTO;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import static com.yunyi.express2b.framework.common.pojo.CommonResult.success;

/**
 * 工作台页面展示
 *
 * <AUTHOR>
 * @since 17
 * @version 1.0
 * @since 2025/5/27 上午11:27
 */

@RestController
@RequestMapping("/agent/workbench")
public class WorkbenchController {

    @Resource
    private WorkbenchService workbenchService;
    /**
     * 传递代理商ID查询代理商总数,代理商下单的数量,代理商直属下级的数量显示
     * @return
     */
    @GetMapping("/total")
    @Operation(summary = "查询代理商总数,代理商下单的数量,代理商直属下级的数量,代理商间接下属数量")
    @PermitAll
    public CommonResult<AgentTotalVo> searchTotal(@RequestParam("agentId")Long agentId){
        return CommonResult.success(workbenchService.getTotal(agentId));
    }

    /**
     * 订单趋势图显示
     * @return
     */
    @GetMapping("/order-trend")
    @Operation(summary = "订单趋势图显示")
    @PermitAll
    public CommonResult<AgentOrderTrendVo> orderTrendDisplay(@RequestParam("agentId") Long agentId){
        return CommonResult.success(workbenchService.getOrderTrendDisplay(agentId));
    }
    /**
     * 收入趋势图显示
     * @return
     *
     */
    @GetMapping("/income-trend")
    @Operation(summary = "收入趋势图显示")
    @PermitAll
    public CommonResult<AgentIncomeTrendVo> incomeTrendDisplay(@RequestParam("agentId") Long agentId){
        return CommonResult.success(workbenchService.getIncomeTrendDisplay(agentId));
    }


    /**
     *  订单数 订单付款数 已入账订单数（） 带入账订单数（） 销售额 实时对比数据下方的对比昨日
     * @param workbenchInterfaceDTO
     * @return
     */
    @GetMapping("/workbench-interface")
    @Operation(summary = "订单数.订单付款数.已入账订单数. 带入账订单数")
    @PermitAll
    public CommonResult<WorkbenchInterfaceDTO> workbenchInterface(WorkbenchInterfaceDTO workbenchInterfaceDTO) {
        return success(workbenchService.workbenchInterface(workbenchInterfaceDTO));
    }


}