package com.yunyi.express2b.module.agent.dal.dataobject.customtemplaterequest;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.yunyi.express2b.framework.mybatis.core.dataobject.BaseDO;

/**
 * 代理商自定义价格模板申请记录 DO
 *
 * <AUTHOR>
 */
@TableName("agent_custom_template_request")
@KeySequence("agent_custom_template_request_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomTemplateRequestDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 申请代理商ID
     */
    private Long agentId;
    /**
     * 申请的模板参数（JSON格式）
     */
    private String requestedTemplateDetails;
    /**
     * 申请状态（0-待审核, 1-已批准, 2-已拒绝）
     */
    private Integer status;
    /**
     * 若批准，关联到pricing模块中为此申请创建的模板ID
     */
    private Long approvedTemplateId;
    /**
     * 申请时间
     */
    private LocalDateTime requestedAt;
    /**
     * 审核时间
     */
    private LocalDateTime reviewedAt;
    /**
     * 审核备注
     */
    private String reviewerComments;
    /**
     * 申请备注
     */
    private String remarks;
    /**
     * 租户ID
     */
    private Long tenantId;
}