package com.yunyi.express2b.module.agent.service.workbench;

import com.yunyi.express2b.framework.tenant.core.aop.TenantIgnore;
import com.yunyi.express2b.module.agent.controller.admin.workbench.vo.AgentIncomeTrendVo;
import com.yunyi.express2b.module.agent.controller.admin.workbench.vo.AgentOrderTrendVo;
import com.yunyi.express2b.module.agent.controller.admin.workbench.vo.AgentTotalVo;
import com.yunyi.express2b.module.agent.dal.mysql.agentprofile.AgentProfileMapper;
import com.yunyi.express2b.module.agent.dal.mysql.relationship.RelationshipMapper;
import com.yunyi.express2b.module.agent.enums.ConstValue;
import com.yunyi.express2b.module.agent.service.adminworkbench.AdminWorkbenchService;
import com.yunyi.express2b.module.express.api.order.OrderStatusApi;
import com.yunyi.express2b.module.express.api.order.dto.WorkbenchInterfaceDTO;
import com.yunyi.express2b.module.express.api.ordertotal.OrderAgentTotalApi;
import com.yunyi.express2b.module.system.api.user.AdminUserApi;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.format.TextStyle;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * 工作台逻辑实现
 *
 * <AUTHOR>
 * @since 17
 * @version 1.0
 * @since 2025/5/27 下午1:17
 */

@Service
public class WorkbenchServiceImpl implements WorkbenchService{

    @Autowired
    private AgentProfileMapper agentProfileMapper;
    @Resource
    private OrderAgentTotalApi orderAgentTotal;
    @Resource
    private RelationshipMapper relationshipMapper;
    @Resource
    private OrderStatusApi orderStatusApi;
    @Resource
    private AdminUserApi adminUserApi;
    @Resource
    private AdminWorkbenchService adminWorkbenchService;

    /**
     * 获取代理商总览信息
     * @param agentId
     * @return AgentTotalVo
     */
    @Override
    public AgentTotalVo getTotal(Long agentId) {
        String remark = getRemarkById(agentId);
        AgentTotalVo agentTotalVo = new AgentTotalVo();
        agentTotalVo.setRemark(remark);
        if(remark.equals(ConstValue.DEFAULT_REMARK_ADMIN)){
//            管理员查询锁定客户数量
            agentTotalVo.setTotal(adminWorkbenchService.getAdminTotal().getTotal());
//            管理员查询所有订单数据
            agentTotalVo.setOrderAgentCount(adminWorkbenchService.getAdminTotal().getOrderAgentCount());
        }else {
//        代理商查询所有下级的数量(商户)
            Long descendantCount = relationshipMapper.selectByAgentId(agentId);
            agentTotalVo.setTotal(descendantCount);
//            查询当前代理商的所有订单数据
            agentTotalVo.setOrderAgentCount(orderAgentTotal.getAgentOrderCount(agentId));
        }
//        添加直接代理商数量 查询关系表下的第一层级,只要有这个关系,查询这个表的所有父级是这个ID的,把获取到的数量减1
        agentTotalVo.setDirectCount(relationshipMapper.selectByAgentId(agentId));
//        添加代理商的间接下级数 (是三级还是全部有)
        List<Long> descendantIds = relationshipMapper.selectByAncestorId(agentId);
//        遍历所有子集ID,并查询出子集ID下有多少代理商进行累加
        Long  indictirectCount = ConstValue.NUMBER_ZERO;
        for (Long descendantId : descendantIds) {
            indictirectCount += relationshipMapper.selectByAgentId(descendantId);
        }
        agentTotalVo.setIndirectCount(indictirectCount);
//        返回完整的代理商对象
        return agentTotalVo;
    }

    /**
     * 获取订单趋势图显示
     * @param agentId
     * @return
     */
    @Override
    public AgentOrderTrendVo getOrderTrendDisplay(Long agentId) {
        String remark = getRemarkById(agentId);
//        获取当前的日期
        LocalDate now = LocalDate.now();
//        获取一周前的日期
        LocalDate oneWeekAgo = now.minusWeeks(ConstValue.OPERATION_DATE);
//        获取到第八天数据用来计算第7天数据
        oneWeekAgo = oneWeekAgo.minusDays(ConstValue.OPERATION_DATE);
//        获取到这个日期的下一天 用来条件查询
        LocalDate date = oneWeekAgo.plusDays(ConstValue.OPERATION_DATE);
//        获取到日查询日期的下一天 用来条件查询
        LocalDate dateTomarrow = date.plusDays(ConstValue.OPERATION_DATE);
//        转换星期对象
        String dateStr = new String();
//        获取订单数对象
        Long orderCountById = ConstValue.NUMBER_ZERO;
//        创建订单计数集合
        List<Long> orderWeekCount = new ArrayList<>();
//        创建订单计数日期集合
        List<String> weekDate = new ArrayList<>();
//        判断权限是否合规
        if(remark.equals(ConstValue.DEFAULT_REMARK_ADMIN)){
            for (int i = ConstValue.DEFAULT_NUMBER_ZERO; i < ConstValue.DEFAULT_NUMBER_SEVEN; i++) {
//            管理员查询所有的订单量
                orderCountById = adminWorkbenchService.getAdminOrderTrendDisplay(date, dateTomarrow);
//            把查询出来的订单数量存储到集合中
                orderWeekCount.add(orderCountById);
//            将当前日期转换成字符串(星期形式)
                dateStr = date.getDayOfWeek().getDisplayName(TextStyle.FULL, Locale.CHINA);
//            将星期存入集合中
                weekDate.add(dateStr);
                date = date.plusDays(ConstValue.OPERATION_DATE);
                dateTomarrow = date.plusDays(ConstValue.OPERATION_DATE);
            }
        }else{
        for (int i = ConstValue.DEFAULT_NUMBER_ZERO; i < ConstValue.DEFAULT_NUMBER_SEVEN; i++) {
//            把日期和当前代理商ID传入获取到当天的订单量
            orderCountById = orderAgentTotal.getOrderCount(agentId,date,dateTomarrow);
//            把查询出来的订单数量存储到集合中
            orderWeekCount.add(orderCountById);
//            将当前日期转换成字符串(星期形式)
            dateStr = date.getDayOfWeek().getDisplayName(TextStyle.FULL, Locale.CHINA);
//            将星期存入集合中
            weekDate.add(dateStr);
            date = date.plusDays(ConstValue.OPERATION_DATE);
            dateTomarrow = date.plusDays(ConstValue.OPERATION_DATE);
        }
    }
//       将存完的值放入当前对象返回给前端
        AgentOrderTrendVo agentOrderTrendVo = new AgentOrderTrendVo();
        agentOrderTrendVo.setOrderCount(orderWeekCount);
        agentOrderTrendVo.setOrderDate(weekDate);
        return agentOrderTrendVo;
    }

    /**
     * 获取收入趋势图显示
     * @param agentId
     * @return
     */
    @Override
    public AgentIncomeTrendVo getIncomeTrendDisplay(Long agentId) {
        String remark = getRemarkById(agentId);
//        获取当前的日期
        LocalDate now = LocalDate.now();
//        获取一周前的日期
        LocalDate oneWeekAgo = now.minusWeeks(ConstValue.OPERATION_DATE);
//        显示表展示的是定向数据所以需要将天数再移一天
        oneWeekAgo = oneWeekAgo.minusDays(ConstValue.OPERATION_DATE);
//        创建收入总和集合
        List<Long> incomeTotal = new ArrayList<>();
//        创建收入计数日期集合
        List<String> weekDate = new ArrayList<>();
//        星期接收对象
        String dateStr = new String();
//        日期接收对象
        LocalDate date = oneWeekAgo.plusDays(ConstValue.OPERATION_DATE);
//            获取到这个日期的下一天 用来条件查询
        LocalDate dateTomarrow = date.plusDays(ConstValue.OPERATION_DATE);
//        初始化总和值
        Long sum = ConstValue.NUMBER_ZERO;
//        判断权限管理操作
        if(remark.equals(ConstValue.DEFAULT_REMARK_ADMIN)){
            for (int i = ConstValue.DEFAULT_NUMBER_ZERO; i < ConstValue.DEFAULT_NUMBER_SEVEN; i++) {
//            查询出本日收入总和 (暂时是订单实收金额)
                sum = adminWorkbenchService.getAdminIncomeTrendDisplay(date, dateTomarrow);
//            处理数据库数据向前端展示
                sum = (sum != null ? sum : ConstValue.NUMBER_ZERO) / ConstValue.CALCULATE_HUNDRED;
//            将收入总和存入集合中
                incomeTotal.add(sum);
//            将当前日期转换成字符串(星期形式)
                dateStr = date.getDayOfWeek().getDisplayName(TextStyle.FULL, Locale.CHINA);
//            将星期存入集合中
                weekDate.add(dateStr);
//            修改时间下标
                date = date.plusDays(ConstValue.OPERATION_DATE);
                dateTomarrow = date.plusDays(ConstValue.OPERATION_DATE);
            }
        }else {
            for (int i = ConstValue.DEFAULT_NUMBER_ZERO; i < ConstValue.DEFAULT_NUMBER_SEVEN; i++) {
//            查询出本日收入总和 (暂时是订单实收金额)
                sum = orderAgentTotal.getOrderIncome(agentId, date, dateTomarrow);
//            处理数据库数据向前端展示
                 sum = (sum != null ? sum : ConstValue.NUMBER_ZERO) / ConstValue.CALCULATE_HUNDRED;
//            将收入总和存入集合中
                incomeTotal.add(sum);
//            将当前日期转换成字符串(星期形式)
                dateStr = date.getDayOfWeek().getDisplayName(TextStyle.FULL, Locale.CHINA);
//            将星期存入集合中
                weekDate.add(dateStr);
//            修改时间下标
                date = date.plusDays(ConstValue.OPERATION_DATE);
                dateTomarrow = date.plusDays(ConstValue.OPERATION_DATE);
            }
        }
            AgentIncomeTrendVo agentIncomeTrendVo = new AgentIncomeTrendVo();
            agentIncomeTrendVo.setIncomeTotal(incomeTotal);
            agentIncomeTrendVo.setIncomeDate(weekDate);
            return agentIncomeTrendVo;
    }

    @Override
    public WorkbenchInterfaceDTO workbenchInterface(WorkbenchInterfaceDTO workbenchInterfaceDTO) {
        return orderStatusApi.workbenchInterface(workbenchInterfaceDTO);
    }

    /**
     * 根据代理商ID获取权限备注
     * @param agentId
     * @return
     */
    public String getRemarkById(Long agentId) {
        Long merberId = agentProfileMapper.selectByAgentId(agentId);
//        获取到当前的权限备注
        return adminUserApi.getRemarkById(merberId);
    }
}