package com.yunyi.express2b.module.agent.dal.dataobject.relationship;

import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import com.yunyi.express2b.framework.mybatis.core.dataobject.BaseDO;

/**
 * 代理商团队关系 DO
 *
 * <AUTHOR>
 */
@TableName("agent_relationship")
@KeySequence("agent_relationship_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RelationshipDO extends BaseDO {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 上级代理商ID
     */
    private Long ancestorId;
    /**
     * 下级代理商ID
     */
    private Long descendantId;
    /**
     *
     * 层级深度
     */
    private Integer depth;

    /**
     * 租户id
     */
    private Long tenantId;
}