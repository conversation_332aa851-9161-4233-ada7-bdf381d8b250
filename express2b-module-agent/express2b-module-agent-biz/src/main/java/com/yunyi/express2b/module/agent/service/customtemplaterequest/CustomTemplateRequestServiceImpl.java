package com.yunyi.express2b.module.agent.service.customtemplaterequest;

import com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.util.json.JsonUtils;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.framework.security.core.util.SecurityFrameworkUtils;
import com.yunyi.express2b.module.agent.controller.admin.customtemplaterequest.vo.*;
import com.yunyi.express2b.module.agent.dal.dataobject.agentprofile.AgentProfileDO;
import com.yunyi.express2b.module.agent.dal.dataobject.customtemplaterequest.CustomTemplateRequestDO;
import com.yunyi.express2b.module.agent.dal.mysql.agentprofile.AgentProfileMapper;
import com.yunyi.express2b.module.agent.dal.mysql.customtemplaterequest.CustomTemplateRequestMapper;
import com.yunyi.express2b.module.agent.enums.ConstValue;
import com.yunyi.express2b.module.agent.service.admintemplaterequest.AdminTemplateRequestService;
import com.yunyi.express2b.module.pricing.api.NewTemplateApi;
import com.yunyi.express2b.module.pricing.api.dto.NewTemplateRelationDTO;
import com.yunyi.express2b.module.pricing.api.vo.NewTemplateSaveReqVO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.function.DoubleConsumer;
import java.util.function.DoubleSupplier;

import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.module.agent.enums.ErrorCodeConstants.*;

/**
 * 代理商自定义价格模板申请记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class CustomTemplateRequestServiceImpl implements CustomTemplateRequestService {

    @Resource
    private CustomTemplateRequestMapper customTemplateRequestMapper;

    @Resource
    private AgentProfileMapper agentProfileMapper;

    @Resource
    private NewTemplateApi newTemplateApi;

    @Resource
    private AdminTemplateRequestService adminTemplateRequestService;

    @Override
    public Long createCustomTemplateRequest(CustomTemplateRequestSaveReqVO createReqVO) {
        // 插入
        CustomTemplateRequestDO customTemplateRequest = BeanUtils.toBean(createReqVO, CustomTemplateRequestDO.class);
        customTemplateRequestMapper.insert(customTemplateRequest);
        // 返回
        return customTemplateRequest.getId();
    }

    @Override
    public void updateCustomTemplateRequest(CustomTemplateRequestSaveReqVO updateReqVO) {
        // 校验存在
        validateCustomTemplateRequestExists(updateReqVO.getId());
        // 更新
        CustomTemplateRequestDO updateObj = BeanUtils.toBean(updateReqVO, CustomTemplateRequestDO.class);
        customTemplateRequestMapper.updateById(updateObj);
    }

    @Override
    public void deleteCustomTemplateRequest(Long id) {
        // 校验存在
        validateCustomTemplateRequestExists(id);
        // 删除
        customTemplateRequestMapper.deleteById(id);
    }

    private void validateCustomTemplateRequestExists(Long id) {
        if (customTemplateRequestMapper.selectById(id) == null) {
            throw exception(CUSTOM_TEMPLATE_REQUEST_NOT_EXISTS);
        }
    }

    @Override
    public CustomTemplateRequestDO getCustomTemplateRequest(Long id) {
        return customTemplateRequestMapper.selectById(id);
    }

    @Override
    public PageResult<CustomTemplateRequestDO> getCustomTemplateRequestPage(CustomTemplateRequestPageReqVO pageReqVO) {
        return customTemplateRequestMapper.selectPage(pageReqVO);
    }

    /**
     * 将自定义定价模版插入到数据表中
     * @param customTemplateRequestRespVO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer insertTemplateSave(CustomTemplateRequestRespVO customTemplateRequestRespVO) {
        CustomTemplateRequestDO customTemplateRequestDO = BeanUtils.toBean(customTemplateRequestRespVO, CustomTemplateRequestDO.class);
        customTemplateRequestDO.setStatus(ConstValue.TENANT_DEFAULT_STATUS);
//        设置插入时的更新和创建者ID值
        AgentProfileDO agentProfileDO = agentProfileMapper.getAgentIdByMemberId(SecurityFrameworkUtils.getLoginUserId());
        customTemplateRequestDO.setAgentId(agentProfileDO.getId());
        customTemplateRequestDO.setCreator(agentProfileDO.getId().toString());
        customTemplateRequestDO.setUpdater(agentProfileDO.getId().toString());
        customTemplateRequestDO.setRequestedAt(LocalDateTime.now());
        return  customTemplateRequestMapper.insert(customTemplateRequestDO);
    }

    /**
     * 审核代理商自定义模版申请
     * @return
     */
    @Override
    public boolean updateTemplate(ReviewTemplateVo reviewTemplateVo) {
        return adminTemplateRequestService.updateTemplate(reviewTemplateVo);
    }

    /**
     * 查询未审核模版
     * @return
     */
    @Override
    public List<CustomTemplateEchoVO> getAgentTemplateRequest(String remark) {
        return adminTemplateRequestService.getAgentTemplateRequest(remark);
    }

    /**
     * 查询回显的代理商自定义模版申请状态
     * @param agentId
     * @return
     */
    @Override
    public List<CustomTemplateRequestRespVO> getQueryEcho(Long agentId) {
//        这里要把查询出来的自定义模版中的模版名称转换出来
        List<CustomTemplateRequestDO> customTemplateRequestDOS = customTemplateRequestMapper.selectQueryEcho(agentId);
//        这个是可以拿到数据为了拿到模版ID值
        List<CustomTemplateRequestRespVO> customTemplateRequestTrans = new ArrayList<>();
        CustomTemplateRequestRespVO customTemplateRequestRespVO = new CustomTemplateRequestRespVO();
        for (CustomTemplateRequestDO customTemplateRequestDO : customTemplateRequestDOS) {
//         第一步是把DO中转换的json值拿到并且存入本次的VO中
            String requestedTemplateDetails = customTemplateRequestDO.getRequestedTemplateDetails();
            if  (requestedTemplateDetails == null) {
                return null;
            }
//            转换当前模版名称,并把这个模版名称给这个VO对象中
            String templateName = JsonUtils.parseObject(requestedTemplateDetails, NewCustomTemplateVO.class).getTemplateName();
            customTemplateRequestRespVO = BeanUtils.toBean(customTemplateRequestDO, CustomTemplateRequestRespVO.class);
            customTemplateRequestRespVO.setTemplateName(templateName);
            customTemplateRequestTrans.add(customTemplateRequestRespVO);
        }
        return customTemplateRequestTrans;
    }

    /**
     * 代理商重新申请模版
     * @param
     * @param customTemplateRequestSaveReqVO
     * @return
     */
    @Override
    public boolean renewTemplate(CustomTemplateRequestSaveReqVO customTemplateRequestSaveReqVO) {
        if  (customTemplateRequestSaveReqVO == null) {
            throw ServiceExceptionUtil.exception(DAILY_TEMPLATE_NOT_EXISTS);
        }
        CustomTemplateRequestDO customTemplateRequestDO = BeanUtils.toBean(customTemplateRequestSaveReqVO, CustomTemplateRequestDO.class);
//        修改当前时间操作
        customTemplateRequestDO.setRequestedAt(LocalDateTime.now());

//        修改当前申请状态
        customTemplateRequestDO.setStatus(ConstValue.TENANT_DEFAULT_STATUS);

//       获取到当前申请模版ID
        Long id = customTemplateRequestDO.getId();

//        修改当前批准关联的模版ID
        customTemplateRequestDO.setApprovedTemplateId(null);

//        查询出代理商id
        Long agentId = customTemplateRequestMapper.selectTemplateIdById(id);
//        将代理商ID修改为当前修改者ID
        try{
            customTemplateRequestDO.setUpdater(agentId.toString());
        }catch (Exception e){
            throw ServiceExceptionUtil.exception(DAILY_TEMPLATE_NOT_EXISTS);
        }
//        检验是否更新成功
        Integer renewCount = customTemplateRequestMapper.updateByTemplateId(id, customTemplateRequestDO);
        if (renewCount == null || renewCount == ConstValue.DEFAULT_COUNT) {
            throw ServiceExceptionUtil.exception(CUSTOM_TEMPLATE_REQUEST_NOT_EXISTS);
        }
        return true;
    }

    /**
     * 获取定价模版回显
     * @param agentId
     * @return
     */
    @Override
    public List<NewCustomTemplateVO> getTemplateEcho(Long agentId) {
//        当前代理商的所有定价模版
        List<NewTemplateSaveReqVO> templateRelationship = newTemplateApi.getTemplateRelationship(agentId);
        return BeanUtils.toBean(templateRelationship, NewCustomTemplateVO.class);
    }

    /**
     * 通过定价模版ID修改代理商默认模版ID
     * @param id
     * @return
     */
    @Override
    public boolean reviseDefaultTemplate(Long id,Long agentId) {
//       获取当前代理商默认模版ID
        Long customerPricingTemplateId = agentProfileMapper.selectApprovalAgentId(agentId);
        if (customerPricingTemplateId != null) {
//          修改关系表中是否默认选项(这个就是把模版关系修改为不默认)
            newTemplateApi.updateTemplateRelationshipDefault(customerPricingTemplateId,agentId);
        }else {
            return false;
        }
//        通过模版ID修改出当前的代理商ID
        int count = agentProfileMapper.updateCustomPricingTemplateId(agentId, id);
//        修改模版关系表中的默认ID
        Long secondCount = newTemplateApi.updateTemplate(id, agentId);
        if (count == ConstValue.TENANT_DEFAULT_STATUS) {
            return false;
        }
        if (secondCount.equals(ConstValue.NUMBER_ZERO)) {
            return false;
        }
        return true;
    }
    /**
     * 回显新定价模版
     * @return
     */
    @Override
    public List<NewTemplateRelationDTO> getNewTemplateList() {
        AgentProfileDO agentProfileDO = agentProfileMapper.getAgentIdByMemberId(SecurityFrameworkUtils.getLoginUserId());

        List<NewTemplateRelationDTO> templateByAgentID = newTemplateApi.getTemplateByAgentID(agentProfileDO.getId());
//        返回给前端的所有数据
        List<NewTemplateRelationDTO> finallyNewTemplateRelationDTO = new ArrayList<>();
        for (NewTemplateRelationDTO newTemplateRelationDTO : templateByAgentID) {
            // 处理V1版本的价格字段
            divideFieldBy100(newTemplateRelationDTO::getPlatformFirstPriceV1, newTemplateRelationDTO::setPlatformFirstPriceV1);
            divideFieldBy100(newTemplateRelationDTO::getPlatformAdditionalPriceV1, newTemplateRelationDTO::setPlatformAdditionalPriceV1);

            // 处理V2版本的价格字段
            divideFieldBy100(newTemplateRelationDTO::getPlatformFirstPriceV2, newTemplateRelationDTO::setPlatformFirstPriceV2);
            divideFieldBy100(newTemplateRelationDTO::getPlatformAdditionalPriceV2, newTemplateRelationDTO::setPlatformAdditionalPriceV2);

            // 处理V3版本的价格字段
            divideFieldBy100(newTemplateRelationDTO::getPlatformFirstPriceV3, newTemplateRelationDTO::setPlatformFirstPriceV3);
            divideFieldBy100(newTemplateRelationDTO::getPlatformAdditionalPriceV3, newTemplateRelationDTO::setPlatformAdditionalPriceV3);
            finallyNewTemplateRelationDTO.add(newTemplateRelationDTO);
        }
        return finallyNewTemplateRelationDTO;
    }

    /**
     * 审核历史模版追溯
     * @return
     */
    @Override
    public List<CustomTemplateReviewVO> getReviewTemplateHistory(String remark) {
        return adminTemplateRequestService.getReviewTemplateHistory(remark);
    }

    /**
     * 抽取出转换分和元的函数方法
     * @param getter
     * @param setter
     */
    private void divideFieldBy100(DoubleSupplier getter, DoubleConsumer setter) {
        if (getter == null) {
            throw ServiceExceptionUtil.exception(PRICING_RULE_NOT_EXISTS);
        }
        Double value = getter.getAsDouble();
        setter.accept(value / ConstValue.CALCULATE_HUNDRED);
    }


}