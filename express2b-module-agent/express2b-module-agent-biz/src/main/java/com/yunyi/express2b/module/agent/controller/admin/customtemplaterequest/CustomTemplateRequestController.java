package com.yunyi.express2b.module.agent.controller.admin.customtemplaterequest;

import com.yunyi.express2b.framework.apilog.core.annotation.ApiAccessLog;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.pojo.PageParam;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.framework.excel.core.util.ExcelUtils;
import com.yunyi.express2b.module.agent.controller.admin.customtemplaterequest.vo.*;
import com.yunyi.express2b.module.agent.dal.dataobject.customtemplaterequest.CustomTemplateRequestDO;
import com.yunyi.express2b.module.agent.service.customtemplaterequest.CustomTemplateRequestService;
import com.yunyi.express2b.module.pricing.api.dto.NewTemplateRelationDTO;
import com.yunyi.express2b.module.pricing.api.dto.TemplateRelationDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static com.yunyi.express2b.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.yunyi.express2b.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 代理商自定义价格模板申请记录")
@RestController
@RequestMapping("/agent/custom-template-request")
@Validated
public class CustomTemplateRequestController {

    @Resource
    private CustomTemplateRequestService customTemplateRequestService;

    @PostMapping("/create")
    @Operation(summary = "创建代理商自定义价格模板申请记录")
    @PreAuthorize("@ss.hasPermission('agent:custom-template-request:create')")
    public CommonResult<Long> createCustomTemplateRequest(@Valid @RequestBody CustomTemplateRequestSaveReqVO createReqVO) {
        return success(customTemplateRequestService.createCustomTemplateRequest(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新代理商自定义价格模板申请记录")
    @PreAuthorize("@ss.hasPermission('agent:custom-template-request:update')")
    public CommonResult<Boolean> updateCustomTemplateRequest(@Valid @RequestBody CustomTemplateRequestSaveReqVO updateReqVO) {
        customTemplateRequestService.updateCustomTemplateRequest(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除代理商自定义价格模板申请记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('agent:custom-template-request:delete')")
    public CommonResult<Boolean> deleteCustomTemplateRequest(@RequestParam("id") Long id) {
        customTemplateRequestService.deleteCustomTemplateRequest(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得代理商自定义价格模板申请记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('agent:custom-template-request:query')")
    public CommonResult<CustomTemplateRequestRespVO> getCustomTemplateRequest(@RequestParam("id") Long id) {
        CustomTemplateRequestDO customTemplateRequest = customTemplateRequestService.getCustomTemplateRequest(id);
        return success(BeanUtils.toBean(customTemplateRequest, CustomTemplateRequestRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得代理商自定义价格模板申请记录分页")
    @PreAuthorize("@ss.hasPermission('agent:custom-template-request:query')")
    public CommonResult<PageResult<CustomTemplateRequestRespVO>> getCustomTemplateRequestPage(@Valid CustomTemplateRequestPageReqVO pageReqVO) {
        PageResult<CustomTemplateRequestDO> pageResult = customTemplateRequestService.getCustomTemplateRequestPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, CustomTemplateRequestRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出代理商自定义价格模板申请记录 Excel")
    @PreAuthorize("@ss.hasPermission('agent:custom-template-request:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportCustomTemplateRequestExcel(@Valid CustomTemplateRequestPageReqVO pageReqVO,
                                                 HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<CustomTemplateRequestDO> list = customTemplateRequestService.getCustomTemplateRequestPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "代理商自定义价格模板申请记录.xls", "数据", CustomTemplateRequestRespVO.class,
                BeanUtils.toBean(list, CustomTemplateRequestRespVO.class));
    }


    /**
     * 代理商插入自定义价格模版
     * @param templateSaveReqVO
     * @return
     */
    @PostMapping("/insert")
    @Operation(summary = "插入自定义定价模板表")
    @PermitAll
    public CommonResult<Integer> createTemplateSave(@Valid @RequestBody CustomTemplateRequestRespVO  templateSaveReqVO) {
        return success(customTemplateRequestService.insertTemplateSave(templateSaveReqVO));
    }

    /**
     * 管理员审核代理商模版显示接口
     * @param
     * @param
     * @return
     */
    @GetMapping("/query-template")
    @Operation(summary = "查询未审核申请模板")
    @PermitAll
    public CommonResult<List<CustomTemplateEchoVO>> getAgentTemplateRequest(String remark) {
        return success(customTemplateRequestService.getAgentTemplateRequest(remark));
    }
    /**
     * 管理员修改当前申请模版状态
     * 传递自定义模板ID和修改后的状态
     * id是自定义模版的ID
     * @param reviewTemplateVo
     * @param
     * @return
     */
    @PutMapping("/update-template")
    @Operation(summary = "审核代理商自定义模板数据")
    @PermitAll
    public CommonResult<String> updateTemplate(@RequestBody ReviewTemplateVo reviewTemplateVo){
        return customTemplateRequestService.updateTemplate(reviewTemplateVo) ? success("审核成功") : success("审核失败");
    }

    /**
     * 根据代理商ID获取代理商自定义申请表的申请状态(查看是否审核通过)
     * @param agentId
     * @return
     */
    @GetMapping("/select/query")
    @Operation(summary = "查询代理商申请模版状态")
    @PermitAll
    public CommonResult<List<CustomTemplateRequestRespVO>> getTemplateById(@RequestParam(value = "agentId") Long agentId){
        return success(customTemplateRequestService.getQueryEcho(agentId));
    }

    /**
     * 代理商对自定义申请模版修改后提交 (在查询当前申请模版状态的下面增加重新提交按钮)
     * 传递当前模版ID和修改后的模版参数
     * @param
     * @return
     */
    @PutMapping("/renew-template")
    @Operation(summary = "代理商对自定义申请模版修改后提交")
    @PermitAll
    public CommonResult<String> replaceAgentTemplate(@RequestBody CustomTemplateRequestSaveReqVO customTemplateRequestSaveReqVO){
        return customTemplateRequestService.renewTemplate(customTemplateRequestSaveReqVO)?success("提交成功"):success("提交失败");
    }

    /**
     * 查询代理商可用定价模版
     * @param agentId
     * @return
     */
    @GetMapping("/query-template-echo")
    @Operation(summary = "代理商可用定价模版")
    @PermitAll
    public CommonResult<List<NewCustomTemplateVO>> getTemplateEcho(@RequestParam(value = "agentId") Long agentId){
        List<NewCustomTemplateVO> templateEcho = customTemplateRequestService.getTemplateEcho(agentId);
        return CommonResult.success(templateEcho);
    }

    /**
     * 代理商可以在可用定价模版中选择一个作为使用模版
     * 这里要处理put请求前面没定义url上的ID值，获取到模板id之后还要获取到当前代理商ID,要修改模版关系表中的默认模版数据
     * @param id
     * @return
     */
    @GetMapping("/update-defaults")
    @Operation(summary = "代理商选择默认定价模版")
    @PermitAll
    public CommonResult<String> updateDefaultTemplate(@RequestParam("id") Long id,@RequestParam(value = "agentId") Long agentId){
        return customTemplateRequestService.reviseDefaultTemplate(id,agentId)?success("修改成功"):success("修改失败");
    }

    /**
     * 查询当前代理商申请通过的价格模版
     * @return
     */
    @GetMapping("/get-newtemplate")
    @Operation(summary = "查询当前代理商申请通过的价格模版")
    @PermitAll
    public CommonResult<List<NewTemplateRelationDTO>> getNewTemplate(){
        return CommonResult.success(customTemplateRequestService.getNewTemplateList());
    }

    /**
     * 查询出已经审核过的模版
     * @return
     */
    @GetMapping("/get-newtemplate-query")
    @Operation(summary = "查询审核过的模版样式")
    @PermitAll
    public CommonResult<List<CustomTemplateReviewVO>> getNewTemplateQuery(String remark){
        return CommonResult.success(customTemplateRequestService.getReviewTemplateHistory(remark));
    }
}
