package com.yunyi.express2b.module.agent.service.agentprofile;

import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.module.agent.api.dto.AgentActivityDTO;
import com.yunyi.express2b.module.agent.api.dto.RegisterDTO;
import com.yunyi.express2b.module.agent.controller.admin.agentprofile.vo.AgentProfileCountVO;
import com.yunyi.express2b.module.agent.controller.admin.agentprofile.vo.AgentProfilePageReqVO;
import com.yunyi.express2b.module.agent.controller.admin.agentprofile.vo.AgentProfileSaveReqVO;
import com.yunyi.express2b.module.agent.enums.RegisterCode;
import com.yunyi.express2b.module.pricing.api.dto.NewTemplateRelationDTO;
import com.yunyi.express2b.module.pricing.api.dto.TemplateApiDTO;
import com.yunyi.express2b.module.agent.controller.admin.agentprofile.vo.*;
import com.yunyi.express2b.module.agent.dal.dataobject.agentprofile.AgentProfileDO;
import com.yunyi.express2b.module.agent.dal.dataobject.agentprofile.OrderCountsDO;
import com.yunyi.express2b.module.agent.dal.dataobject.agentprofile.OrderInfoDO;
import jakarta.validation.Valid;

import java.io.File;
import java.util.List;

/**
 * 代理商档案 Service 接口
 *
 * <AUTHOR>
 */
public interface AgentProfileService {

    /**
     * 创建代理商档案
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createProfile(@Valid AgentProfileSaveReqVO createReqVO);

    /**
     * 更新代理商档案
     *
     * @param updateReqVO 更新信息
     */
    void updateProfile(@Valid AgentProfileSaveReqVO updateReqVO);

    /**
     * 删除代理商档案
     *
     * @param id 编号
     */
    void deleteProfile(Long id);

    /**
     * 获得代理商档案
     *
     * @param id 编号
     * @return 代理商档案
     */
    AgentProfileDO getProfile(Long id);

    /**
     * 获得代理商档案分页
     *
     * @param pageReqVO 分页查询
     * @return 代理商档案分页
     */
    PageResult<AgentProfileDO> getProfilePage(AgentProfilePageReqVO pageReqVO);

    /**
     * 根据二维码/链接邀请信息更改代理商档案信息
     * @param agentProfileDO
     */
    void editAgentProfile(AgentProfileDO agentProfileDO);

    /**
     * 通过父团队信息查询所有子团队成员信息
     * @return
     */
    List<AgentProfileDO> getTeam();

    /**
     * 通过member_id查询代理商档案
     * @return
     */
    RegisterDTO getProfileByMemberId(Long memberId,Long templateId);


    /**
     * 根据用户关联中心id注册
     * @param registerDTO
     */
    RegisterCode registerProfile(RegisterDTO registerDTO);

    /**
     * 保存图片url
     * @param file
     */
    void saveImageUrl(File file) throws Exception;

    /**
     * 根据当前的代理商id统计订单数量
     * @return
     */
    OrderCountsDO selectOrderCount();

    /**
     * 根据当前的代理商id查询订单列表
     * @return
     */
    PageResult<OrderInfoDO> selectOrderList(OrderRequestVO orderRequestVO);
    /**
     * 获取当前代理商团队信息
     * @return
     */
    PageResult<AgentProfileDO> getMyTeam(String type,String str);

    /**
     * 获取团队人数，直接下级总数，间接下级总数
     * @return
     */
    AgentProfileCountVO getTeamCount();

    /**
     * 为直接下级选择模板
     * @param agentId
     * @param templateId
     */
    void selectDirectTemplate(Long agentId, Long templateId);

    /**
     * 根据memberId查询模板
     * @param memberId
     */
    PageResult<TemplateApiDTO> selectTemplate(Long memberId);

    /**
     * 获取团队累计结算总额
     * @param memberId
     * @return
     */
    Long getamountSUM(Long memberId);

    /**
     * 获取代理商审核通过的价格模板，并根据传入的类型分类
     * @param templateType
     * @return
     */
    List<NewTemplateRelationDTO> getTypeNewTemplate(Long templateType);

    /**
     * 统计所有代理商及其团队(直接下级)活跃度信息
     * @return
     */
    List<AgentActivityDTO> getAllAgentActivity();

    /**
     * 获取某代理商的活跃度数据
     * @param agentId
     * @return
     */
    AgentActivityVO getAgentActivity(Long agentId);



    /**
     * 创建url
     * @param templateId
     * @return
     */
    RegisterDTO createUrl(String key,Long templateId);
}