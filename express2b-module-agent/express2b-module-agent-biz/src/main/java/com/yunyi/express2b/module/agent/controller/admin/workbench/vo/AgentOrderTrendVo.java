package com.yunyi.express2b.module.agent.controller.admin.workbench.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 代理商订单趋势
 *
 * <AUTHOR>
 * @since 17
 * @version 1.0
 * @since 2025/5/27 下午1:37
 */
@Schema(description = "代理商订单量趋势显示")
@Data
public class AgentOrderTrendVo {
    @Schema(description = "订单数量")
    List<Long> orderCount;

    @Schema(description = "日期")
    List<String> orderDate;
}