package com.yunyi.express2b.module.agent.service.relationship;

import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.module.agent.controller.admin.relationship.vo.RelationshipPageReqVO;
import com.yunyi.express2b.module.agent.controller.admin.relationship.vo.RelationshipSaveReqVO;
import com.yunyi.express2b.module.agent.dal.dataobject.agentprofile.AgentProfileDO;
import com.yunyi.express2b.module.agent.dal.dataobject.relationship.RelationshipDO;
import jakarta.validation.Valid;

/**
 * 代理商团队关系 Service 接口
 *
 * <AUTHOR>
 */
public interface RelationshipService {

    /**
     * 创建代理商团队关系
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createRelationship(@Valid RelationshipSaveReqVO createReqVO);

    /**
     * 更新代理商团队关系
     *
     * @param updateReqVO 更新信息
     */
    void updateRelationship(@Valid RelationshipSaveReqVO updateReqVO);

    /**
     * 删除代理商团队关系
     *
     * @param id 编号
     */
    void deleteRelationship(Long id);

    /**
     * 获得代理商团队关系
     *
     * @param id 编号
     * @return 代理商团队关系
     */
    RelationshipDO getRelationship(Long id);
    /**
     * 向代理商表中插入数据
     *
     * @param agentProfileRespDO
     * @return
     */
    Long insertRelationshipById(AgentProfileDO agentProfileRespDO);

    /**
     * 获得代理商团队关系分页
     *
     * @param pageReqVO 分页查询
     * @return 代理商团队关系分页
     */
    PageResult<RelationshipDO> getRelationshipPage(RelationshipPageReqVO pageReqVO);

    /**
     * 根据代理商id查询租户id
     * @param id
     * @return
     */
    Long getAgentTenantId(Long id);

}