package com.yunyi.express2b.module.agent.service.relationship;

import com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.framework.tenant.core.aop.TenantIgnore;
import com.yunyi.express2b.module.agent.controller.admin.relationship.vo.RelationshipPageReqVO;
import com.yunyi.express2b.module.agent.controller.admin.relationship.vo.RelationshipSaveReqVO;
import com.yunyi.express2b.module.agent.dal.dataobject.agentprofile.AgentProfileDO;
import com.yunyi.express2b.module.agent.dal.dataobject.relationship.RelationshipDO;
import com.yunyi.express2b.module.agent.dal.mysql.agentprofile.AgentProfileMapper;
import com.yunyi.express2b.module.agent.dal.mysql.relationship.RelationshipMapper;
import com.yunyi.express2b.module.agent.enums.ConstValue;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.module.agent.enums.ErrorCodeConstants.RELATIONSHIP_NOT_EXISTS;

/**
 * 代理商团队关系 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class RelationshipServiceImpl implements RelationshipService {
    @Resource
    private RelationshipMapper relationshipMapper;
    @Resource
    private AgentProfileMapper agentProfileMapper;

    @Override
    public Long createRelationship(RelationshipSaveReqVO createReqVO) {
        // 插入
        RelationshipDO relationship = BeanUtils.toBean(createReqVO, RelationshipDO.class);
        relationshipMapper.insert(relationship);
        // 返回
        return relationship.getId();
    }

    @Override
    public void updateRelationship(RelationshipSaveReqVO updateReqVO) {
        // 校验存在
        validateRelationshipExists(updateReqVO.getId());
        // 更新
        RelationshipDO updateObj = BeanUtils.toBean(updateReqVO, RelationshipDO.class);
        relationshipMapper.updateById(updateObj);
    }

    @Override
    public void deleteRelationship(Long id) {
        // 校验存在
        validateRelationshipExists(id);
        // 删除
        relationshipMapper.deleteById(id);
    }

    private void validateRelationshipExists(Long id) {
        if (relationshipMapper.selectById(id) == null) {
            throw exception(RELATIONSHIP_NOT_EXISTS);
        }
    }

    @Override
    public RelationshipDO getRelationship(Long id) {
        return relationshipMapper.selectById(id);
    }

    /**
     * 判断代理商关系并插入到表中
     * 获取到代理商的信息,找到他的层级把层级存入代理商关系白哦
     * @param agentProfileRespDO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertRelationshipById(AgentProfileDO agentProfileRespDO) {
//          代理商关系表中没有代理商的信息，存入代理商信息并将深度设置为0
        if (relationshipMapper.selectByDesAgentId(agentProfileRespDO.getId()).isEmpty()) {
//          单独定义对象，使用同一个对象的时候id不会被修改(因为实体类中设置主键自增，一个对象就只有一个主键)
            RelationshipDO relationshipDO = new RelationshipDO();
            relationshipDO.setAncestorId(agentProfileRespDO.getId());
            relationshipDO.setDescendantId(agentProfileRespDO.getId());
            relationshipDO.setDepth(ConstValue.TENANT_DEFAULT_STATUS);
            relationshipMapper.insert(relationshipDO);
        }
//      获取推荐代理商ID
        Long referrerAgentId = agentProfileRespDO.getReferrerAgentId();
        Integer deth = ConstValue.DEFAULT_NUMBER_ONE;
//      判断推荐代理商只要不是空就将值存入
        while (referrerAgentId != null) {
//          推荐代理商的推荐代理商id为null，就直接将深度设置为1
            RelationshipDO relationshipDO = new RelationshipDO();
            relationshipDO.setAncestorId(referrerAgentId);
//          根据推荐代理商查找租户ID,并把租户ID存入当前对象中
            relationshipDO.setTenantId(agentProfileMapper.selectTenantId(referrerAgentId));
            relationshipDO.setDescendantId(agentProfileRespDO.getId());
            relationshipDO.setDepth(deth);
            relationshipMapper.insert(relationshipDO);
//          通过推荐代理商ID查询推荐代理商信息
            AgentProfileDO agentProfileDO = agentProfileMapper.selectReferrerAgentById(referrerAgentId);
//          将推荐代理商ID修改为他的推荐代理商ID,并把深度加1
            referrerAgentId = agentProfileDO.getReferrerAgentId();
            deth++;
        }
    return agentProfileRespDO.getId();
    }
    @Override
    public PageResult<RelationshipDO> getRelationshipPage(RelationshipPageReqVO pageReqVO) {
        return relationshipMapper.selectPage(pageReqVO);
    }

    /**
     * 根据代理商ID获取代理商的租户ID
     * @param id
     * @return
     */
    @Override
    @TenantIgnore
    public Long getAgentTenantId(Long id) {
        return agentProfileMapper.selectTenantId(id);
    }
}