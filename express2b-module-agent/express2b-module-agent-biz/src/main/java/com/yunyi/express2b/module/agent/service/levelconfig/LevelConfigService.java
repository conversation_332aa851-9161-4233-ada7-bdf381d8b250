package com.yunyi.express2b.module.agent.service.levelconfig;

import java.util.*;

import com.yunyi.express2b.module.agent.api.dto.UpLevelRuleDTO;
import jakarta.validation.*;
import com.yunyi.express2b.module.agent.controller.admin.levelconfig.vo.*;
import com.yunyi.express2b.module.agent.dal.dataobject.levelconfig.LevelConfigDO;
import com.yunyi.express2b.framework.common.pojo.PageResult;


/**
 * 代理商等级配置 Service 接口
 *
 * <AUTHOR>
 */
public interface LevelConfigService {

    /**
     * 创建代理商等级配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createLevelConfig(@Valid LevelConfigSaveReqVO createReqVO);

    /**
     * 更新代理商等级配置
     *
     * @param updateReqVO 更新信息
     */
    void updateLevelConfig(@Valid LevelConfigSaveReqVO updateReqVO);

    /**
     * 删除代理商等级配置
     *
     * @param id 编号
     */
    void deleteLevelConfig(Long id);

    /**
     * 获得代理商等级配置
     *
     * @param id 编号
     * @return 代理商等级配置
     */
    LevelConfigDO getLevelConfig(Long id);

    /**
     * 获得代理商等级配置分页
     *
     * @param pageReqVO 分页查询
     * @return 代理商等级配置分页
     */
    PageResult<LevelConfigDO> getLevelConfigPage(LevelConfigPageReqVO pageReqVO);

    /**
     * 自定义代理商升级规则
     * @return
     */
    int setUpLevelRule(UpLevelRuleDTO upLevelRuleDTO);
}