package com.yunyi.express2b.module.agent.controller.admin.agentprofile;

import com.yunyi.express2b.framework.security.core.util.SecurityFrameworkUtils;
import com.yunyi.express2b.framework.web.core.util.WebFrameworkUtils;
import com.yunyi.express2b.module.agent.api.AgentApi;
import com.yunyi.express2b.module.agent.api.dto.RegisterDTO;
import com.yunyi.express2b.module.agent.dal.dataobject.agentprofile.OrderCountsDO;
import com.yunyi.express2b.module.agent.dal.dataobject.agentprofile.OrderInfoDO;
import com.yunyi.express2b.module.agent.enums.RegisterCode;
import com.yunyi.express2b.module.pricing.api.dto.NewTemplateRelationDTO;
import com.yunyi.express2b.module.pricing.api.dto.TemplateApiDTO;
import jakarta.annotation.security.PermitAll;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.io.File;
import java.time.LocalDateTime;
import java.util.*;
import java.io.IOException;
import com.yunyi.express2b.framework.common.pojo.PageParam;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import static com.yunyi.express2b.framework.common.pojo.CommonResult.success;
import com.yunyi.express2b.framework.excel.core.util.ExcelUtils;
import com.yunyi.express2b.framework.apilog.core.annotation.ApiAccessLog;
import static com.yunyi.express2b.framework.apilog.core.enums.OperateTypeEnum.*;
import com.yunyi.express2b.module.agent.controller.admin.agentprofile.vo.*;
import com.yunyi.express2b.module.agent.dal.dataobject.agentprofile.AgentProfileDO;
import com.yunyi.express2b.module.agent.service.agentprofile.AgentProfileService;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;


/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/5/15 9:53
 */
@Tag(name = "管理后台 - 代理商档案")
@RestController
@RequestMapping("/agent/profile")
@Validated
@Slf4j
public class AgentProfileController {


    @Resource
    private AgentProfileService profileService;
    @Resource
    private AgentApi agentApi;

    @PostMapping("/create")
    @Operation(summary = "创建代理商档案")
    @PreAuthorize("@ss.hasPermission('agent:profile:create')")
    public CommonResult<Long> createProfile(@Valid @RequestBody AgentProfileSaveReqVO createReqVO) {
        return success(profileService.createProfile(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新代理商档案")
    @PreAuthorize("@ss.hasPermission('agent:profile:update')")
    public CommonResult<Boolean> updateProfile(@Valid @RequestBody AgentProfileSaveReqVO updateReqVO) {
        profileService.updateProfile(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除代理商档案")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('agent:profile:delete')")
    public CommonResult<Boolean> deleteProfile(@RequestParam("id") Long id) {
        profileService.deleteProfile(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得代理商档案")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('agent:profile:query')")
    public CommonResult<AgentProfileRespVO> getProfile(@RequestParam("id") Long id) {
        AgentProfileDO profile = profileService.getProfile(id);
        return success(BeanUtils.toBean(profile, AgentProfileRespVO.class));
    }

    /**
     * 获得代理商档案分页
     *
     * @param status
     * @param levelId
     * @param createTime
     * @param name
     * @param pageNo
     * @param pageSize
     * @return
     */
    @GetMapping("/page")
    @Operation(summary = "获得代理商档案分页")
//    @PreAuthorize("@ss.hasPermission('agent:profile:query')")
    public CommonResult<PageResult<AgentProfileRespVO>> getProfilePage(
            @RequestParam(name = "status", required = false) Integer status,
            @RequestParam(name = "levelId", required = false) Long levelId,
            @RequestParam(name = "createTime", required = false) LocalDateTime[] createTime,
            @RequestParam(name = "name", required = false) String name,
            @RequestParam(name = "pageNo", required = false, defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", required = false, defaultValue = "10") Integer pageSize) {
        AgentProfilePageReqVO pageReqVO = new AgentProfilePageReqVO(status, levelId, createTime, name, pageNo, pageSize);
        PageResult<AgentProfileDO> pageResult = profileService.getProfilePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AgentProfileRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出代理商档案 Excel")
    @PreAuthorize("@ss.hasPermission('agent:profile:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportProfileExcel(@Valid AgentProfilePageReqVO pageReqVO,
                                   HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AgentProfileDO> list = profileService.getProfilePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "代理商档案.xls", "数据", AgentProfileRespVO.class,
                BeanUtils.toBean(list, AgentProfileRespVO.class));
    }

    /**
     * 根据价格模板id进行代理商档案的检索（根据memberId字段），并返回一个邀请链接（String）
     *
     * @param templateId
     * @return
     */

    @GetMapping("/createinvitation")
    @Operation(summary = "为下级选择模板，系统自动生成带模板信息的二维码/邀请链接")
    @PermitAll
    public CommonResult<RegisterDTO> createTemplate(@RequestParam("templateId")Long templateId,
                                                    @RequestParam("key")String key){
        //创建推荐链接
        return success(profileService.createUrl(key,templateId));
    }


    /**
     * 根据状态类型以及名字/电话号查询团队信息
     *
     * @param type
     * @param str
     * @return
     */
    @GetMapping("/get-team")
    @Operation(summary = "获取当前代理商账号下的团队信息(全部、直接、间接)")
    @PermitAll
    public CommonResult<PageResult<AgentProfileRespVO>> getTeam(@RequestParam(name = "agentOption",required = false)String type,
                                                                @RequestParam(name = "number",required = false)String str){
        PageResult<AgentProfileDO> pageResult = profileService.getMyTeam(type,str);
        return success(BeanUtils.toBean(pageResult, AgentProfileRespVO.class));
    }

    /**
     * 获取团队数量，直接下级，间接下级
     * @return
     */
    @GetMapping("/get-team-count")
    @Operation(summary = "获取当前代理商账号下的团队数")
    @PermitAll
    public CommonResult<AgentProfileCountVO> getTeamCount(){
        return success(profileService.getTeamCount());
    }



    @PostMapping("/register")
    @PermitAll
    @Operation(summary = "注册")
    public CommonResult<RegisterCode> register(@Valid @RequestBody RegisterDTO registerDTO) {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        requestAttributes.setAttribute(
                WebFrameworkUtils.REQUEST_ATTRIBUTE_LOGIN_USER_ID,1L,
                RequestAttributes.SCOPE_REQUEST
        );
        RequestContextHolder.setRequestAttributes(requestAttributes);

        //根据关联用户中心id注册代理商、注册租户、注册钱包SecurityFrameworkUtils.getLoginUserId()
        return success(profileService.registerProfile(registerDTO));

    }

    @PostMapping("/license")
    @Operation(summary = "上传营业执照")
    @PermitAll
    public CommonResult<Boolean> uploadLicense(@RequestParam("file") MultipartFile file) throws Exception {

        File tempFile = File.createTempFile("license-", file.getOriginalFilename());
        try {
            file.transferTo(tempFile);
            profileService.saveImageUrl(tempFile);
            return success(true);
        } finally {
            // 可选：上传后删除临时文件
            if (tempFile.exists()) {
                tempFile.delete();
            }
        }
    }

    /**
     *获取代理商memberId
     * @return
     */
    @GetMapping("/memberid")
    @PermitAll
    public CommonResult<Long> getMemberId(){
        return success(agentApi.getAgentByMemberId(SecurityFrameworkUtils.getLoginUserId()).getId());
    }

    @PutMapping("/select-direct-template")
    @Operation(summary = "选择直接下级价格模板")
    @PermitAll
    public CommonResult<Boolean> selectDirectTemplate(@RequestParam("agentId")Long agentId,
                                                      @RequestParam("templateId")Long templateId){
        profileService.selectDirectTemplate(agentId,templateId);
        return success(true);
    }

    /**
     * 获取当前代理商的价格模板
     * @return
     */
    @GetMapping("/select-template")
    @Operation(summary = "查询价格模板")
    @PermitAll
    public CommonResult<PageResult<TemplateApiDTO> > selectTemplate(){
        return success(profileService.selectTemplate(SecurityFrameworkUtils.getLoginUserId()));
    }

    @GetMapping("/select-order-count")
    @Operation(summary = "根据当前的代理商id统计订单数量")
    @PermitAll
    public CommonResult<OrderCountsRespVO> selectOrderCount() {
        OrderCountsDO orderCountsDO = profileService.selectOrderCount();
        return success(BeanUtils.toBean(orderCountsDO, OrderCountsRespVO.class));
    }

    @GetMapping("/select-order-list")
    @Operation(summary = "根据当前的代理商id查询订单列表")
    @PermitAll
    public CommonResult<PageResult<OrderResponseVO>> selectOrderList(@Valid OrderRequestVO orderRequestVO) {
        PageResult<OrderInfoDO> orderInfoDOList = profileService.selectOrderList(orderRequestVO);
        log.info("cotroller:" + orderInfoDOList);
        return success(BeanUtils.toBean(orderInfoDOList, OrderResponseVO.class));
    }

    /**
     *     累计结算总额,根据钱包里的代理商id关联钱包流水表计算amount
     */
    @GetMapping("/amount-sum")
    @PermitAll
    @Operation(summary = "根据代理商id查询团队总交易额")
    public CommonResult<AgentProfileCountVO> amountSUM(){
        AgentProfileCountVO agentProfileCountVO = new AgentProfileCountVO()
                .setAmountSUM(profileService.getamountSUM(SecurityFrameworkUtils.getLoginUserId()));
        return success(agentProfileCountVO);
    }

    /**
     * 传递模版类型查询当前代理商的所有可用模版
     * @param templateType
     * @return
     */
    @GetMapping("/system-custom-newtemplate")
    @Operation(summary = "获取系统模板以及自定义的模板")
    @PermitAll
    public CommonResult<List<NewTemplateRelationDTO>>getNewTypeTemplate(@RequestParam("templateType")Long templateType){
        return success(profileService.getTypeNewTemplate(templateType));
    }
}