package com.yunyi.express2b.module.agent.controller.admin.customtemplaterequest.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;


/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/5/15 9:53
 */
@Schema(description = "管理后台 - 代理商自定义价格模板申请记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class CustomTemplateRequestRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "8006")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "申请代理商ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27662")
    @ExcelProperty("申请代理商ID")
    private Long agentId;

    @Schema(description = "申请的模板参数（JSON格式）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("申请的模板参数（JSON格式）")
    private String requestedTemplateDetails;

    @Schema(description = "申请状态（0-待审核, 1-已批准, 2-已拒绝）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("申请状态（0-待审核, 1-已批准, 2-已拒绝）")
    private Integer status;

    @Schema(description = "若批准，关联到pricing模块中为此申请创建的模板ID", example = "16587")
    @ExcelProperty("若批准，关联到pricing模块中为此申请创建的模板ID")
    private Long approvedTemplateId;

    @Schema(description = "申请时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("申请时间")
    private LocalDateTime requestedAt;

    @Schema(description = "审核时间")
    @ExcelProperty("审核时间")
    private LocalDateTime reviewedAt;

    @Schema(description = "审核备注")
    @ExcelProperty("审核备注")
    private String reviewerComments;

    @Schema(description = "申请备注", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("申请备注")
    private String remarks;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建者id", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建者id")
    private String creator;

    @Schema(description = "更新者id", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("更新者id")
    private String updater;

    @Schema(description = "申请模版昵称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("申请模版名称")
    private String templateName;

}