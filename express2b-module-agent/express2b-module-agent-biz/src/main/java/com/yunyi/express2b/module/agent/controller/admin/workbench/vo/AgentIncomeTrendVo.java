package com.yunyi.express2b.module.agent.controller.admin.workbench.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 收入趋势
 *
 * <AUTHOR>
 * @since 17
 * @version 1.0
 * @since 2025/5/27 下午1:38
 */
@Schema(description = "代理商收入趋势显示")
@Data
public class AgentIncomeTrendVo {
    @Schema(description = "收入总额")
    List<Long> incomeTotal;

    @Schema(description = "日期")
    List<String> incomeDate;
}