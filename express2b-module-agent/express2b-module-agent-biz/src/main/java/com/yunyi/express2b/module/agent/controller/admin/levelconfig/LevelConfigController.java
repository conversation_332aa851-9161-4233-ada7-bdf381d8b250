package com.yunyi.express2b.module.agent.controller.admin.levelconfig;

import com.yunyi.express2b.module.agent.api.AgentApi;
import com.yunyi.express2b.module.agent.api.dto.UpLevelRuleDTO;
import jakarta.annotation.security.PermitAll;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;
import com.yunyi.express2b.framework.common.pojo.PageParam;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import static com.yunyi.express2b.framework.common.pojo.CommonResult.success;
import com.yunyi.express2b.framework.excel.core.util.ExcelUtils;
import com.yunyi.express2b.framework.apilog.core.annotation.ApiAccessLog;
import static com.yunyi.express2b.framework.apilog.core.enums.OperateTypeEnum.*;
import com.yunyi.express2b.module.agent.controller.admin.levelconfig.vo.*;
import com.yunyi.express2b.module.agent.dal.dataobject.levelconfig.LevelConfigDO;
import com.yunyi.express2b.module.agent.service.levelconfig.LevelConfigService;


/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/5/15 9:53
 */
@Tag(name = "管理后台 - 代理商等级配置")
@RestController
@RequestMapping("/agent/level-config")
@Validated
public class LevelConfigController {

    @Resource
    private LevelConfigService levelConfigService;

    @PostMapping("/create")
    @Operation(summary = "创建代理商等级配置")
    @PreAuthorize("@ss.hasPermission('agent:level-config:create')")
    public CommonResult<Long> createLevelConfig(@Valid @RequestBody LevelConfigSaveReqVO createReqVO) {
        return success(levelConfigService.createLevelConfig(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新代理商等级配置")
    @PreAuthorize("@ss.hasPermission('agent:level-config:update')")
    public CommonResult<Boolean> updateLevelConfig(@Valid @RequestBody LevelConfigSaveReqVO updateReqVO) {
        levelConfigService.updateLevelConfig(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除代理商等级配置")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('agent:level-config:delete')")
    public CommonResult<Boolean> deleteLevelConfig(@RequestParam("id") Long id) {
        levelConfigService.deleteLevelConfig(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得代理商等级配置")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('agent:level-config:query')")
    public CommonResult<LevelConfigRespVO> getLevelConfig(@RequestParam("id") Long id) {
        LevelConfigDO levelConfig = levelConfigService.getLevelConfig(id);
        return success(BeanUtils.toBean(levelConfig, LevelConfigRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得代理商等级配置分页")
    @PreAuthorize("@ss.hasPermission('agent:level-config:query')")
    public CommonResult<PageResult<LevelConfigRespVO>> getLevelConfigPage(@Valid LevelConfigPageReqVO pageReqVO) {
        PageResult<LevelConfigDO> pageResult = levelConfigService.getLevelConfigPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, LevelConfigRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出代理商等级配置 Excel")
    @PreAuthorize("@ss.hasPermission('agent:level-config:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportLevelConfigExcel(@Valid LevelConfigPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<LevelConfigDO> list = levelConfigService.getLevelConfigPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "代理商等级配置.xls", "数据", LevelConfigRespVO.class,
                        BeanUtils.toBean(list, LevelConfigRespVO.class));
    }

    @PutMapping("/set-level-config")
    @Operation(summary = "设置代理商等级规则")
    @PermitAll
    public CommonResult<Integer> setLevelConfig(@Valid @RequestBody UpLevelRuleDTO upLevelRuleDTO){
        return success(levelConfigService.setUpLevelRule(upLevelRuleDTO));
    }

}