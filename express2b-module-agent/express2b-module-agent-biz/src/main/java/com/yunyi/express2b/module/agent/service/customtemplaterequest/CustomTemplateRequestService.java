package com.yunyi.express2b.module.agent.service.customtemplaterequest;

import com.yunyi.express2b.module.pricing.api.dto.NewTemplateRelationDTO;
import com.yunyi.express2b.module.pricing.api.dto.TemplateRelationDTO;
import jakarta.validation.*;
import com.yunyi.express2b.module.agent.controller.admin.customtemplaterequest.vo.*;
import com.yunyi.express2b.module.agent.dal.dataobject.customtemplaterequest.CustomTemplateRequestDO;
import com.yunyi.express2b.framework.common.pojo.PageResult;

import java.util.List;

/**
 * 代理商自定义价格模板申请记录 Service 接口
 *
 * <AUTHOR>
 */
public interface CustomTemplateRequestService {

    /**
     * 创建代理商自定义价格模板申请记录
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createCustomTemplateRequest(@Valid CustomTemplateRequestSaveReqVO createReqVO);

    /**
     * 更新代理商自定义价格模板申请记录
     *
     * @param updateReqVO 更新信息
     */
    void updateCustomTemplateRequest(@Valid CustomTemplateRequestSaveReqVO updateReqVO);

    /**
     * 删除代理商自定义价格模板申请记录
     *
     * @param id 编号
     */
    void deleteCustomTemplateRequest(Long id);

    /**
     * 获得代理商自定义价格模板申请记录
     *
     * @param id 编号
     * @return 代理商自定义价格模板申请记录
     */
    CustomTemplateRequestDO getCustomTemplateRequest(Long id);

    /**
     * 获得代理商自定义价格模板申请记录分页
     *
     * @param pageReqVO 分页查询
     * @return 代理商自定义价格模板申请记录分页
     */
    PageResult<CustomTemplateRequestDO> getCustomTemplateRequestPage(CustomTemplateRequestPageReqVO pageReqVO);

    /**
     * 插入代理商自定义模板数据
     * @param customTemplateRequestDO
     * @return
     */
    Integer insertTemplateSave(CustomTemplateRequestRespVO customTemplateRequestDO);

    /**
     * 更新根据自定义模版表更新状态码
     * @param
     * @param reviewTemplateVo
     * @param
     * @return
     */
    boolean updateTemplate(ReviewTemplateVo reviewTemplateVo);

    /**
     * 查询回显(代理商申请模版是否通过)
     * @param agentId
     * @return
     */
    List<CustomTemplateRequestRespVO> getQueryEcho(Long agentId);

    /**
     * 管理员查询未审核的申请模版
     * @return
     */
    List<CustomTemplateEchoVO> getAgentTemplateRequest(String remark);

    /**
     * 代理商重新申请修改的模版
     * @param
     * @param customTemplateRequestSaveReqVO
     * @return
     */
    boolean renewTemplate(CustomTemplateRequestSaveReqVO customTemplateRequestSaveReqVO);

    /**
     * 代理商查询已审核的模版
     * @param agentId
     * @return
     */
    List<NewCustomTemplateVO> getTemplateEcho(Long agentId);

    /**
     * 代理商修改默认模版
     * @param id
     * @return
     */
    boolean reviseDefaultTemplate(Long id,Long agentId);

    /**
     * 查询代理商申请通过的价格模板
     * @return
     */
    List<NewTemplateRelationDTO> getNewTemplateList();

    /**
     * 审核历史模版追溯
     * @return
     */
    List<CustomTemplateReviewVO> getReviewTemplateHistory(String remark);

}