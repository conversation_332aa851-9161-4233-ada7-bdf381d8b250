package com.yunyi.express2b.module.agent.api.agentprofile;


import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.framework.security.core.util.SecurityFrameworkUtils;
import com.yunyi.express2b.module.agent.api.AgentApi;
import com.yunyi.express2b.module.agent.api.dto.*;
import com.yunyi.express2b.module.agent.dal.dataobject.agentprofile.AgentProfileDO;
import com.yunyi.express2b.module.agent.dal.dataobject.levelconfig.LevelConfigDO;
import com.yunyi.express2b.module.agent.dal.mysql.agentprofile.AgentProfileMapper;
import com.yunyi.express2b.module.agent.dal.mysql.levelconfig.LevelConfigMapper;
import com.yunyi.express2b.module.agent.dal.mysql.relationship.RelationshipMapper;
import com.yunyi.express2b.module.wallet.api.TransationsApi.TransactionsApi;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.module.agent.enums.ConstValue.*;
import static com.yunyi.express2b.module.agent.enums.ConstValue.DEFAULT_ACTIVITY_COUNT;
import static com.yunyi.express2b.module.agent.enums.ErrorCodeConstants.AGENT_NOT_EXISTS;
import static com.yunyi.express2b.module.agent.enums.ErrorCodeConstants.LEVEL_CONFIG_NOT_EXISTS;


/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/5/15 9:53
 */
@Service
public class AgentApiServiceImpl implements AgentApi {

    @Resource
    private AgentProfileMapper agentProfileMapper;
    @Resource
    private RelationshipMapper relationshipMapper;
    @Resource
    private LevelConfigMapper levelConfigMapper;
    @Resource
    private TransactionsApi transactionsApi;


    /**
     * 查询代理商的所有上级（带层级距离)
     * @param agentId
     * @return
     */
    @Override
    public List<RelationShipDTO> getListByAgentId(Long agentId) {
        return BeanUtils.toBean(relationshipMapper.getlistByAgentId(agentId),  RelationShipDTO.class);
    }

    /**
     * 获取代理商信息+等级名称
     * @param agentId
     * @return
     */
    @Override
    public AgentProfileRespDTO getAgentProfile(Long agentId) {
        //通过代理商id查询代理商基本信息以及等级名称
        AgentProfileRespDTO agentProfileRespDTO = agentProfileMapper.selectAgentAndLevelNameById(agentId);
        //判断代理商存在
        if(agentProfileRespDTO==null){
            throw exception(AGENT_NOT_EXISTS);
        }
        return agentProfileRespDTO;

    }

    /**
     * 查询距离当前代理商最近的特定级别上级
     * @param agentId
     * @param levelId
     * @return
     */
    @Override
    public RelationShipDTO getAgentProfileByAgentIdAndLevelId(Long agentId, Long levelId) {
        RelationShipDTO relationShipDTO = relationshipMapper.selectAgentByAgentIdAndLevelId(agentId,levelId);

        relationShipDTO.setNearestSpecificLevelAncestorId(relationShipDTO.getNearestSpecificLevelAncestorId());
        if (relationShipDTO == null){
            throw exception(AGENT_NOT_EXISTS);
        }
        return relationShipDTO;
    }


    /**
     * 根据ID查询代理商信息
     * @param agentId
     * @return
     */
    @Override
    public AgentInfoDTO getAgentInfo(Long agentId) {
        AgentProfileDO agentProfileDO = agentProfileMapper.selectById(agentId);
        //校验agent是否存在
        if (agentProfileDO == null) {
            throw exception(AGENT_NOT_EXISTS);
        }
        return BeanUtils.toBean(agentProfileDO,  AgentInfoDTO.class);
    }


    /**
     * 获取代理商等级ID(testAccess)
     * @return
     */
    @Override
    public Long getAgentLevelId() {
        //根据用户id查询代理商等级
        AgentProfileDO agentProfileDO = agentProfileMapper.getLevelId(SecurityFrameworkUtils.getLoginUserId());
        //判断该联系人的存在
        if(agentProfileDO.getLevelId()==null){
            throw exception(AGENT_NOT_EXISTS);
        }
        //返回等级ID
        return agentProfileDO.getLevelId();
    }

    /**
     * 根据memberId查询代理商信息
     * @param memberId
     * @return
     */
    @Override
    public AgentInfoDTO getAgentByMemberId(Long memberId) {
        return BeanUtils.toBean(agentProfileMapper.getAgentIdByMemberId(memberId),  AgentInfoDTO.class);
    }

    /**
     * 获取代理商MemberId
     * @return
     */
    @Override
    public Long getAgentIdMemberId() {
        return SecurityFrameworkUtils.getLoginUserId();
    }

    /**
     * 检查代理商等级提升(个人检查)
     */
    @Override
    public int checkLevelUp() {
        //修改的数据条数
        int count = DEFAULT_COUNT;
        //按等级进行分组检查
        Long levelId = DEFAULT_LEVEL_ID;
        //查询每一级的代理商id列表（不包含最大等级）
        Long maxLevel = levelConfigMapper.getMaxLevel();
        while (!levelId.equals(maxLevel)) {
            //根据代理商id判断是否够升级流水总数
            LevelConfigDO levelConfigDO = levelConfigMapper.selectById(levelId);
            //校验等级存在
            if(levelConfigDO==null){
                throw exception(LEVEL_CONFIG_NOT_EXISTS);
            }
            //获取该等级代理商id列表
            List<Long> agentIdList = getAgentIdListByLevelId(levelId);
            if(agentIdList!=null){
                for (Long agentId : agentIdList) {
                    //进行个人以及团队流水判定是否提升该代理商等级
                    if(transactionsApi.getSumBetweenTime(agentId) >= levelConfigDO.getUpgradePersonalDailyOrders()){
                        //进行代理商等级提升
                        count+=agentProfileMapper.upLevelByAgentId(agentId,levelId+1);
                    }else if(transactionsApi.getTeamSumBetweenTime(agentId) >= levelConfigDO.getUpgradeTeamDailyOrders()){
                        count+=agentProfileMapper.upLevelByAgentId(agentId,levelId+1);
                    }
                }
            }
            //下一等级id
            levelId++;
        }
        return count;
    }

    /**
     * 根据等级ID查询代理商ID列表
     * @param levelId
     * @return
     */
    @Override
    public List<Long> getAgentIdListByLevelId(Long levelId) {
        return agentProfileMapper.getAgentIdListByLevelId(levelId);
    }

    /**
     * 获取的团队成员id
     * @param agentId
     * @return
     */
    @Override
    public List<Long> getTeamIdByAgentId(Long agentId) {
        return relationshipMapper.getTeamIdByAgentId(agentId);
    }

    /**
     * 通过代理商id获取等级提升规则参数
     * @param agentId
     * @return
     */
    @Override
    public UpLevelRuleDTO getUpLevelRule(Long agentId) {
        AgentProfileDO agentProfileDO = agentProfileMapper.selectById(agentId);
        return levelConfigMapper.getUpLevelRule(agentProfileDO.getLevelId()+1);
    }

    /**
     * 获取代理商直接下级
     * @param agentId
     * @return
     */
    @Override
    public List<Long> getAgentDirectIdList(Long agentId) {
        return relationshipMapper.getAgentDirectIdList(agentId);
    }

    /**
     * 获取代理商推广人数
     * @param agentActivityDTO
     * @return
     */
    @Override
    public AgentActivityDTO getAgentPromotersCount(AgentActivityDTO agentActivityDTO,List<Long> teamIds) {
        //获取代理商推广人数活跃度
        agentActivityDTO = relationshipMapper.getAgentPromotersCount(agentActivityDTO);
        //给空团队添加默认推广数据，防止空指针异常以及减少数据库访问次数
        if(teamIds.isEmpty()){
            agentActivityDTO
                    .setWeekTeamPromoteCount(DEFAULT_ACTIVITY_COUNT)
                    .setLastWeekTeamPromoteCount(DEFAULT_ACTIVITY_COUNT)
                    .setMonthTeamPromoteCount(DEFAULT_ACTIVITY_COUNT)
                    .setLastMonthTeamPromoteCount(DEFAULT_ACTIVITY_COUNT);

        }else{
            //获取代理商团队推广人数活跃度
            agentActivityDTO = relationshipMapper.getTeamPromotersCount(agentActivityDTO,teamIds);
        }
        //封装数据
        return agentActivityDTO;
    }


}
