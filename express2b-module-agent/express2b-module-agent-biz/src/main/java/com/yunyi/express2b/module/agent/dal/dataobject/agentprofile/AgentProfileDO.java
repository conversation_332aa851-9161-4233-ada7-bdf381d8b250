package com.yunyi.express2b.module.agent.dal.dataobject.agentprofile;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import com.yunyi.express2b.framework.mybatis.core.dataobject.BaseDO;

/**
 * 代理商档案 DO
 *
 * <AUTHOR>
 */
@TableName("agent_agent_profile")
@KeySequence("agent_agent_profile_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AgentProfileDO extends BaseDO {

    /**
     * 代理商唯一标识
     */
    @TableId
    private Long id;
    /**
     * 关联用户中心的用户ID（SSOUSERID）
     */
    private Long memberId;
    /**
     * 代理商名称/联系人
     */
    private String name;
    /**
     * 手机号，唯一，用于登录和联系
     */
    private String mobile;
    /**
     * 邮箱（可选）
     */
    private String email;
    /**
     * 状态（0-待审核, 1-正常, 2-冻结）对应AgentStatusEnum
     */
    private Integer status;
    /**
     * 当前等级ID，关联agent_level_config.id
     */
    private Long levelId;
    /**
     * 推荐人代理商ID（上级）
     */
    private Long referrerAgentId;
    /**
     * 代理商专属自定义价格模板ID
     */
    private Long customPricingTemplateId;
    /**
     * 关联钱包模块的钱包ID
     */
    private String walletId;
    /**
     * 关联系统模块的租户ID
     */
    private Long systemTenantId;
    /**
     * 关联系统模块的租户ID
     */
    private Long tenantId;
    /**
     * 直接下级数量
     */
    private Integer directDownlineCount;
    /**
     * 团队总下级数量
     */
    private Integer totalDownlineCount;

}