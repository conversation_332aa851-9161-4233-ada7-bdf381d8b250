package com.yunyi.express2b.module.agent.controller.admin.agentprofile.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;


/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/5/15 9:53
 */
@Schema(description = "管理后台 - 代理商档案 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AgentProfileRespVO {

    @Schema(description = "代理商唯一标识", requiredMode = Schema.RequiredMode.REQUIRED, example = "31558")
    @ExcelProperty("代理商唯一标识")
    private Long id;

    @Schema(description = "关联用户中心的用户ID（SSOUSERID）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1691")
    @ExcelProperty("关联用户中心的用户ID（SSOUSERID）")
    private Long memberId;

    @Schema(description = "代理商名称/联系人", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("代理商名称/联系人")
    private String name;

    @Schema(description = "手机号，唯一，用于登录和联系", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("手机号，唯一，用于登录和联系")
    private String mobile;

    @Schema(description = "邮箱（可选）")
    @ExcelProperty("邮箱（可选）")
    private String email;

    @Schema(description = "状态（0-待审核, 1-正常, 2-冻结）对应AgentStatusEnum", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("状态（0-待审核, 1-正常, 2-冻结）对应AgentStatusEnum")
    private Integer status;

    @Schema(description = "当前等级ID，关联agent_level_config.id", requiredMode = Schema.RequiredMode.REQUIRED, example = "20275")
    @ExcelProperty("当前等级ID，关联agent_level_config.id")
    private Long levelId;

    @Schema(description = "推荐人代理商ID（上级）", example = "18141")
    @ExcelProperty("推荐人代理商ID（上级）")
    private Long referrerAgentId;

    @Schema(description = "代理商专属自定义价格模板ID", example = "27560")
    @ExcelProperty("代理商专属自定义价格模板ID")
    private Long customPricingTemplateId;

    @Schema(description = "关联钱包模块的钱包ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "3435")
    @ExcelProperty("关联钱包模块的钱包ID")
    private String walletId;

    @Schema(description = "关联系统模块的租户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "6844")
    @ExcelProperty("关联系统模块的租户ID")
    private Long tenantId;

    @Schema(description = "直接下级数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "15584")
    @ExcelProperty("直接下级数量")
    private Integer directDownlineCount;

    @Schema(description = "团队总下级数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "18731")
    @ExcelProperty("团队总下级数量")
    private Integer totalDownlineCount;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}