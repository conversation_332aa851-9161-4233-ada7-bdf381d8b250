package com.yunyi.express2b.module.agent.service.agentprofile;

import com.yunyi.express2b.framework.common.enums.CommonStatusEnum;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.framework.security.core.util.SecurityFrameworkUtils;
import com.yunyi.express2b.framework.tenant.core.aop.TenantIgnore;
import com.yunyi.express2b.module.agent.api.dto.AgentActivityDTO;
import com.yunyi.express2b.module.agent.api.dto.RegisterDTO;
import com.yunyi.express2b.module.agent.controller.admin.agentprofile.vo.*;
import com.yunyi.express2b.module.agent.dal.dataobject.relationship.RelationshipDO;
import com.yunyi.express2b.module.agent.convert.OrdersMapStructMapper;
import com.yunyi.express2b.module.agent.dal.dataobject.agentprofile.AgentProfileDO;
import com.yunyi.express2b.module.agent.dal.dataobject.agentprofile.OrderCountsDO;
import com.yunyi.express2b.module.agent.dal.dataobject.agentprofile.OrderInfoDO;
import com.yunyi.express2b.module.agent.dal.mysql.agentprofile.AgentProfileMapper;
import com.yunyi.express2b.module.agent.dal.mysql.relationship.RelationshipMapper;
import com.yunyi.express2b.module.agent.enums.ConstValue;
import com.yunyi.express2b.module.agent.enums.RegisterCode;
import com.yunyi.express2b.module.agent.service.relationship.RelationshipService;
import com.yunyi.express2b.module.express.api.order.OrderCountsApi;
import com.yunyi.express2b.module.express.api.order.dto.OrderCountsDTO;
import com.yunyi.express2b.module.express.api.order.dto.OrderDTO;
import com.yunyi.express2b.module.express.api.vo.RequestOrderVO;
import com.yunyi.express2b.module.express.enums.OrderStatusTypeEnum;
import com.yunyi.express2b.module.infra.api.config.ConfigApi;
import com.yunyi.express2b.module.infra.api.file.FileApi;
import com.yunyi.express2b.module.pricing.api.NewTemplateApi;
import com.yunyi.express2b.module.pricing.api.PricingApiflow;
import com.yunyi.express2b.module.pricing.api.TemplateApi;
import com.yunyi.express2b.module.pricing.api.dto.NewTemplateRelationDTO;
import com.yunyi.express2b.module.pricing.api.dto.TemplateApiDTO;
import com.yunyi.express2b.module.pricing.api.vo.NewTemplateSaveReqVO;
import com.yunyi.express2b.module.pricing.api.vo.SelectedPricingTemplateSaveReqVO;
import com.yunyi.express2b.module.system.api.tenant.TenantApi;
import com.yunyi.express2b.module.system.api.tenant.dto.TenantSaveReqDTO;
import com.yunyi.express2b.module.wallet.api.TransationsApi.TransactionsApi;
import com.yunyi.express2b.module.wallet.api.TransationsApi.dto.TransactionsDTO;
import com.yunyi.express2b.module.wallet.api.WalletApi.WalletApi;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import java.io.File;
import java.util.ArrayList;
import java.util.List;
import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.module.agent.enums.ConstValue.*;
import static com.yunyi.express2b.module.agent.enums.ErrorCodeConstants.*;

/**
 * 代理商档案 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class AgentProfileServiceImpl implements AgentProfileService {

    @Resource
    private AgentProfileMapper agentProfileMapper;
    @Resource
    private PricingApiflow pricingApiflow;
    @Resource
    private WalletApi walletApi;
    @Resource
    private TenantApi tenantApi;
    @Resource
    private FileApi fileApi;
    @Resource
    private RelationshipService relationshipService;
    @Resource
    private ConfigApi configApi;
    @Resource
    private RelationshipMapper relationshipMapper;
    @Resource
    private TemplateApi templateApi;
    @Resource
    private TransactionsApi transactionsApi;
    @Resource
    private OrderCountsApi orderCountsApi;
    @Resource
    private NewTemplateApi newTemplateApi;

    @Override
    public Long createProfile(AgentProfileSaveReqVO createReqVO) {
        // 插入
        AgentProfileDO profile = BeanUtils.toBean(createReqVO, AgentProfileDO.class);
        agentProfileMapper.insert(profile);
        // 返回
        return profile.getId();
    }

    @Override
    public void updateProfile(AgentProfileSaveReqVO updateReqVO) {
        // 校验存在
        validateProfileExists(updateReqVO.getId());
        // 更新
        AgentProfileDO updateObj = BeanUtils.toBean(updateReqVO, AgentProfileDO.class);
        agentProfileMapper.updateById(updateObj);
    }

    @Override
    public void deleteProfile(Long id) {
        // 校验存在
        validateProfileExists(id);
        // 删除
        agentProfileMapper.deleteById(id);
    }

    private void validateProfileExists(Long id) {
        if (agentProfileMapper.selectById(id) == null) {
            throw exception(PROFILE_NOT_EXISTS);
        }
    }

    @Override
    public AgentProfileDO getProfile(Long id) {
        return agentProfileMapper.selectById(id);
    }

    @Override
    public PageResult<AgentProfileDO> getProfilePage(AgentProfilePageReqVO pageReqVO) {
        return agentProfileMapper.selectPage(pageReqVO);
    }

    /**
     * 根据为下级选择的定价模板更新代理商档案
     * @param agentProfileDO
     */
    @Override
    public void  editAgentProfile(AgentProfileDO agentProfileDO) {
        if(agentProfileDO.getCustomPricingTemplateId()==null){
            throw exception(TEMPLATE_NOT_EXISTS);
        }
        if(agentProfileDO.getReferrerAgentId()==null){
            throw exception(AGENT_NOT_EXISTS);
        }
        //创建选择价格模板的对象
        SelectedPricingTemplateSaveReqVO selectedPricingTemplateSaveReqVO = new SelectedPricingTemplateSaveReqVO()
                .setSuperiorAgentId(agentProfileDO.getReferrerAgentId())
                .setDownlineAgentId(agentProfileDO.getId())
                .setPricingTemplateId(agentProfileDO.getCustomPricingTemplateId());
        //创建为下级选择价格模板流水
        pricingApiflow.createSelectedPricingTemplate(selectedPricingTemplateSaveReqVO);
    }

    /**
     * 通过父团队信息的id查询子团队信息
     * @return
     */
    @Override
    public List<AgentProfileDO> getTeam() {
        AgentProfileDO agentProfileDO = agentProfileMapper.getAgentIdByMemberId(1L);
        return agentProfileMapper.getTeamById(agentProfileDO.getId());
    }

    /**
     * 根据memberId查询代理商档案ID
     * @return
     */
    @Override
    public RegisterDTO getProfileByMemberId(Long memberId,Long templateId) {
        AgentProfileDO agentProfileDO = agentProfileMapper.getAgentIdByMemberId(memberId);
        //判断存在
        if(agentProfileDO==null)
            throw exception(AGENT_NOT_EXISTS);
        NewTemplateSaveReqVO templateSaveReqVO = pricingApiflow.statusTemplate(templateId);
        //判断是否可用
        if(templateSaveReqVO.getStatus()==0L)
            throw exception(TEMPLATE_NOT_EXISTS);
        RegisterDTO registerDTO = new RegisterDTO()
                .setAncestorId(agentProfileDO.getId())
                .setTemplateId(templateId);
        return registerDTO;
    }

    /**
     * 注册 前后端联调的时候数据写死了 参数需要修改
     * @param registerDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public RegisterCode registerProfile(RegisterDTO registerDTO) {

        Boolean register = false;

        //判断手机号是否唯一
        if(agentProfileMapper.getAgentByMobile(registerDTO.getMobile())!=null) {
            throw exception(MOBILE_EXISTS);
        }


        //创建租户，获取代理商ID
        TenantSaveReqDTO tenantSaveReqDTO = new TenantSaveReqDTO()
                .setName(registerDTO.getUsername())
                .setUsername(registerDTO.getUsername())
                .setPassword(registerDTO.getPassword())
                .setExpireTime(ConstValue.DEFAULT_EXPIRE_TIME)
                .setStatus(ConstValue.TENANT_DEFAULT_STATUS)
                .setPackageId(registerDTO.getPackageId())
                .setAccountCount(registerDTO.getAccountCount())
                .setContactName(registerDTO.getUsername())
                .setRemark(DEFAULT_REMARK);
        Long systemTenantID = tenantApi.createTenant(tenantSaveReqDTO);



        //注册代理商档案并注册租户 下面一定是将这个成员ID设置为当前的档案ID SecurityFrameworkUtils.getLoginUserId()
        AgentProfileRespVO agentProfileRespVO = new AgentProfileRespVO()
                .setName(registerDTO.getUsername())
                .setMobile(registerDTO.getMobile())
                .setMemberId((tenantApi.getTenant(systemTenantID)).getContactUserId())
                .setEmail(registerDTO.getEmail())
                .setStatus(CommonStatusEnum.ENABLE.getStatus())
                .setLevelId(ConstValue.DEFAULT_LEVEL_ID)
                .setTenantId(systemTenantID);
        //判断是否为扫码注册
        if(registerDTO.getAncestorId()!=null&&registerDTO.getTemplateId()!=null){
            agentProfileRespVO.setReferrerAgentId(registerDTO.getAncestorId())
                    .setCustomPricingTemplateId(registerDTO.getTemplateId());
            register = true;
        }
        AgentProfileDO agentProfileDO = BeanUtils.toBean(agentProfileRespVO, AgentProfileDO.class)
                        .setSystemTenantId(systemTenantID);
        agentProfileMapper.insert(agentProfileDO);
        Long agentId = agentProfileDO.getId();

        //进行模板类型数据流水创建
        if(register){
            //创建为下级选择模板表流水
            editAgentProfile(agentProfileDO);
            //创建分享模板绑定流水
            newTemplateApi.insertTemplateRelationshipOnUnder(agentId,registerDTO.getTemplateId(),systemTenantID);
        }else {
            //创建系统默认模板绑定流水
            newTemplateApi.insertTemplateRelationshipForRegister(agentProfileDO.getId(),newTemplateApi.getSystemTemplateId(),agentProfileDO.getTenantId());
        }

        //注册钱包，存入钱包id
        agentProfileDO.setWalletId((walletApi.createWallet(agentProfileDO.getId(),systemTenantID)).toString());

        //更新代理商表，将钱包id、租户id存入
        agentProfileMapper.updateById(agentProfileDO);

        //更新团队关系表
        relationshipService.insertRelationshipById(agentProfileDO);

        return RegisterCode.REGISTER;
    }

    /**
     * 上传营业执照,并保存
     * @param file
     */
    @Override
    public void saveImageUrl(File file) throws Exception {

        //上传图片返回url
        List<String> imageUrlList= fileApi.imageUploadMsg(file);
        if (imageUrlList == null || imageUrlList.isEmpty()) {
            throw new Exception("图片上传失败");
        }
        // 从返回的列表中获取第一个URL（假设只需要一个）
        String imageUrl = imageUrlList.get(0);

        Long memberId = SecurityFrameworkUtils.getLoginUserId();
        AgentProfileDO agentProfile = agentProfileMapper.selectByMemberId(memberId);
        if (agentProfile == null) {
            throw new Exception("memberId对应的记录不存在，无法更新");
        }
        int rows = agentProfileMapper.updateLicenseUr(imageUrl, memberId);
        if (rows <= 0) {
            throw new Exception("保存营业执照URL失败，请重试");
        }

    }

    @Override
    public OrderCountsDO selectOrderCount() {
        OrderCountsDTO orderCountsDTO = orderCountsApi.selectOrderCount();
        return BeanUtils.toBean(orderCountsDTO, OrderCountsDO.class);

    }

    @Override
    public PageResult<OrderInfoDO> selectOrderList(OrderRequestVO orderRequestVO) {

        PageResult<OrderDTO> pageResult = orderCountsApi.selectOrderList(BeanUtils.toBean(orderRequestVO, RequestOrderVO.class));
        log.info("pageResult: {}", pageResult);
        List<OrderInfoDO> orderInfoDOList = OrdersMapStructMapper.INSTANCE.DTOListToDOList(pageResult.getList());
        for (OrderInfoDO orderInfoDO : orderInfoDOList) {
            orderInfoDO.setStatus(OrderStatusTypeEnum.getNameByStatus(orderInfoDO.getStatus()));
        }
        return new PageResult<>(orderInfoDOList, pageResult.getTotal());
    }

    /**
     * 查询当前账号下的代理商团队
     * @return
     */
    @Override
    public PageResult<AgentProfileDO> getMyTeam(String type,String str) {
        //判断str的含义
        String name = null;
        String mobile = null;
        if(str != null && !str.isEmpty()){
            //判断是否是数字
            if(str.matches("\\d+")){
                mobile = str;
            }else{
                name = str;
            }
        }
        //根据前端返回的代理商信息查询代理商
        AgentProfileDO agentProfileDO = agentProfileMapper.getAgentIdByMemberId(SecurityFrameworkUtils.getLoginUserId());
        //根据选择的类型返回代理商信息
        if(type != null){
            if("direct".equals(type)){
                //直接代理商
                return agentProfileMapper.selectDirectTeam(agentProfileDO.getId(),name,mobile);
            }
            if("indirect".equals(type)){
                //间接代理商
                return agentProfileMapper.selectIndirectTeam(agentProfileDO.getId(),name,mobile);
            }
        }
        //全部代理商
        return agentProfileMapper.selectTeam(agentProfileDO.getId(),name,mobile);
    }

    /**
     * 获取团队总数，直接下级总数，简间接下级总数
     * @return
     */
    @Override
    public AgentProfileCountVO getTeamCount() {
        AgentProfileDO agentProfileDO = agentProfileMapper.getAgentIdByMemberId(SecurityFrameworkUtils.getLoginUserId());
        AgentProfileCountVO  agentProfileCountVO = new AgentProfileCountVO()
                .setTeamCount(agentProfileMapper.selectTeam(agentProfileDO.getId(),null,null).getTotal())
                .setDirectTeamCount(agentProfileMapper.selectDirectTeam(agentProfileDO.getId(),null,null).getTotal());
        agentProfileCountVO.setIndirectTeamCount(agentProfileCountVO.getTeamCount()-agentProfileCountVO.getDirectTeamCount());
        return agentProfileCountVO;
    }

    /**
     * 为直接下级选择模板
     * @param agentId
     * @param templateId
     */
    @Override
    public void selectDirectTemplate(Long agentId, Long templateId) {

        //获取代理商
        AgentProfileDO descendant = agentProfileMapper.selectById(agentId);
        if(descendant == null){
            throw exception(AGENT_NOT_EXISTS);
        }
        if(descendant.getStatus() == 2){
            throw exception(AGENT_NOT_AVAILABLE);
        }
        //查询代理商是否为直接下级
        RelationshipDO relationshipDO = relationshipMapper.selectDepth(SecurityFrameworkUtils.getLoginUserId(),descendant.getId());
        if(relationshipDO.getDepth()!=1){
            throw exception(AGENT_NOT_DIRECT);
        }
        //修改代理商的价格模板
        descendant.setCustomPricingTemplateId(templateId);
        agentProfileMapper.updateById(descendant);

    }

    /**
     * 查询模板
     * @param memberId
     * @return
     */
    @Override
    public PageResult<TemplateApiDTO> selectTemplate(Long memberId) {
        AgentProfileDO agentProfileDO = agentProfileMapper.getAgentIdByMemberId(memberId);
        return templateApi.selectTemplatePage(agentProfileDO.getId());
    }

    /**
     * 查询团队累计结算总额
     * @param memberId
     * @return
     */
    @Override
    public Long getamountSUM(Long memberId) {
        AgentProfileDO agentProfileDO = agentProfileMapper.getAgentIdByMemberId(memberId);
        //查询团队成员id
        List<Long> agentIdList = agentProfileMapper.getAgentIdList(agentProfileDO.getId());
        agentIdList.add(agentProfileDO.getId());
        //通过api查询团队交易流水
        List<TransactionsDTO> transactionsDTOList = transactionsApi.getAmountSUM(agentIdList);
        if(transactionsDTOList != null && !transactionsDTOList.isEmpty()){
            return transactionsDTOList.stream().mapToLong(TransactionsDTO::getAmount).sum();
        }else {
            throw exception(RELATIONSHIP_NOT_EXISTS);
        }
    }


    /**
     * 通过代理商id查询该代理商通过的价格模板并根据类型返回模板list
     * @param templateType
     * @return
     */
    @TenantIgnore
    @Override
    public List<NewTemplateRelationDTO> getTypeNewTemplate(Long templateType) {
        AgentProfileDO agentProfileDO = agentProfileMapper.getAgentIdByMemberId(SecurityFrameworkUtils.getLoginUserId());
        return newTemplateApi.getTypeTemplate(agentProfileDO.getId(),templateType);
    }

    /**
     * 统计所有代理商及其团队(直接下级)活跃度信息
     * @return
     */
    @Override
    @TenantIgnore
    public List<AgentActivityDTO> getAllAgentActivity() {
        //获取所有代理商id列表
        List<Long> agentIdList = agentProfileMapper.selectAgentIdList();
        //获取所有代理商及其团队活跃度
        List<AgentActivityDTO> agentActivityDTOS = orderCountsApi.getAgentActivity(agentIdList);
        return agentActivityDTOS;
    }

    /**
     * 获取代理商活跃度数据
     * @param agentId
     * @return
     */
    @Override
    public AgentActivityVO getAgentActivity(Long agentId) {
        //创建该代理商列表
        List<Long> agent = new ArrayList<>();
        agent.add(agentId);
        //查询活跃度数据
        List<AgentActivityDTO> list = orderCountsApi.getAgentActivity(agent);
        return BeanUtils.toBean(list.get(0),AgentActivityVO.class);
    }



    /**
     * 创建邀请链接
     * @param templateId
     * @return
     */
    @Override
    public RegisterDTO createUrl(String key,Long templateId) {
        //用注册用的DTO存储推荐人创建的部分注册信息，用于扫码后注册信息补全的注册
        RegisterDTO registerDTO =new RegisterDTO()
                .setAncestorId(agentProfileMapper.getAgentIdByMemberId(SecurityFrameworkUtils.getLoginUserId()).getId())
                .setTemplateId(templateId)
                .setConfigValue(configApi.getConfigValueByKey(key));
        //校验模板是否存在
        if(newTemplateApi.getNewTemplateById(templateId)==null){
            throw exception(TEMPLATE_NOT_EXISTS);
        }
        return registerDTO;
    }
}