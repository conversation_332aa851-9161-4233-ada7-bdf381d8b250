package com.yunyi.express2b.module.agent.controller.admin.workbench.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 展示代理商总数,下单代理商数,代理商直属下级数,代理商间接下级数
 *
 * <AUTHOR>
 * @since 17
 * @version 1.0
 * @since 2025/5/26 下午2:29
 */
@Schema(description = "工作台接口 - 代理商总数,下单代理商数,代理商直属下级数,代理商间接下级数")
@Data
public class AgentTotalVo {

    @Schema(description = "代理商总数")
    private Long total;

    @Schema(description = "下单代理商数")
    private Long orderAgentCount;

    @Schema(description = "代理商直属下级数")
    private Long directCount;

    @Schema(description = "代理商间接下级数")
    private Long indirectCount;

    @Schema(description = "代理商备注")
    private String remark;
}