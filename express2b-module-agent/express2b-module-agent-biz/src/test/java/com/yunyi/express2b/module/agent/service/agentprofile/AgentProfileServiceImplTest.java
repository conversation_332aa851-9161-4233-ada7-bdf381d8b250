package com.yunyi.express2b.module.agent.service.agentprofile;

import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.test.core.ut.BaseDbUnitTest;
import com.yunyi.express2b.module.agent.controller.admin.agentprofile.vo.AgentProfilePageReqVO;
import com.yunyi.express2b.module.agent.controller.admin.agentprofile.vo.AgentProfileSaveReqVO;
import com.yunyi.express2b.module.agent.dal.dataobject.agentprofile.AgentProfileDO;
import com.yunyi.express2b.module.agent.dal.mysql.agentprofile.AgentProfileMapper;
import com.yunyi.express2b.module.agent.dal.mysql.relationship.RelationshipMapper;
import com.yunyi.express2b.module.agent.service.relationship.RelationshipService;
import com.yunyi.express2b.module.express.api.order.OrderCountsApi;
import com.yunyi.express2b.module.infra.api.config.ConfigApi;
import com.yunyi.express2b.module.infra.api.file.FileApi;
import com.yunyi.express2b.module.pricing.api.NewTemplateApi;
import com.yunyi.express2b.module.pricing.api.PricingApiflow;
import com.yunyi.express2b.module.pricing.api.TemplateApi;
import com.yunyi.express2b.module.system.api.tenant.TenantApi;
import com.yunyi.express2b.module.wallet.api.TransationsApi.TransactionsApi;
import com.yunyi.express2b.module.wallet.api.WalletApi.WalletApi;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Primary;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import static com.yunyi.express2b.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static com.yunyi.express2b.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static com.yunyi.express2b.framework.test.core.util.AssertUtils.assertPojoEquals;
import static com.yunyi.express2b.framework.test.core.util.AssertUtils.assertServiceException;
import static com.yunyi.express2b.framework.test.core.util.RandomUtils.randomLongId;
import static com.yunyi.express2b.framework.test.core.util.RandomUtils.randomPojo;
import static com.yunyi.express2b.module.agent.enums.ErrorCodeConstants.PROFILE_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link AgentProfileServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import({AgentProfileServiceImpl.class, AgentProfileMapper.class, PricingApiflow.class})
public class AgentProfileServiceImplTest extends BaseDbUnitTest {

    @Resource
    private AgentProfileServiceImpl profileService;

    @Resource
    private AgentProfileMapper profileMapper;

    @MockitoBean
    private PricingApiflow pricingApiflow;
    @MockitoBean
    private WalletApi walletApi;
    @MockitoBean
    private TenantApi tenantApi;
    @MockitoBean
    private FileApi fileApi;
    @MockitoBean
    private RelationshipService relationshipService;
    @MockitoBean
    private ConfigApi configApi;
    @MockitoBean
    private RelationshipMapper relationshipMapper;
    @MockitoBean
    private TemplateApi templateApi;
    @MockitoBean
    private TransactionsApi transactionsApi;
    @MockitoBean
    private OrderCountsApi orderCountsApi;
    @MockitoBean
    private NewTemplateApi newTemplateApi;
    @Test
    public void testCreateProfile_success() {
        // 准备参数
        AgentProfileSaveReqVO createReqVO = randomPojo(AgentProfileSaveReqVO.class).setId(null);

        // 调用
        Long profileId = profileService.createProfile(createReqVO);
        // 断言
        assertNotNull(profileId);
        // 校验记录的属性是否正确
        AgentProfileDO profile = profileMapper.selectById(profileId);
        assertPojoEquals(createReqVO, profile, "id");
    }

    @Test
    public void testUpdateProfile_success() {
        // mock 数据
        AgentProfileDO dbProfile = randomPojo(AgentProfileDO.class);
        profileMapper.insert(dbProfile);// @Sql: 先插入出一条存在的数据
        // 准备参数
        AgentProfileSaveReqVO updateReqVO = randomPojo(AgentProfileSaveReqVO.class, o -> {
            o.setId(dbProfile.getId()); // 设置更新的 ID
        });

        // 调用
        profileService.updateProfile(updateReqVO);
        // 校验是否更新正确
        AgentProfileDO profile = profileMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, profile);
    }

    @Test
    public void testUpdateProfile_notExists() {
        // 准备参数
        AgentProfileSaveReqVO updateReqVO = randomPojo(AgentProfileSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> profileService.updateProfile(updateReqVO), PROFILE_NOT_EXISTS);
    }

    @Test
    public void testDeleteProfile_success() {
        // mock 数据
        AgentProfileDO dbProfile = randomPojo(AgentProfileDO.class);
        profileMapper.insert(dbProfile);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbProfile.getId();

        // 调用
        profileService.deleteProfile(id);
        // 校验数据不存在了
        assertNull(profileMapper.selectById(id));
    }

    @Test
    public void testDeleteProfile_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> profileService.deleteProfile(id), PROFILE_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetProfilePage() {
        // mock 数据
        AgentProfileDO dbProfile = randomPojo(AgentProfileDO.class, o -> { // 等会查询到
            o.setMemberId(null);
            o.setName(null);
            o.setMobile(null);
            o.setEmail(null);
            o.setStatus(null);
            o.setLevelId(null);
            o.setReferrerAgentId(null);
            o.setCustomPricingTemplateId(null);
            o.setWalletId(null);
            o.setSystemTenantId(null);
            o.setDirectDownlineCount(null);
            o.setTotalDownlineCount(null);
            o.setCreateTime(null);
        });
        profileMapper.insert(dbProfile);
        // 测试 memberId 不匹配
        profileMapper.insert(cloneIgnoreId(dbProfile, o -> o.setMemberId(null)));
        // 测试 name 不匹配
        profileMapper.insert(cloneIgnoreId(dbProfile, o -> o.setName(null)));
        // 测试 mobile 不匹配
        profileMapper.insert(cloneIgnoreId(dbProfile, o -> o.setMobile(null)));
        // 测试 email 不匹配
        profileMapper.insert(cloneIgnoreId(dbProfile, o -> o.setEmail(null)));
        // 测试 status 不匹配
        profileMapper.insert(cloneIgnoreId(dbProfile, o -> o.setStatus(null)));
        // 测试 levelId 不匹配
        profileMapper.insert(cloneIgnoreId(dbProfile, o -> o.setLevelId(null)));
        // 测试 referrerAgentId 不匹配
        profileMapper.insert(cloneIgnoreId(dbProfile, o -> o.setReferrerAgentId(null)));
        // 测试 customPricingTemplateId 不匹配
        profileMapper.insert(cloneIgnoreId(dbProfile, o -> o.setCustomPricingTemplateId(null)));
        // 测试 walletId 不匹配
        profileMapper.insert(cloneIgnoreId(dbProfile, o -> o.setWalletId(null)));
        // 测试 systemTenantId 不匹配
        profileMapper.insert(cloneIgnoreId(dbProfile, o -> o.setSystemTenantId(null)));
        // 测试 directDownlineCount 不匹配
        profileMapper.insert(cloneIgnoreId(dbProfile, o -> o.setDirectDownlineCount(null)));
        // 测试 totalDownlineCount 不匹配
        profileMapper.insert(cloneIgnoreId(dbProfile, o -> o.setTotalDownlineCount(null)));
        // 测试 createTime 不匹配
        profileMapper.insert(cloneIgnoreId(dbProfile, o -> o.setCreateTime(null)));
        // 准备参数
        AgentProfilePageReqVO reqVO = new AgentProfilePageReqVO();
        reqVO.setMemberId(null);
        reqVO.setName(null);
        reqVO.setMobile(null);
        reqVO.setEmail(null);
        reqVO.setStatus(null);
        reqVO.setLevelId(null);
        reqVO.setReferrerAgentId(null);
        reqVO.setCustomPricingTemplateId(null);
        reqVO.setWalletId(null);
        reqVO.setSystemTenantId(null);
        reqVO.setDirectDownlineCount(null);
        reqVO.setTotalDownlineCount(null);
        reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

        // 调用
        PageResult<AgentProfileDO> pageResult = profileService.getProfilePage(reqVO);
        // 断言
        assertEquals(1, pageResult.getTotal());
        assertEquals(1, pageResult.getList().size());
        assertPojoEquals(dbProfile, pageResult.getList().get(0));
    }

}