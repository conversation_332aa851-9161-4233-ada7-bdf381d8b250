<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yunyi.express2b.module.pricing.dal.mysql.template.TemplateMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
     <!--获取定价模板详细信息，包括关联的代理商和基础价格信息 -->
<!--    <select id="selecttemplateId" resultType="com.yunyi.express2b.module.pricing.dal.dataobject.template.PricingTemplateDetailDTO">-->
<!--       select a.id,a.template_name, a.markup_type, a.first_weight_markup,-->
<!--              a.additional_weight_markup, a.platform_first_price,-->
<!--              a.platform_additional_price, a.status, a.description,b.agent_id,-->
<!--              c.name,c.level_id-->
<!--       from pricing_template a-->
<!--       left join pricing_template_agent_relation b on a.id = b.template_id-->
<!--       left join agent_agent_profile c on b.agent_id = c.id-->
<!--        <where>-->
<!--            <if test="id != null">-->
<!--                a.id = #{id}-->
<!--            </if>-->
<!--            <if test="levelId != null">-->
<!--                a.template_name = #{levelId}-->
<!--            </if>-->
<!--        </where>-->
<!--    </select>-->
    <!--定价模板应用公共方法 优先级：特殊定价 > 代理商模板定价 > 代理商自定义定价 > 基础运价-->
<!--    <select id="getApplicationStrategy"-->
<!--            resultType="com.yunyi.express2b.module.pricing.dal.dataobject.template.PricingTemplateDetailDTO">-->
<!--        select a.id,a.template_name, a.markup_type, a.first_weight_markup,-->
<!--               a.additional_weight_markup, a.platform_first_price,-->
<!--               a.platform_additional_price, a.status, a.description-->
<!--        from pricing_template a-->
<!--                 left join agent_agent_profile c on b.custom_pricing_template_id = a.id-->
<!--        <where>-->
<!--            <if test="templateId != null">-->
<!--                a.id = #{templateId}-->
<!--            </if>-->
<!--        </where>-->
<!--    </select>-->

    <!--作为代理商我可以查询平台提供的定价模板，了解首重/续重价格规则-->
<!--    <select id="getTemplatePricingRules"-->
<!--            resultType="com.yunyi.express2b.module.pricing.dal.dataobject.template.PricingTemplateDetailDTO">-->
<!--        select a.id,a.template_name, a.markup_type, a.first_weight_markup,-->
<!--               a.additional_weight_markup, a.platform_first_price,-->
<!--               a.platform_additional_price, a.status, a.description-->
<!--        from pricing_template a-->
<!--                 left join pricing_template_agent_relation b on a.id = b.template_id-->
<!--                 left join agent_agent_profile c on b.agent_id = c.id-->
<!--        <where>-->
<!--            <if test="templateId != null">-->
<!--                a.id = #{templateId}-->
<!--            </if>-->
<!--        </where>-->
<!--    </select>-->


</mapper>