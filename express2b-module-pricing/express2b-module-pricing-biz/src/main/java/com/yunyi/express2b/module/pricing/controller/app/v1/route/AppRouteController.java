package com.yunyi.express2b.module.pricing.controller.app.v1.route;


import com.yunyi.express2b.framework.common.pojo.CommonResult;

import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;


import com.yunyi.express2b.module.pricing.controller.admin.route.vo.RoutePageReqVO;
import com.yunyi.express2b.module.pricing.controller.app.v1.route.vo.RouteRespVO;

import com.yunyi.express2b.module.pricing.dal.dataobject.route.RouteDO;
import com.yunyi.express2b.module.pricing.service.route.RouteService;
import io.swagger.v3.oas.annotations.Operation;

import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;

import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


import static com.yunyi.express2b.framework.common.pojo.CommonResult.success;

@Tag(name = "小程序 - 快递全国运价基础")
@RestController
@RequestMapping("/v1/pricing/fare")
@Validated
public class AppRouteController {

    @Resource
    private RouteService routeService;


    /**
     * 获取指定品牌和线路的基础价格信息
     * @param pageReqVO
     * @return
     */
    @GetMapping("/get-linePrice")
    @Operation(summary = " 获取指定品牌和线路的基础价格信息")
    public CommonResult<PageResult<RouteRespVO>> getgetLinePrice(@Valid RoutePageReqVO pageReqVO) {
        PageResult<RouteDO> pageResult = routeService.getRoutePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, RouteRespVO.class));
    }



}