package com.yunyi.express2b.module.pricing.api;


import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.module.agent.api.AgentApi;
import com.yunyi.express2b.module.agent.api.dto.AgentProfileRespDTO;
import com.yunyi.express2b.module.agent.api.dto.DetailShareReqDO;
import com.yunyi.express2b.module.agent.api.dto.RelationShipDTO;
import com.yunyi.express2b.module.agent.api.dto.TemplateDTO;
import com.yunyi.express2b.module.commission.api.CommissionApi;
import com.yunyi.express2b.module.commission.api.vo.DetailSaveReqVO;
import com.yunyi.express2b.module.pricing.api.dto.DetailShareReqDTO;
import com.yunyi.express2b.module.pricing.api.vo.PricingRuleApiPageReqVO;
import com.yunyi.express2b.module.pricing.dal.dataobject.pricingrule.PricingRuleDO;
import com.yunyi.express2b.module.pricing.dal.mysql.pricingrule.PricingRuleMapper;
import com.yunyi.express2b.module.pricing.enums.ErrorCodeConstants;
import com.yunyi.express2b.module.pricing.service.pricingrule.PricingRuleService;
import com.yunyi.express2b.module.wallet.api.TransationsApi.TransactionsApi;
import com.yunyi.express2b.module.wallet.api.WalletApi.WalletApi;
import com.yunyi.express2b.module.wallet.api.WalletApi.dto.WalletDetailsDTO;
import com.yunyi.express2b.module.wallet.controller.admin.transactions.vo.TransactionsSaveReqVO;
import com.yunyi.express2b.module.wallet.enums.WalletTransactionEnum;
import com.yunyi.express2b.module.wallet.enums.WalletTransactionTypeEnum;
import com.yunyi.express2b.module.wallet.service.transactions.TransactionsService;
import com.yunyi.framework.api.login.utils.TransactionNoGenerator;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.util.Date;

import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.module.wallet.enums.ErrorCodeConstants.WALLET_NOT_EXISTS;


/**
 * 分润/差价 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class PricingUtilsApiServiceImpl implements PricingUtilsApiService {

    @Resource
    private CommissionApi commissionApi;
    @Resource
    private AgentApi agentProfileQueryApi;
    @Resource
    private PricingRuleService pricingRuleService;
    @Resource
    private PricingRuleMapper pricingRuleMapper;
    @Resource
    private TransactionsService transactionsService;
    @Resource
    private NewTemplateApi newTemplateApi;
    @Resource
    private WalletApi walletApi;


    /**
     * 分润公共方法
     *
     * @param reqDO 分润请求对象
     * @return 分润明细ID
     */

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<String> sharebenefit(DetailShareReqDTO reqDO) {
        log.info("接收进行分润的参数：{}", reqDO);
        // 0. 参数非空校验
        if (reqDO == null || reqDO.getAgentId() == null) {
            throw exception(ErrorCodeConstants.PARAMETER_NULL);
        }
        CommonResult<String> Result = new CommonResult<>();
        try {
            // 1. 检查代理商等级
            Integer agentLevelId = Integer.valueOf(agentProfileQueryApi.getAgentLevelId().toString());
            log.info("agentLevelId:{}", agentLevelId);
            switch (agentLevelId) {
                case 3:
                    Result.setData(agentLevelId.toString());
                    Result.setMsg("V3等级不分润");
                    return Result;
                case 1:
                    CommonResult<Long> CommonResult = handleV1AgentShare(BeanUtils.toBean(reqDO, DetailShareReqDO.class));
                    Result.setData(CommonResult.getData().toString());
                    Result.setMsg("V2等级分润");
                    return Result;
                case 2:
                    CommonResult<Long> longCommonResult = handleV2AgentShare(BeanUtils.toBean(reqDO, DetailShareReqDO.class));
                    Result.setData(longCommonResult.toString());
                    Result.setMsg("V3等级分润");
                    return Result;
                default:
                    Result.setData(agentLevelId.toString());
                    Result.setMsg("未知的代理商等级");
                    return Result;
            }
        } catch (Exception e) {
            // 返回包含错误信息的结果
            return CommonResult.error(500, "分润处理失败: " + e.getLocalizedMessage());
        }
    }


    /**
     * 处理V1代理商分润
     */
    private CommonResult<Long> handleV1AgentShare(DetailShareReqDO reqDO) {
        // 0. 参数非空校验
        if (reqDO == null || reqDO.getAgentId() == null) {
            throw exception(ErrorCodeConstants.PARAMETER_NULL);
        }
        // 如果是v1级别的 需要查出最近的上级v2和最近的上级v3
        // 获取V1代理商模板
        TemplateDTO v1Template = BeanUtils.toBean(newTemplateApi.getTemplateIdByAgentId(reqDO.getAgentId()), TemplateDTO.class);
        if (v1Template == null) {
            throw exception(ErrorCodeConstants.AGENT_TEMPLATE_DOES_NOT_EXIST);
        }
        // 获取V1上级代理商信息
        RelationShipDTO v2Relation = agentProfileQueryApi.getAgentProfileByAgentIdAndLevelId(reqDO.getAgentId(), 2L);
        log.info("v2Relation:{}", v2Relation);
        if (v2Relation == null || v2Relation.getNearestSpecificLevelAncestorId() == null) {
            //throw exception(ErrorCodeConstants.UPPER_AGENT_NOT_FOUND);
            return CommonResult.error(ErrorCodeConstants.UPPER_AGENT_NOT_FOUND);
        }
        Long v2AgentId = v2Relation.getNearestSpecificLevelAncestorId();
        TemplateDTO v2Template = BeanUtils.toBean(newTemplateApi.getTemplateIdByAgentId(v2AgentId), TemplateDTO.class);
        if (v2Template == null) {
            throw exception(ErrorCodeConstants.AGENT_TEMPLATE_DOES_NOT_EXIST);
        }


        // 获取V2上级代理商信息
        RelationShipDTO v3Relation = agentProfileQueryApi.getAgentProfileByAgentIdAndLevelId(v2AgentId, 3L);
        if (v3Relation == null || v3Relation.getNearestSpecificLevelAncestorId() == null) {
//            throw exception(ErrorCodeConstants.UPPER_AGENT_NOT_FOUND);
            return CommonResult.error(ErrorCodeConstants.UPPER_AGENT_NOT_FOUND);

        }
        Long v3AgentId = v3Relation.getNearestSpecificLevelAncestorId();
        TemplateDTO v3Template = BeanUtils.toBean(newTemplateApi.getTemplateIdByAgentId(v3AgentId), TemplateDTO.class);
        if (v3Template == null) {
            throw exception(ErrorCodeConstants.AGENT_TEMPLATE_DOES_NOT_EXIST);
        }

        // 计算V1到V2的分润
        Long v1ToV2Detail = calculateAndSaveShare(reqDO, v1Template, v2Template, v2AgentId);
        log.info("计算V1到V2的分润:{},v2代理商id：{}", v1ToV2Detail, v2AgentId);

        // 计算V2到V3的分润 没返回值
        Long v2ToV3Detail = calculateAndSaveSharev2(reqDO, v2Template, v3Template, v3AgentId);
        log.info("计算V2到V3的分润:{},v3代理商id：{}", v2ToV3Detail, v3AgentId);

        return CommonResult.success(v1ToV2Detail);
    }

    /**
     * 处理V2代理商分润
     */
    private CommonResult<Long> handleV2AgentShare(DetailShareReqDO reqDO) {
        // 0. 参数非空校验
        if (reqDO == null || reqDO.getAgentId() == null) {
            throw exception(ErrorCodeConstants.PARAMETER_NULL);
        }
        // 获取V2代理商模板
        TemplateDTO v2Template = BeanUtils.toBean(newTemplateApi.getTemplateIdByAgentId(reqDO.getAgentId()), TemplateDTO.class);
        if (v2Template == null) {
            throw exception(ErrorCodeConstants.AGENT_TEMPLATE_DOES_NOT_EXIST);
        }

        // 获取V2上级代理商信息
        RelationShipDTO v3Relation = agentProfileQueryApi.getAgentProfileByAgentIdAndLevelId(reqDO.getAgentId(), 2L);
        if (v3Relation == null || v3Relation.getNearestSpecificLevelAncestorId() == null) {
//            throw exception(ErrorCodeConstants.UPPER_AGENT_NOT_FOUND);
            return CommonResult.error(ErrorCodeConstants.UPPER_AGENT_NOT_FOUND);

        }
        //获取v3代理商id
        Long v3AgentId = v3Relation.getNearestSpecificLevelAncestorId();
        TemplateDTO v3Template = BeanUtils.toBean(newTemplateApi.getTemplateIdByAgentId(v3AgentId), TemplateDTO.class);
        if (v3Template == null) {
            throw exception(ErrorCodeConstants.AGENT_TEMPLATE_DOES_NOT_EXIST);
        }
        // 计算V2到V3的分润
        Long detail = calculateAndSaveSharev2(reqDO, v2Template, v3Template, v3AgentId);

        return CommonResult.success(detail);
    }

    /**
     * 计算并保存分润
     */
    private Long calculateAndSaveShare(DetailShareReqDO reqDO, TemplateDTO lowerTemplate, TemplateDTO higherTemplate, Long targetAgentId) {
        // 计算首重分润
        int firstWeightDifference = higherTemplate.getPlatformFirstPriceV2() - lowerTemplate.getPlatformFirstPriceV1();

        // 计算续重分润
        int additionalWeightDifference = Math.toIntExact(reqDO.getWeight() - 1) *
                (higherTemplate.getPlatformAdditionalPriceV2() - lowerTemplate.getPlatformAdditionalPriceV1());
        int totalShare = 0;
        if (additionalWeightDifference > 0) {
            // 计算总分润
            totalShare = firstWeightDifference + additionalWeightDifference;
        } else {
            // 计算总分润
            totalShare = firstWeightDifference;
        }


        log.info("开始插入分润明细表");
        // 创建分润明细
        DetailSaveReqVO detailSaveReqVO = new DetailSaveReqVO();
        detailSaveReqVO.setOrderId(reqDO.getOrderId());
        detailSaveReqVO.setOrderNo(reqDO.getOrderNo());
        detailSaveReqVO.setTargetId(targetAgentId);
        detailSaveReqVO.setTargetType(2); // 分润目标类型：代理商
        detailSaveReqVO.setAmount(totalShare);
        detailSaveReqVO.setStatus(1);
        detailSaveReqVO.setCalculateTime(LocalDateTime.now());
        log.info("记录V2最终要插入明细表的对象：{}", detailSaveReqVO);
        Long detail = commissionApi.createDetail(detailSaveReqVO);
        log.info("记录V2的分润明细成功,返回自增id", detail);
        //查看钱包详情
        WalletDetailsDTO walletDetails = walletApi.getWalletDetails(targetAgentId);
        if (ObjectUtil.isEmpty(walletDetails)) {
            throw exception(WALLET_NOT_EXISTS);
        }
        log.info("v2代理商的钱包详情:{}", walletDetails);
        //更新钱包表冻结余额
        log.info("计算利润后，准备更新的v2代理商的冻结余额", totalShare + walletDetails.getFrozenBalance());
        Long aLong = walletApi.updateWallet(targetAgentId, totalShare + walletDetails.getFrozenBalance());
        if (aLong > 0) {
            log.info("更新V2代理商钱包成功");
        }
        //再次查询一下钱包
        WalletDetailsDTO walletDetailsV2 = walletApi.getWalletDetails(targetAgentId);
        log.info("再次查询v2代理商的钱包详情:{}", walletDetailsV2);

        //记录钱包流水
        String payOutsNumber = TransactionNoGenerator.payOutsNumber("TX", "wx", "eb2", new Date(), totalShare);
        TransactionsSaveReqVO transactionsSaveReqVO = TransactionsSaveReqVO.builder()
                .agentId(targetAgentId)
                .amount(totalShare)
                .walletId(walletDetailsV2.getId())
                .balanceBeforeTransaction(walletDetailsV2.getAvailableBalance() + walletDetailsV2.getFrozenBalance())
                .balanceAfterTransaction(walletDetailsV2.getAvailableBalance() + walletDetailsV2.getFrozenBalance())
                .availableBalanceAfterTransaction(walletDetailsV2.getAvailableBalance())
                .frozenBalanceAfterTransaction(totalShare)
//                .availableAt(LocalDateTime.now().plusDays(3))
                .status(WalletTransactionEnum.FROZEN.getStatus())
                .transactionType(WalletTransactionTypeEnum.COMMISSION_EARNED.getStatus())
                .transactionId(payOutsNumber)
                .build();
        transactionsService.createTransactions(transactionsSaveReqVO);
        return detail;
    }

    /**
     * 计算并保存分润
     */
    private Long calculateAndSaveSharev2(DetailShareReqDO reqDO, TemplateDTO lowerTemplate, TemplateDTO
            higherTemplate, Long targetAgentId) {
        // 计算首重分润
        int firstWeightDifference = higherTemplate.getPlatformFirstPriceV3() - lowerTemplate.getPlatformFirstPriceV2();

        // 计算续重分润
        int additionalWeightDifference = Math.toIntExact(reqDO.getWeight() - 1) *
                (higherTemplate.getPlatformAdditionalPriceV3() - lowerTemplate.getPlatformAdditionalPriceV2());
        int totalShare = 0;
        if (additionalWeightDifference > 0) {
            // 计算总分润
            totalShare = firstWeightDifference + additionalWeightDifference;
        } else {
            // 计算总分润
            totalShare = firstWeightDifference;
        }
        log.info("开始插入V3分润明细表");
        // 创建分润明细
        DetailSaveReqVO detailSaveReqVO = new DetailSaveReqVO();
        detailSaveReqVO.setOrderId(reqDO.getOrderId());
        detailSaveReqVO.setOrderNo(reqDO.getOrderNo());
        detailSaveReqVO.setTargetId(targetAgentId);
        detailSaveReqVO.setTargetType(3); // 分润目标类型：代理商
        detailSaveReqVO.setAmount(totalShare);
        detailSaveReqVO.setCalculateTime(LocalDateTime.now());
        detailSaveReqVO.setStatus(1);
        log.info("最终要插入V3明细表的对象：{}", detailSaveReqVO);
        Long detail = commissionApi.createDetail(detailSaveReqVO);
        log.info("插入V3分润明细表成功,返回自增id:{}", detail);

        //查看钱包详情
        WalletDetailsDTO walletDetails = walletApi.getWalletDetails(targetAgentId);
        if (ObjectUtil.isEmpty(walletDetails)) {
            throw exception(WALLET_NOT_EXISTS);
        }
        log.info("v3代理商的钱包详情:{}", walletDetails);
        //更新钱包表冻结余额
        Long aLong = walletApi.updateWallet(targetAgentId, totalShare + walletDetails.getFrozenBalance());
        if (aLong > 0) {
            log.info("更新V3代理商钱包成功");
        }

        //再次查询一下钱包
        WalletDetailsDTO walletDetailsV3 = walletApi.getWalletDetails(targetAgentId);
        log.info("再次查询V3代理商的钱包详情:{}", walletDetailsV3);
        //记录钱包流水
        String payOutsNumber = TransactionNoGenerator.payOutsNumber("TX", "wx", "eb2", new Date(), totalShare);
        TransactionsSaveReqVO transactionsSaveReqVO = TransactionsSaveReqVO.builder()
                .agentId(targetAgentId)
                .amount(totalShare)
                .walletId(walletDetailsV3.getId())
                .balanceBeforeTransaction(walletDetailsV3.getAvailableBalance() + walletDetailsV3.getFrozenBalance())
                .balanceAfterTransaction(walletDetailsV3.getAvailableBalance() + walletDetailsV3.getFrozenBalance())
                .availableBalanceAfterTransaction(walletDetailsV3.getAvailableBalance())
                .frozenBalanceAfterTransaction(totalShare)
                //.availableAt(LocalDateTime.now().plusDays(3))
                .status(WalletTransactionEnum.FROZEN.getStatus())
                .transactionType(WalletTransactionTypeEnum.COMMISSION_EARNED.getStatus())
                .transactionId(payOutsNumber)
                .build();
        transactionsService.createTransactions(transactionsSaveReqVO);
        return detail;
    }


    /**
     * 差价计算公共方法
     *
     * @param reqVO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<PricingRuleApiPageReqVO> difference(PricingRuleApiPageReqVO reqVO) {
        //全局变量
        Integer platformFirstPrice = null;
        Integer platformAdditionalPrice = null;
        log.info("开始执行计算差价的方法，参数为：{}", reqVO);
        // 1. 获取订单数据（假设通过方法参数传入或调用其他API获取）
        Integer orderFirstWeight = reqVO.getFirstWeightMarkup(); // 假设VO中包首含重信息
        Integer orderAdditionalWeight = reqVO.getAdditionalWeightMarkup(); // 假设VO中包含续重信息
        // 2. 根据代理商ID查询定价规则（reqVO中已提供agentId）
        Long agentId = reqVO.getAgentId();
        // 用于查询规则
        Long ruleId = pricingRuleMapper.selectIdByAgentId(agentId);
        log.info("ruleId:{}", ruleId);
        //代理商终端零售价
        PricingRuleDO rule = pricingRuleService.getRule(ruleId);
        log.info("rule:{}", rule);
        // 3. 获取代理商对应的价格（模拟数据，实际应调用服务获取）
        Integer agentFirstPrice = rule.getFirstWeightMarkup(); // 代理商默认首重价格（单位：分）
        Integer agentAdditionalPrice = rule.getAdditionalWeightMarkup(); // 代理商续重价格（单位：分）
        //获取到代理商id
        AgentProfileRespDTO agentProfile = agentProfileQueryApi.getAgentProfile(agentId);
        log.info("获取代理商信息:{}", agentProfile);
        Long levelId = agentProfile.getLevelId();
        TemplateDTO templateDTO = BeanUtils.toBean(newTemplateApi.getTemplateIdByAgentId(agentId), TemplateDTO.class);
        if (levelId == null) {
            throw exception(ErrorCodeConstants.PARAMETER_NULL);
        }
        log.info("---------------------------------------------------------------");
        //根据代理商等级用的那个模板
        switch (levelId.intValue()) {
            case 1:
                platformFirstPrice = templateDTO.getPlatformFirstPriceV1();
                platformAdditionalPrice = templateDTO.getPlatformAdditionalPriceV1();
                break;
            case 2:
                platformFirstPrice = templateDTO.getPlatformFirstPriceV2();
                platformAdditionalPrice = templateDTO.getPlatformAdditionalPriceV2();
                break;
            case 3:
                platformFirstPrice = templateDTO.getPlatformFirstPriceV3();
                platformAdditionalPrice = templateDTO.getPlatformAdditionalPriceV3();
                break;
            default:
                throw exception(ErrorCodeConstants.AGENT_TEMPLATE_DOES_NOT_EXIST);
        }

        log.info("进行差价计算的参数：orderFirstWeight: {}, agentFirstPrice: {}, " +
                        "platformFirstPrice: {}, orderAdditionalWeight: {}, " +
                        "agentAdditionalPrice: {}, platformAdditionalPrice: {}",
                orderFirstWeight, agentFirstPrice, platformFirstPrice, orderAdditionalWeight, agentAdditionalPrice, platformAdditionalPrice);
        double totalDifferencePrice = orderFirstWeight * (agentFirstPrice - platformFirstPrice) +
                orderAdditionalWeight * (agentAdditionalPrice - platformAdditionalPrice);
        log.info("总差价：{}", totalDifferencePrice);
        int chaJiaMoney = NumberUtil.roundDown(totalDifferencePrice, 0).intValue();

        // 将差价添加到钱包流水表（需调用钱包服务接口，此处为模拟逻辑）
        //查看钱包详情
        WalletDetailsDTO walletDetails = walletApi.getWalletDetails(agentId);
        if (ObjectUtil.isEmpty(walletDetails)) {
            throw exception(WALLET_NOT_EXISTS);
        }
        log.info("钱包详情:{}", walletDetails);
        //更新钱包的冻结金额
        try {
            Long aLong = walletApi.updateWallet(agentId, chaJiaMoney + walletDetails.getFrozenBalance());
            log.info("更新钱包金额成功");
        } catch (Exception e) {
            log.error("更新钱包金额失败");
        }
        // 6. 将差价添加到钱包流水表（需调用钱包服务接口）

        //再次查询一下钱包
        WalletDetailsDTO walletDetailsDTO = walletApi.getWalletDetails(agentId);
        log.info("再次查询当前代理商的钱包详情:{}", walletDetailsDTO);


        //生成交易编号
        String payOutsNumber = TransactionNoGenerator.payOutsNumber("TX", "wx", "eb2", new Date(), agentFirstPrice);
        log.info("payOutsNumber:{}", payOutsNumber);
        TransactionsSaveReqVO transactionsSaveReqVO = TransactionsSaveReqVO.builder()
                .agentId(agentId)
                .amount(chaJiaMoney)
                .walletId(agentProfile.getWalletId())
                //交易前(可用余额+冻结余额)
                .balanceBeforeTransaction(walletDetailsDTO.getAvailableBalance() + walletDetailsDTO.getFrozenBalance())
                //交易后(可用余额+冻结余额)
                .balanceAfterTransaction(walletDetailsDTO.getAvailableBalance() + walletDetailsDTO.getFrozenBalance())
                //交易后可用余额
                .availableBalanceAfterTransaction(walletDetailsDTO.getAvailableBalance())
                //本次我的差价
                .frozenBalanceAfterTransaction(chaJiaMoney)
                //.availableAt(LocalDateTime.now().plusDays(3))
                .status(WalletTransactionEnum.FROZEN.getStatus())
                .transactionType(WalletTransactionTypeEnum.COMMISSION_EARNED.getStatus())
                .transactionId(payOutsNumber)
                .build();
        transactionsService.createTransactions(transactionsSaveReqVO);

        // 返回结果
        return CommonResult.success(reqVO);

    }

}