package com.yunyi.express2b.module.pricing.service.route;


import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.yunyi.express2b.module.pricing.controller.admin.route.vo.*;
import com.yunyi.express2b.module.pricing.dal.dataobject.route.RouteDO;
import com.yunyi.express2b.framework.common.pojo.PageResult;

import com.yunyi.express2b.framework.common.util.object.BeanUtils;

import com.yunyi.express2b.module.pricing.dal.mysql.route.RouteMapper;

import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.module.pricing.enums.ErrorCodeConstants.*;

/**
 * 快递全国运价基础 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class RouteServiceImpl implements RouteService {

    @Resource
    private RouteMapper routeMapper;

    /**
     * 创建快递全国运价基础
     * @param createReqVO 创建信息
     * @return
     */
    @Override
    public Long createRoute(RouteSaveReqVO createReqVO) {
        // 插入
        RouteDO route = BeanUtils.toBean(createReqVO, RouteDO.class);
        routeMapper.insert(route);
        // 返回
        return route.getId();
    }

    /**
     * 更新快递全国运价基础
     * @param updateReqVO 更新信息
     */
    @Override
    public void updateRoute(RouteSaveReqVO updateReqVO) {
        // 校验存在
        validateRouteExists(updateReqVO.getId());
        // 更新
        RouteDO updateObj = BeanUtils.toBean(updateReqVO, RouteDO.class);
        routeMapper.updateById(updateObj);
    }

    /**
     * 删除快递全国运价基础
     * @param id 编号
     */
    @Override
    public void deleteRoute(Long id) {
        // 校验存在
        validateRouteExists(id);
        // 删除
        routeMapper.deleteById(id);
    }

    /**
     * 校验指定快递全国运价基础是否存在
     * @param id
     */
    private void validateRouteExists(Long id) {
        if (routeMapper.selectById(id) == null) {
            throw exception(ROUTE_NOT_EXISTS);
        }
    }

    /**
     * 获得快递全国运价基础
     * @param id 编号
     * @return
     */
    @Override
    public RouteDO getRoute(Long id) {
        return routeMapper.selectById(id);
    }

    /**
     * 获得快递全国运价基础分页
     * @param pageReqVO 分页查询
     * @return
     */
    @Override
    public PageResult<RouteDO> getRoutePage(RoutePageReqVO pageReqVO) {
        // 调用 mapper 方法获取分页结果
        PageResult<RouteDO> routeDOPageResult = routeMapper.selectPage(pageReqVO);
        // 使用 forEach 方法遍历分页结果中的每个 RouteDO 对象
        routeDOPageResult.getList().forEach(routeDO -> {
            if (routeDO.getBrandKey() == null) {
                // 抛出异常并附带当前对象的关键信息
                throw exception(BRAND_NOT_FOUND, "RouteDO ID: " + routeDO.getId());
            }
            if (routeDO.getLineKey() == null) {
                // 抛出异常并附带当前对象的关键信息
                throw exception(LINE_NOT_FOUND, "RouteDO ID: " + routeDO.getId());
            }
        });
        return routeDOPageResult;
    }

}