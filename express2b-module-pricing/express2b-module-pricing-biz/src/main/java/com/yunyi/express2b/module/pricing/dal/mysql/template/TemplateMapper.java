package com.yunyi.express2b.module.pricing.dal.mysql.template;

import java.util.*;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yunyi.express2b.framework.mybatis.core.mapper.BaseMapperX;
import com.yunyi.express2b.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.yunyi.express2b.module.agent.api.dto.TemplateDTO;
import com.yunyi.express2b.module.agent.dal.dataobject.agentprofile.AgentProfileDO;
import com.yunyi.express2b.module.pricing.api.dto.NewTemplateRelationDTO;
import com.yunyi.express2b.module.pricing.api.dto.TemplateRelationDTO;
import com.yunyi.express2b.module.pricing.controller.admin.template.newvo.NewTemplatePageReqVO;
import com.yunyi.express2b.module.pricing.dal.dataobject.newtemplate.NewTemplateDO;
import com.yunyi.express2b.module.pricing.dal.dataobject.relationship.TemplateAgentRelationDO;
import com.yunyi.express2b.module.pricing.dal.dataobject.template.PricingTemplateDetailDTO;
import com.yunyi.express2b.module.pricing.dal.dataobject.template.TemplateDO;
import org.apache.ibatis.annotations.Mapper;
import com.yunyi.express2b.module.pricing.controller.admin.template.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 定价模板 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TemplateMapper extends BaseMapperX<TemplateDO> {

    default PageResult<TemplateDO> selectPage(TemplatePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TemplateDO>()
                .likeIfPresent(TemplateDO::getTemplateName, reqVO.getTemplateName())
                .eqIfPresent(TemplateDO::getMarkupType, reqVO.getMarkupType())
                .eqIfPresent(TemplateDO::getFirstWeightMarkup, reqVO.getFirstWeightMarkup())
                .eqIfPresent(TemplateDO::getAdditionalWeightMarkup, reqVO.getAdditionalWeightMarkup())
                .eqIfPresent(TemplateDO::getPlatformFirstPrice, reqVO.getPlatformFirstPrice())
                .eqIfPresent(TemplateDO::getPlatformAdditionalPrice, reqVO.getPlatformAdditionalPrice())
                .eqIfPresent(TemplateDO::getStatus, reqVO.getStatus())
                .eqIfPresent(TemplateDO::getDescription, reqVO.getDescription())
                .betweenIfPresent(TemplateDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(TemplateDO::getId));
    }

    /**
     * 通过代理商id查询模板
     * @param agentId
     * @return
     */
    default TemplateDO selectByAgentId(Long agentId){
        return selectOne(new MPJLambdaWrapperX<TemplateDO>()
                .select(TemplateDO::getId,
                        TemplateDO::getTemplateName,
                        TemplateDO::getMarkupType,
                        TemplateDO::getFirstWeightMarkup,
                        TemplateDO::getAdditionalWeightMarkup,
                        TemplateDO::getPlatformFirstPrice,
                        TemplateDO::getPlatformAdditionalPrice,
                        TemplateDO::getStatus,
                        TemplateDO::getDescription
                )
                .leftJoin(TemplateAgentRelationDO.class,TemplateAgentRelationDO::getTemplateId,TemplateDO::getId)
                .eq(TemplateAgentRelationDO::getAgentId,agentId)
        );
    }


    //PricingTemplateDetailDTO selecttemplateId(PricingTemplateDetailDTO pricingTemplateDetailDTO );
    default PricingTemplateDetailDTO selecttemplateId(PricingTemplateDetailDTO pricingTemplateDetailDTO){
        return selectJoinOne(PricingTemplateDetailDTO.class,new MPJLambdaWrapperX<TemplateDO>()
                .select(TemplateDO::getId,
                        TemplateDO::getTemplateName,
                        TemplateDO::getMarkupType,
                        TemplateDO::getFirstWeightMarkup,
                        TemplateDO::getAdditionalWeightMarkup,
                        TemplateDO::getPlatformFirstPrice,
                        TemplateDO::getPlatformAdditionalPrice,
                        TemplateDO::getStatus,
                        TemplateDO::getDescription)
                .leftJoin(TemplateAgentRelationDO.class,TemplateAgentRelationDO::getTemplateId,TemplateDO::getId)
                .selectAs(TemplateAgentRelationDO::getAgentId, PricingTemplateDetailDTO::getAgentId)
                .leftJoin(AgentProfileDO.class,AgentProfileDO::getId,TemplateAgentRelationDO::getAgentId)
                .selectAs(AgentProfileDO::getName,PricingTemplateDetailDTO::getName)
                .selectAs(AgentProfileDO::getLevelId,PricingTemplateDetailDTO::getLevelId)
                .eqIfExists(TemplateDO::getId,pricingTemplateDetailDTO.getId())
                .eqIfExists(TemplateDO::getTemplateName,pricingTemplateDetailDTO.getLevelId())
        );

    }

    //PricingTemplateDetailDTO getApplicationStrategy(Long templateId);
    default  PricingTemplateDetailDTO getApplicationStrategy(Long templateId){
        return selectJoinOne(PricingTemplateDetailDTO.class,new MPJLambdaWrapperX<TemplateDO>()
                .select(TemplateDO::getId,
                        TemplateDO::getTemplateName,
                        TemplateDO::getMarkupType,
                        TemplateDO::getFirstWeightMarkup,
                        TemplateDO::getAdditionalWeightMarkup,
                        TemplateDO::getPlatformFirstPrice,
                        TemplateDO::getPlatformAdditionalPrice,
                        TemplateDO::getStatus,
                        TemplateDO::getDescription
                )
                .leftJoin(AgentProfileDO.class,AgentProfileDO::getId,TemplateDO::getId)
                .eqIfExists(TemplateDO::getId,templateId)
        );

    }


    //List<PricingTemplateDetailDTO> getTemplatePricingRules(Long templateId);

    default List<PricingTemplateDetailDTO> getTemplatePricingRules(Long templateId){
        return selectJoinList(PricingTemplateDetailDTO.class,new MPJLambdaWrapperX<TemplateDO>()
                .select(TemplateDO::getId,
                        TemplateDO::getTemplateName,
                        TemplateDO::getMarkupType,
                        TemplateDO::getFirstWeightMarkup,
                        TemplateDO::getAdditionalWeightMarkup,
                        TemplateDO::getPlatformFirstPrice,
                        TemplateDO::getPlatformAdditionalPrice,
                        TemplateDO::getDescription)
                .leftJoin(TemplateAgentRelationDO.class,TemplateAgentRelationDO::getTemplateId,TemplateDO::getId)
                .leftJoin(AgentProfileDO.class,AgentProfileDO::getId,TemplateAgentRelationDO::getAgentId)
                .eqIfExists(TemplateDO::getId,templateId)
        );
    }

    /**
     * 根据代理商ID查询模版信息
     * @param agentId
     * @return
     */
    default PageResult<TemplateDO> selectTemplatePage(Long agentId){
        TemplatePageReqVO templatePageReqVO =  new TemplatePageReqVO();
        return selectPage(templatePageReqVO,new MPJLambdaWrapperX<TemplateDO>()
                .select(
                        TemplateDO::getId,TemplateDO::getId,
                        TemplateDO::getTemplateName,
                        TemplateDO::getMarkupType,
                        TemplateDO::getTemplateName,
                        TemplateDO::getMarkupType,
                        TemplateDO::getFirstWeightMarkup,
                        TemplateDO::getId,TemplateDO::getId,
                        TemplateDO::getTemplateName,
                        TemplateDO::getMarkupType,
                        TemplateDO::getFirstWeightMarkup,
                        TemplateDO::getAdditionalWeightMarkup,
                        TemplateDO::getPlatformFirstPrice,
                        TemplateDO::getPlatformAdditionalPrice,
                        TemplateDO::getStatus,
                        TemplateDO::getDescription
                )
                .leftJoin(TemplateAgentRelationDO.class,TemplateAgentRelationDO::getTemplateId,TemplateDO::getId)
                .eq(TemplateAgentRelationDO::getAgentId,agentId)
        );
    }


    /**
     * 查询该代理商可为下级选择的所有模板
     * @param agentId
     * @return
     */
    default List<TemplateRelationDTO> getTemplateAndUserSum(Long agentId){
        return selectJoinList(TemplateRelationDTO.class,new MPJLambdaWrapperX<TemplateDO>()
                .select(TemplateDO::getId,
                        TemplateDO::getTemplateName,
                        TemplateDO::getFirstWeightMarkup,
                        TemplateDO::getAdditionalWeightMarkup,
                        TemplateDO::getCreateTime,
                        TemplateDO::getMarkupType)
                .leftJoin(TemplateAgentRelationDO.class,TemplateAgentRelationDO::getTemplateId,TemplateDO::getId)
                .eq(TemplateAgentRelationDO::getAgentId,agentId)
                .leftJoin(AgentProfileDO.class,AgentProfileDO::getCustomPricingTemplateId,TemplateDO::getId)
                .selectAs("COUNT(t2.id)",TemplateRelationDTO::getUserSum)
                .groupBy(TemplateDO::getId)
        );
    }



    default List<TemplateRelationDTO> getTemplateByType(Long agentId,Long templateType){
        return selectJoinList(TemplateRelationDTO.class,new MPJLambdaWrapperX<TemplateDO>()
                .select(TemplateDO::getId,
                        TemplateDO::getTemplateName,
                        TemplateDO::getFirstWeightMarkup,
                        TemplateDO::getAdditionalWeightMarkup,
                        TemplateDO::getCreateTime,
                        TemplateDO::getMarkupType)
                .eq(TemplateDO::getMarkupType, templateType)
                .leftJoin(TemplateAgentRelationDO.class,TemplateAgentRelationDO::getTemplateId,TemplateDO::getId)
                .eq(TemplateAgentRelationDO::getAgentId,agentId)
                .leftJoin(AgentProfileDO.class,AgentProfileDO::getCustomPricingTemplateId,TemplateDO::getId)
                .selectAs("COUNT(t2.id)",TemplateRelationDTO::getUserSum)
                .groupBy(TemplateDO::getId)
        );
    }


}