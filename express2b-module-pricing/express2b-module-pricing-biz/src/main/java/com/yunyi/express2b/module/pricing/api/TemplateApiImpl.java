package com.yunyi.express2b.module.pricing.api;

import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.module.agent.api.AgentApi;
import com.yunyi.express2b.module.agent.api.dto.AgentInfoDTO;
import com.yunyi.express2b.module.pricing.api.dto.TemplateApiDTO;
import com.yunyi.express2b.module.pricing.api.dto.TemplateRelationDTO;
import com.yunyi.express2b.module.pricing.dal.dataobject.template.TemplateDO;
import com.yunyi.express2b.module.pricing.dal.mysql.template.TemplateMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.module.agent.enums.ErrorCodeConstants.AGENT_NOT_EXISTS;

/**
 * TemplateApi的实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.8
 * @since 2025/5/23 13:20
 */
@Service
public class TemplateApiImpl implements TemplateApi{

    @Resource
    private TemplateMapper templateMapper;

    @Resource
    private AgentApi agentApi;

    /**
     * 通过代理商ID获取模板ID
     * @param agentId 代理商ID
     * @return
     */
    @Override
    public TemplateApiDTO getTemplateIdByAgentId(Long agentId) {
//      根据代理商ID到代理商档案表中获取到当前代理商正在使用的默认ID
        AgentInfoDTO agentProfile = agentApi.getAgentInfo(agentId);
        if (agentProfile==null){
            throw exception(AGENT_NOT_EXISTS);
        }
//        获取到当前代理商默认模版ID
        Long templateId = agentProfile.getCustomPricingTemplateId();
//        通过当前代理商默认模版ID查询所有模版数据
        TemplateDO templateDO = templateMapper.selectById(templateId);
        return BeanUtils.toBean(templateDO, TemplateApiDTO.class);
    }

    /**
     * 获取模板列表
     * @param agentId
     * @return
     */
    @Override
    public PageResult<TemplateApiDTO> selectTemplatePage(Long agentId) {
        return BeanUtils.toBean(templateMapper.selectTemplatePage(agentId),TemplateApiDTO.class);
    }

    /**
     * 获取模板列表含人数
     * @param agentId
     * @return
     */
    @Override
    public List<TemplateRelationDTO> getTemplateByAgentID(Long agentId) {
        return templateMapper.getTemplateAndUserSum(agentId);
    }

    /**
     * 通过代理商id查询相应的通过的价格模板
     * @param agentId
     * @param templateType
     * @return
     */
    @Override
    public List<TemplateRelationDTO> getTypeTemplate(Long agentId, Long templateType) {
        return templateMapper.getTemplateByType(agentId,templateType);
    }
}
