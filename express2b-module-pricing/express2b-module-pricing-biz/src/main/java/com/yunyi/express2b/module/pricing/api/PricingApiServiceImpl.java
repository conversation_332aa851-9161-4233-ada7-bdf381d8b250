package com.yunyi.express2b.module.pricing.api;

import com.yunyi.express2b.module.pricing.api.vo.*;

import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.module.pricing.dal.dataobject.newtemplate.NewTemplateDO;
import com.yunyi.express2b.module.pricing.dal.dataobject.pricingrule.PricingRuleDO;
import com.yunyi.express2b.module.pricing.dal.dataobject.route.RouteDO;
import com.yunyi.express2b.module.pricing.dal.dataobject.selectedpricingtemplate.SelectedPricingTemplateDO;
import com.yunyi.express2b.module.pricing.dal.mysql.newtemplate.NewTemplateMapper;
import com.yunyi.express2b.module.pricing.dal.mysql.pricingrule.PricingRuleMapper;
import com.yunyi.express2b.module.pricing.dal.mysql.route.RouteMapper;
import com.yunyi.express2b.module.pricing.dal.mysql.selectedpricingtemplate.SelectedPricingTemplateMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;



import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.module.pricing.enums.ErrorCodeConstants.RULE_NOT_EXISTS;
import static com.yunyi.express2b.module.pricing.enums.ErrorCodeConstants.TEMPLATE_NOT_EXISTS;


/**
 * 代理商定价（终端零售价）规则 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PricingApiServiceImpl implements PricingApiflow {

    @Resource
    private PricingRuleMapper ruleMapper;
    @Resource
    private RouteMapper routeMapper;
    @Resource
    private SelectedPricingTemplateMapper selectedPricingTemplateMapper;

    @Resource
    private NewTemplateMapper newTemplateMapper;

    /**
     * 校验规则是否存在
     * @param id
     */
    private void validateRuleExists(Long id) {
        if (ruleMapper.selectById(id) == null) {
            throw exception(RULE_NOT_EXISTS);
        }
    }


    /**
     * 创建路由
     * @param createReqVO 创建信息
     * @return
     */
    @Override
    public Long createRoute(RouteSaveReqVO createReqVO) {
        // 插入
        RouteDO route = BeanUtils.toBean(createReqVO, RouteDO.class);
        routeMapper.insert(route);
        // 返回
        return route.getId();
    }


    /**
     * 创建代理商定价（终端零售价）规则
     * @param createReqVO 创建信息
     * @return
     */
    @Override
    public Long createRule(PricingRuleSaveReqVO createReqVO) {
        // 插入
        PricingRuleDO rule = BeanUtils.toBean(createReqVO, PricingRuleDO.class);
        ruleMapper.insert(rule);
        // 返回
        return rule.getId();
    }

    /**
     * 创建代理商
     * @param createReqVO 创建信息
     * @return
     */
    @Override
    public Long createSelectedPricingTemplate(SelectedPricingTemplateSaveReqVO createReqVO) {
        // 插入
        SelectedPricingTemplateDO selectedPricingTemplate = BeanUtils.toBean(createReqVO, SelectedPricingTemplateDO.class);
        selectedPricingTemplateMapper.insert(selectedPricingTemplate);
        // 返回
        return selectedPricingTemplate.getId();
    }

    /**
     * 获取模板信息
     * @param id
     * @return
     */
    @Override
    public NewTemplateSaveReqVO statusTemplate(Long id) {
        NewTemplateDO newTemplateDO = newTemplateMapper.selectById(id);
        if (newTemplateDO == null) {
            throw exception(TEMPLATE_NOT_EXISTS);
        }
        return BeanUtils.toBean(newTemplateDO, NewTemplateSaveReqVO.class);
    }


}