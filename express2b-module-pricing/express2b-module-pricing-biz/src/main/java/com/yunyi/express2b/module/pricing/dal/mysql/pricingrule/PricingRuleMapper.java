package com.yunyi.express2b.module.pricing.dal.mysql.pricingrule;

import java.util.*;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yunyi.express2b.framework.mybatis.core.mapper.BaseMapperX;
import com.yunyi.express2b.module.pricing.dal.dataobject.pricingrule.PricingRuleDO;
import org.apache.ibatis.annotations.Mapper;
import com.yunyi.express2b.module.pricing.controller.admin.pricingrule.vo.*;

/**
 * 代理商定价（终端零售价）规则 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PricingRuleMapper extends BaseMapperX<PricingRuleDO> {

    default PageResult<PricingRuleDO> selectPage(PricingRulePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PricingRuleDO>()
                .eqIfPresent(PricingRuleDO::getAgentId, reqVO.getAgentId())
                .eqIfPresent(PricingRuleDO::getMarkupType, reqVO.getMarkupType())
                .eqIfPresent(PricingRuleDO::getFirstWeightMarkup, reqVO.getFirstWeightMarkup())
                .eqIfPresent(PricingRuleDO::getAdditionalWeightMarkup, reqVO.getAdditionalWeightMarkup())
                .eqIfPresent(PricingRuleDO::getBrandKey, reqVO.getBrandKey())
                .eqIfPresent(PricingRuleDO::getFromAdcode, reqVO.getFromAdcode())
                .eqIfPresent(PricingRuleDO::getToAdcode, reqVO.getToAdcode())
                .eqIfPresent(PricingRuleDO::getStatus, reqVO.getStatus())
                .eqIfPresent(PricingRuleDO::getPriority, reqVO.getPriority())
                .betweenIfPresent(PricingRuleDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(PricingRuleDO::getId));
    }

        default Long selectIdByAgentId(Long agentId) {
        PricingRuleDO ruleDO = selectOne(new LambdaQueryWrapper<PricingRuleDO>()
                .select(PricingRuleDO::getId)
                .eq(PricingRuleDO::getAgentId, agentId)
                .last("LIMIT 1"));
        return ruleDO != null ? ruleDO.getId() : null;
    }

}