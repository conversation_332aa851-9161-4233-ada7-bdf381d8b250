package com.yunyi.express2b.module.pricing.dal.mysql.relationship;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.yunyi.express2b.framework.common.enums.CommonStatusEnum;
import com.yunyi.express2b.framework.mybatis.core.mapper.BaseMapperX;
import com.yunyi.express2b.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yunyi.express2b.module.pricing.dal.dataobject.relationship.TemplateAgentRelationDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 定价关系模版表
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/5/22 下午1:21
 */
@Mapper
public interface TemplateAgentRelationMapper extends BaseMapperX<TemplateAgentRelationDO> {
    /**
     * 插入代理商和模版关联关系
     * @param agentId
     * @param templateId
     * @return
     */
    default Long insertTemplateAgentRelation(Long agentId,Long templateId,Long tanentId) {
        TemplateAgentRelationDO templateAgentRelationDo = new TemplateAgentRelationDO();
        templateAgentRelationDo.setAgentId(agentId);
        templateAgentRelationDo.setTemplateId(templateId);
        templateAgentRelationDo.setTenantId(tanentId);
        templateAgentRelationDo.setCreator(agentId.toString());
        templateAgentRelationDo.setUpdater(agentId.toString());
        return (long) insert(templateAgentRelationDo);
    }

    /**
     * 根据代理商id查询所有定价模版id
     * @param agentId
     * @return
     */
    default List<Long> selectByAgentId(Long agentId){
        return selectList(new LambdaQueryWrapperX<TemplateAgentRelationDO>()
                .select(TemplateAgentRelationDO::getTemplateId)
                .eq(TemplateAgentRelationDO::getAgentId,agentId)
                .orderByDesc(TemplateAgentRelationDO::getTemplateId)
                .last("LIMIT 10"))
                .stream()
                .map(TemplateAgentRelationDO::getTemplateId)
                .toList();
    }

    /**
     * 根据模版id查询代理商id
     * @param id
     * @return
     */
    default Long selectAgentID(Long id){
        return selectOne(new LambdaQueryWrapperX<TemplateAgentRelationDO>()
                .select(TemplateAgentRelationDO::getAgentId)
                .eq(TemplateAgentRelationDO::getTemplateId,id))
                .getAgentId();
    }

    /**
     * 修改模版关系表中的是否默认选项
     * @param id
     * @param agentId
     * @return
     */
    default Long updateDefaultId(Long id,Long agentId){
        return Long.valueOf(update(new LambdaUpdateWrapper<TemplateAgentRelationDO>()
                .set(TemplateAgentRelationDO::getIsDefault, CommonStatusEnum.ENABLE.getStatus())
                .eq(TemplateAgentRelationDO::getTemplateId,id)
                .eq(TemplateAgentRelationDO::getAgentId,agentId)));
    }

    /**
     * 将原默认模版修改为不默认
     * @param id
     * @param agentId
     * @return
     */
    default Long updateDefaultIdUnDefault(Long id,Long agentId){
        return Long.valueOf(update(new LambdaUpdateWrapper<TemplateAgentRelationDO>()
                .set(TemplateAgentRelationDO::getIsDefault, CommonStatusEnum.DISABLE.getStatus())
                .eq(TemplateAgentRelationDO::getTemplateId,id)
                .eq(TemplateAgentRelationDO::getAgentId,agentId)));
    }
    /**
     * 插入代理商与模板关系
     * @param templateAgentRelationDO
     * @return
     */
    default int insertTemplateAgentRelationForRegister(TemplateAgentRelationDO templateAgentRelationDO){
        return insert(templateAgentRelationDO);
    }
}
