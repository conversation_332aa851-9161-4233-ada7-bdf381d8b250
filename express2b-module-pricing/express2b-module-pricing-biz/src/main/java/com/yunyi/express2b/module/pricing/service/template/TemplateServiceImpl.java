package com.yunyi.express2b.module.pricing.service.template;

import com.yunyi.express2b.module.pricing.dal.dataobject.template.PricingTemplateDetailDTO;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import com.yunyi.express2b.module.pricing.controller.admin.template.vo.*;
import com.yunyi.express2b.module.pricing.dal.dataobject.template.TemplateDO;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.pojo.PageParam;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;

import com.yunyi.express2b.module.pricing.dal.mysql.template.TemplateMapper;

import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.module.pricing.enums.ErrorCodeConstants.*;

/**
 * 定价模板 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TemplateServiceImpl implements TemplateService {

    @Resource
    private TemplateMapper templateMapper;

    @Override
    public Long createTemplate(TemplateSaveReqVO createReqVO) {
        // 插入
        TemplateDO template = BeanUtils.toBean(createReqVO, TemplateDO.class);
        templateMapper.insert(template);
        // 返回
        return template.getId();
    }

    @Override
    public void updateTemplate(TemplateSaveReqVO updateReqVO) {
        // 校验存在
        validateTemplateExists(updateReqVO.getId());
        // 更新
        TemplateDO updateObj = BeanUtils.toBean(updateReqVO, TemplateDO.class);
        templateMapper.updateById(updateObj);
    }

    @Override
    public void deleteTemplate(Long id) {
        // 校验存在
        validateTemplateExists(id);
        // 删除
        templateMapper.deleteById(id);
    }

    private void validateTemplateExists(Long id) {
        if (templateMapper.selectById(id) == null) {
            throw exception(TEMPLATE_NOT_EXISTS);
        }
    }

    @Override
    public TemplateDO getTemplate(Long id) {
        return templateMapper.selectById(id);
    }

    @Override
    public PageResult<TemplateDO> getTemplatePage(TemplatePageReqVO pageReqVO) {
        return templateMapper.selectPage(pageReqVO);
    }

    /**
     * 获取定价模板详细信息，包括关联的代理商和基础价格信息
     * @param id 模板id
     * @return
     */
    @Override
    public PricingTemplateDetailDTO getTemplateid(Long id) {
        PricingTemplateDetailDTO templateDO = null;

        try {
            PricingTemplateDetailDTO pricingTemplateDetailDTO = new PricingTemplateDetailDTO();
            pricingTemplateDetailDTO.setId(id);
            templateDO = templateMapper.selecttemplateId(pricingTemplateDetailDTO);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        //如果模板不存在，抛出异常
        if (templateDO == null){
            throw exception(TEMPLATE_NOT_FOUND," ID: " + id);
        }
        return templateDO;
    }

    /**
     * 作为代理商我可以查询平台提供的定价模板，了解首重/续重价格规则
     * @param
     * @return
     */
    @Override
    public List<PricingTemplateDetailDTO> getTemplatePricingRules(Long templateId) {
        List<PricingTemplateDetailDTO> templatePricingRules = null;
        try {
            templatePricingRules = templateMapper.getTemplatePricingRules(templateId);
        } catch (Exception e) {
            throw exception(TEMPLATE_NOT_FOUND,"templateId ID: " + templateId);
        }
        if (templatePricingRules == null || templatePricingRules.isEmpty()) {
            throw exception(TEMPLATE_NOT_FOUND,"templateId ID: " + templateId);
        }
        return templatePricingRules;
    }

}