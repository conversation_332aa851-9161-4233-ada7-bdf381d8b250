-- 将该建表 SQL 语句，添加到 express2b-module-pricing-biz 模块的 test/resources/sql/create_tables.sql 文件里
CREATE TABLE IF NOT EXISTS "agent_pricing_rule" (
                                                    "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
                                                    "agent_id" bigint NOT NULL,
                                                    "markup_type" int NOT NULL,
                                                    "first_weight_markup" int NOT NULL,
                                                    "additional_weight_markup" int NOT NULL,
                                                    "brand_key" varchar,
                                                    "from_adcode" varchar,
                                                    "to_adcode" varchar,
                                                    "status" int NOT NULL,
                                                    "priority" int NOT NULL,
                                                    "creator" varchar DEFAULT '',
                                                    "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                                    "updater" varchar DEFAULT '',
                                                    "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                                    "deleted" bit NOT NULL DEFAULT FALSE,
                                                    "tenant_id" bigint NOT NULL DEFAULT 0,
                                                    PRIMARY KEY ("id")
    ) COMMENT '代理商定价（终端零售价）规则表';
-- 将该建表 SQL 语句，添加到 express2b-module-pricing-biz 模块的 test/resources/sql/create_tables.sql 文件里
CREATE TABLE IF NOT EXISTS "agent_selected_pricing_template" (
                                                                 "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
                                                                 "superior_agent_id" bigint NOT NULL,
                                                                 "downline_agent_id" bigint NOT NULL,
                                                                 "pricing_template_id" bigint NOT NULL,
                                                                 "creator" varchar DEFAULT '',
                                                                 "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                                                 "updater" varchar DEFAULT '',
                                                                 "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                                                 "deleted" bit NOT NULL DEFAULT FALSE,
                                                                 "tenant_id" bigint NOT NULL DEFAULT 0,
                                                                 PRIMARY KEY ("id")
    ) COMMENT '代理商为下级选择的价格模板记录表';
-- 将该建表 SQL 语句，添加到 express2b-module-pricing-biz 模块的 test/resources/sql/create_tables.sql 文件里
CREATE TABLE IF NOT EXISTS "pricing_template" (
                                                  "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
                                                  "template_name" varchar NOT NULL,
                                                  "markup_type" int NOT NULL,
                                                  "first_weight_markup" int NOT NULL,
                                                  "additional_weight_markup" int NOT NULL,
                                                  "platform_first_price" int NOT NULL,
                                                  "platform_additional_price" int NOT NULL,
                                                  "status" int NOT NULL,
                                                  "description" varchar,
                                                  "creator" varchar DEFAULT '',
                                                  "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                                  "updater" varchar DEFAULT '',
                                                  "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                                  "deleted" bit NOT NULL DEFAULT FALSE,
                                                  "tenant_id" bigint NOT NULL DEFAULT 0,
                                                  PRIMARY KEY ("id")
    ) COMMENT '定价模板表';
-- 将该建表 SQL 语句，添加到 express2b-module-pricing-biz 模块的 test/resources/sql/create_tables.sql 文件里
CREATE TABLE IF NOT EXISTS "express2b_route" (
                                                 "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
                                                 "brand_key" varchar NOT NULL,
                                                 "line_key" varchar NOT NULL,
                                                 "send_province" varchar,
                                                 "receive_province" varchar,
                                                 "send_province_code" varchar NOT NULL,
                                                 "receive_province_code" varchar NOT NULL,
                                                 "product_code" varchar,
                                                 "first_weight" int NOT NULL,
                                                 "additional_weight" int NOT NULL,
                                                 "cost_first_price" int NOT NULL,
                                                 "cost_over_price" int NOT NULL,
                                                 "original_first_price" int,
                                                 "original_over_price" int,
                                                 "creator" varchar DEFAULT '',
                                                 "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                                 "updater" varchar DEFAULT '',
                                                 "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                                 "deleted" bit NOT NULL DEFAULT FALSE,
                                                 "tenant_id" bigint NOT NULL DEFAULT 0,
                                                 PRIMARY KEY ("id")
    ) COMMENT '快递全国运价基础表';

-- 将该建表 SQL 语句，添加到 express2b-module-pricing-biz 模块的 test/resources/sql/create_tables.sql 文件里
CREATE TABLE IF NOT EXISTS "pricing_agent_rule" (
                                                    "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
                                                    "agent_id" bigint NOT NULL,
                                                    "markup_type" int NOT NULL,
                                                    "first_weight_markup" int NOT NULL,
                                                    "additional_weight_markup" int NOT NULL,
                                                    "brand_key" varchar,
                                                    "from_adcode" varchar,
                                                    "to_adcode" varchar,
                                                    "status" int NOT NULL,
                                                    "priority" int NOT NULL,
                                                    "creator" varchar DEFAULT '',
                                                    "create_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                                    "updater" varchar DEFAULT '',
                                                    "update_time" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                                    "deleted" bit NOT NULL DEFAULT FALSE,
                                                    "tenant_id" bigint NOT NULL DEFAULT 0,
                                                    PRIMARY KEY ("id")
) COMMENT '代理商定价（终端零售价）规则表';