package com.yunyi.express2b.module.pricing.service.pricingrule;

import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.test.core.ut.BaseDbUnitTest;
import com.yunyi.express2b.framework.test.core.util.RandomUtils;
import com.yunyi.express2b.module.pricing.controller.admin.pricingrule.vo.PricingRulePageReqVO;
import com.yunyi.express2b.module.pricing.controller.admin.pricingrule.vo.PricingRuleSaveReqVO;
import com.yunyi.express2b.module.pricing.dal.dataobject.pricingrule.PricingRuleDO;
import com.yunyi.express2b.module.pricing.dal.mysql.pricingrule.PricingRuleMapper;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import static com.yunyi.express2b.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static com.yunyi.express2b.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static com.yunyi.express2b.framework.test.core.util.AssertUtils.assertPojoEquals;
import static com.yunyi.express2b.framework.test.core.util.AssertUtils.assertServiceException;
import static com.yunyi.express2b.framework.test.core.util.RandomUtils.randomLongId;
import static com.yunyi.express2b.framework.test.core.util.RandomUtils.randomPojo;
import static com.yunyi.express2b.module.pricing.enums.ErrorCodeConstants.RULE_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link PricingRuleServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(PricingRuleServiceImpl.class)
public class PricingRuleServiceImplTest extends BaseDbUnitTest {

    @Resource
    private PricingRuleServiceImpl ruleService;

    @Resource
    private PricingRuleMapper ruleMapper;

    @Test
    public void testCreateRule_success() {
        // 准备参数
        PricingRuleSaveReqVO createReqVO = randomPojo(PricingRuleSaveReqVO.class).setId(null);

        // 调用
        Long ruleId = ruleService.createRule(createReqVO);
        // 断言
        assertNotNull(ruleId);
        // 校验记录的属性是否正确
        PricingRuleDO rule = ruleMapper.selectById(ruleId);
        assertPojoEquals(createReqVO, rule, "id");
    }

    @Test
    public void testUpdateRule_success() {
        // mock 数据
        PricingRuleDO dbRule = randomPojo(PricingRuleDO.class);
        ruleMapper.insert(dbRule);// @Sql: 先插入出一条存在的数据
        // 准备参数
        PricingRuleSaveReqVO updateReqVO = randomPojo(PricingRuleSaveReqVO.class, o -> {
            o.setId(dbRule.getId()); // 设置更新的 ID
        });

        // 调用
        ruleService.updateRule(updateReqVO);
        // 校验是否更新正确
        PricingRuleDO rule = ruleMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, rule);
    }

    @Test
    public void testUpdateRule_notExists() {
        // 准备参数
        PricingRuleSaveReqVO updateReqVO = randomPojo(PricingRuleSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> ruleService.updateRule(updateReqVO), RULE_NOT_EXISTS);
    }

    @Test
    public void testDeleteRule_success() {
        // mock 数据
        PricingRuleDO dbRule = randomPojo(PricingRuleDO.class);
        ruleMapper.insert(dbRule);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbRule.getId();

        // 调用
        ruleService.deleteRule(id);
        // 校验数据不存在了
        assertNull(ruleMapper.selectById(id));
    }

    @Test
    public void testDeleteRule_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> ruleService.deleteRule(id), RULE_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetRulePage() {
        // mock 数据
        PricingRuleDO dbRule = randomPojo(PricingRuleDO.class, o -> { // 等会查询到
            o.setAgentId(null);
            o.setMarkupType(null);
            o.setFirstWeightMarkup(null);
            o.setAdditionalWeightMarkup(null);
            o.setBrandKey(null);
            o.setFromAdcode(null);
            o.setToAdcode(null);
            o.setStatus(null);
            o.setPriority(null);
            o.setCreateTime(null);
        });
        ruleMapper.insert(dbRule);
        // 测试 agentId 不匹配
        ruleMapper.insert(cloneIgnoreId(dbRule, o -> o.setAgentId(null)));
        // 测试 markupType 不匹配
        ruleMapper.insert(cloneIgnoreId(dbRule, o -> o.setMarkupType(null)));
        // 测试 firstWeightMarkup 不匹配
        ruleMapper.insert(cloneIgnoreId(dbRule, o -> o.setFirstWeightMarkup(null)));
        // 测试 additionalWeightMarkup 不匹配
        ruleMapper.insert(cloneIgnoreId(dbRule, o -> o.setAdditionalWeightMarkup(null)));
        // 测试 brandKey 不匹配
        ruleMapper.insert(cloneIgnoreId(dbRule, o -> o.setBrandKey(null)));
        // 测试 fromAdcode 不匹配
        ruleMapper.insert(cloneIgnoreId(dbRule, o -> o.setFromAdcode(null)));
        // 测试 toAdcode 不匹配
        ruleMapper.insert(cloneIgnoreId(dbRule, o -> o.setToAdcode(null)));
        // 测试 status 不匹配
        ruleMapper.insert(cloneIgnoreId(dbRule, o -> o.setStatus(null)));
        // 测试 priority 不匹配
        ruleMapper.insert(cloneIgnoreId(dbRule, o -> o.setPriority(null)));
        // 测试 createTime 不匹配
        ruleMapper.insert(cloneIgnoreId(dbRule, o -> o.setCreateTime(null)));
        // 准备参数
        PricingRulePageReqVO reqVO = new PricingRulePageReqVO();
        reqVO.setAgentId(null);
        reqVO.setMarkupType(null);
        reqVO.setFirstWeightMarkup(null);
        reqVO.setAdditionalWeightMarkup(null);
        reqVO.setBrandKey(null);
        reqVO.setFromAdcode(null);
        reqVO.setToAdcode(null);
        reqVO.setStatus(null);
        reqVO.setPriority(null);
        reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
        reqVO.setTenantId(null);

        // 调用
        PageResult<PricingRuleDO> pageResult = ruleService.getRulePage(reqVO);
        // 断言
        assertEquals(1, pageResult.getTotal());
        assertEquals(1, pageResult.getList().size());
        assertPojoEquals(dbRule, pageResult.getList().get(0));
    }

    @Test
    public void testSelectIdByAgentId(){
        Long aLong = ruleMapper.selectIdByAgentId(1L);
        assertNull(aLong);

        PricingRuleDO entity = randomPojo(PricingRuleDO.class);
        Long enId = entity.getAgentId();
        ruleMapper.insert(entity);
        aLong = ruleMapper.selectIdByAgentId(enId);
        System.out.println(aLong);
        assertNotNull(aLong);
    }
}