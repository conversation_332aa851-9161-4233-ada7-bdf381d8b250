package com.yunyi.express2b.module.pricing.api;

import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.module.pricing.api.dto.TemplateApiDTO;
import com.yunyi.express2b.module.pricing.api.dto.TemplateRelationDTO;

import java.util.List;
//需要修改
public interface TemplateApi {

    /**
     * 通过代理商ID查询定价模版
     * @param agentId 代理商ID
     * @return pricingTemplateId
     */
    TemplateApiDTO getTemplateIdByAgentId(Long agentId);

    /**
     * 查询定价模板
     * @param agentId
     * @return
     */
    PageResult<TemplateApiDTO> selectTemplatePage(Long agentId);

    /**
     * 根据代理商查询价格模板
     * @param agentId
     * @return
     */
    List<TemplateRelationDTO> getTemplateByAgentID(Long agentId);

    /**
     *根据模版类型查询
     * @param templateType
     * @return
     */
    List<TemplateRelationDTO> getTypeTemplate(Long agentId,Long templateType);
}
