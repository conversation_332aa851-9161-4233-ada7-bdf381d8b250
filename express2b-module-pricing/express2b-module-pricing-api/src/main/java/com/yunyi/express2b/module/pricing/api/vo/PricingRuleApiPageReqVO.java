package com.yunyi.express2b.module.pricing.api.vo;

import com.yunyi.express2b.framework.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;


import java.time.LocalDateTime;



@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PricingRuleApiPageReqVO extends PageParam {

   //"代理商ID"
    private Long agentId;

    //"加价类型 (1-比例加价，2-固定金额加价-默认)"
    private Integer markupType;

    //"首重重量
    private Integer firstWeightMarkup;

    // "续重重量
    private Integer additionalWeightMarkup;

    // "品牌标识 (可为空，表示所有品牌)")
    private String brandKey;

    //"始发地adcode (可为空，表示所有始发地)")
    private String fromAdcode;

    //"目的地adcode (可为空，表示所有目的地)")
    private String toAdcode;

    // "状态 (0-禁用，1-启用)", example = "2")
    private Integer status;

    // "优先级 (数字越大优先级越高)")
    private Integer priority;

    // "创建时间"
    private LocalDateTime[] createTime;

    //"租户编号"
    private Long tenantId;

}