package com.yunyi.express2b.module.pricing.api.dto;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class DetailShareReqDTO {
    private Long id;


    @NotNull(message = "订单ID不能为空")
    private Long orderId;


    @NotEmpty(message = "订单编号不能为空")
    private String orderNo;


    @NotNull(message = "分润目标ID（代理商ID）不能为空")
    private Long targetId;


    @NotNull(message = "分润目标类型（2:V2代理商,3:V3代理商）不能为空")
    private Integer targetType;


    //@NotNull(message = "分润金额（单位：分）不能为空")
    private Integer amount;


    @NotNull(message = "状态 (1:未结算, 2:已结算, 3:已取消)不能为空")
    private Integer status;


    private String reason;


    @NotNull(message = "计算时间不能为空")
    private LocalDateTime calculateTime;

    /**
     * 订单重量（单位：克）
     */
    private Long weight;
    /**
     * 代理商ID
     */
    private Long agentId;
}
