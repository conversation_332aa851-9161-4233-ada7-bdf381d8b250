package com.yunyi.express2b.module.wallet.api.TransationsApi;

import com.yunyi.express2b.module.agent.api.dto.AgentActivityDTO;
import com.yunyi.express2b.module.wallet.api.TransationsApi.dto.TransactionsDTO;
import com.yunyi.express2b.module.wallet.api.TransationsApi.dto.TransactionsDetailsDTO;
import com.yunyi.express2b.module.wallet.api.WalletApi.dto.TransactionsSaveReqDO;

import java.util.List;

public interface TransactionsApi {
    /**
     * 根据代理商id获取钱包流水表信息
     * @param agentId
     * @return
     */

    TransactionsDetailsDTO getTransactionsDetailsByAgentId(Long agentId);

    /**
     * 根据代理商团队id获取团队流水总数
     *
     * @param agentId
     * @return
     */
    List<TransactionsDTO> getAmountSUM(List<Long> agentId);
    /**
     * 根据代理商id获取钱包流水表信息
     * @param agentId
     * @return
     */

    List<TransactionsDetailsDTO> getTransactionsDetails(Long agentId);

    /**
     * 根据代理商id获取时间区段流水总数
     *
     * @param agentId
     * @return
     */
    Integer getSumBetweenTime(Long agentId);

    /**
     * 根据代理商id获取团队时间区段流水总数
     *
     * @param agentId
     * @return
     */
    Integer getTeamSumBetweenTime(Long agentId);

    /**
     * 坏账处理
     * @param transactionsSaveReqDO
     * @return
     */
    Long badDebtProcessing(TransactionsSaveReqDO transactionsSaveReqDO);

    /**
     * 坏账收回处理
     * @param transactionsSaveReqDO
     * @return
     */
    Long badDebtRecovery(TransactionsSaveReqDO transactionsSaveReqDO);

    /**
     * 统计代理商及其直属团队的钱包流水状态信息
     * @param agentActivityDTO
     * @param teamIds
     * @return
     */
    AgentActivityDTO getAgentWalletTransactionsActivity(AgentActivityDTO agentActivityDTO,List<Long> teamIds);

}
