package com.yunyi.express2b.module.wallet.enums;

import com.yunyi.express2b.framework.common.core.ArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/3/24 11:35
 */
@Getter
@AllArgsConstructor
public enum WalletTransactionEnum implements ArrayValuable<String> {
// 钱包流水状态
    FROZEN("FROZEN", "冻结中"),
    REQUESTED("REQUESTED", "已申请"),
    AVAILABLE("AVAILABLE", "可用"),
    COMPLETED("COMPLETED", "已完成"),
    FAILED("FAILED", "失败"),
    CANCELLED("CANCELLED", "已取消");
    private final String status;
    private final String name;
    /**
     * 根据 status 获取对应的中文名称
     * @param status 枚举的英文标识
     * @return 对应的中文名称，如果没有找到则返回 null 或默认值
     */
    public static String getNameByStatus(String status) {
        for (WalletTransactionEnum type : WalletTransactionEnum.values()) {
            if (type.getStatus().equals(status)) {
                return type.getName();
            }
        }
        return null; // 或者返回 "未知状态"
    }

    public static final String[] ARRAYS = Arrays.stream(values()).map(WalletTransactionEnum::getStatus).toArray(String[]::new);

    @Override
    public String[] array() {
        return ARRAYS;
    }
}
