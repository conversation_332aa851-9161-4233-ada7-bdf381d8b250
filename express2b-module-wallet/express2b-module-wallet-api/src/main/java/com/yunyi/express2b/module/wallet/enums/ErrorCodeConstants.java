//      钱包模块-wallet：     1-007-xxx-xxx  [1-007-000-000 ~ 1-007-999-999]
package com.yunyi.express2b.module.wallet.enums;

import com.yunyi.express2b.framework.common.exception.ErrorCode;

/**
 * Wallet 错误码枚举类
 *
 * 钱包系统，使用 1_007_000_000 段
 * 钱包管理模块：1_007_001_xxx
 * 提现申请模块：1_007_002_xxx
 * 交易记录模块：1_007_003_xxx
 */
public interface ErrorCodeConstants {

    // ========== 钱包管理模块 1_007_001_xxx ==========
    ErrorCode WALLET_NOT_EXISTS = new ErrorCode(1_007_001_000, "钱包不存在");
    ErrorCode WALLET_BALANCE_NOT_ENOUGH = new ErrorCode(1_007_001_001, "钱包余额不足");
    ErrorCode WALLET_FROZEN = new ErrorCode(1_007_001_002, "钱包已冻结");
    ErrorCode AGENT_WALLET_NOT_EXISTS = new ErrorCode(1_007_001_003, "代理商钱包不存在");
    ErrorCode WALLET_TRANSACTIONS_FAIL = new ErrorCode(1_007_001_004, "创建钱包流水失败");

    // ========== 提现申请模块 1_007_002_xxx ==========
    ErrorCode WITHDRAWAL_REQUEST_NOT_EXISTS = new ErrorCode(1_007_002_000, "提现申请不存在");
    ErrorCode WITHDRAWAL_REQUEST_STATUS_ERROR = new ErrorCode(1_007_002_001, "提现申请状态错误");
    ErrorCode REQUESTS_NOT_EXISTS = new ErrorCode(1_007_002_002, "提现申请不存在");
    
    // ========== 交易记录模块 1_007_003_xxx ==========
    ErrorCode WALLET_TRANSACTION_NOT_EXISTS = new ErrorCode(1_007_003_000, "钱包交易记录不存在");
    ErrorCode TRANSACTIONS_NOT_EXISTS = new ErrorCode(1_007_003_001, "钱包流水不存在");
}