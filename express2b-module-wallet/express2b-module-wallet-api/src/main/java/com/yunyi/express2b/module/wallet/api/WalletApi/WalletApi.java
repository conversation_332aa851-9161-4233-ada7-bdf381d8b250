package com.yunyi.express2b.module.wallet.api.WalletApi;

import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.module.wallet.api.WalletApi.dto.TransactionsSaveReqDO;
import com.yunyi.express2b.module.wallet.api.WalletApi.dto.WalletDetailsDTO;
import com.yunyi.express2b.module.wallet.api.WalletApi.dto.WalletQueryDTO;
import jakarta.validation.Valid;

/**
 * 钱包模块 API 接口
 *
 * <AUTHOR>
 */
public interface WalletApi {

    /**
     * 获取代理商钱包详情 (包含可用余额和冻结余额)
     *
     * @param agentId 代理商ID
     * @return 钱包详情DTO
     */
    WalletDetailsDTO getWalletDetails(Long agentId);
    /**
     * 创建代理商钱包接口 (包含可用余额和冻结余额)
     *
     * @param agentId 代理商ID
     * @return 钱包DTO
     */
    Long createWallet(Long agentId,Long tenantId);

    /**
     * 创建钱包流水
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTransactions(@Valid TransactionsSaveReqDO createReqVO);

    /**
     *
     * 更改钱包冻结余额接口
     * @param agentId
     * @return 编号
     * */
    Long updateWallet(Long agentId,Integer frozenBalance);

} 