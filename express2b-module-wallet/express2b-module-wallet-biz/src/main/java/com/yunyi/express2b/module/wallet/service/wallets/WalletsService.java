package com.yunyi.express2b.module.wallet.service.wallets;

import com.yunyi.express2b.module.wallet.controller.admin.requests.vo.WalletPageReqVO;
import com.yunyi.express2b.module.wallet.controller.admin.requests.vo.walletDetailRespVO;
import jakarta.validation.*;
import com.yunyi.express2b.module.wallet.controller.admin.wallets.vo.*;
import com.yunyi.express2b.module.wallet.dal.dataobject.wallets.WalletsDO;
import com.yunyi.express2b.framework.common.pojo.PageResult;

/**
 * 代理商钱包 Service 接口
 *
 * <AUTHOR>
 */
public interface WalletsService {

    /**
     * 创建代理商钱包
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long creates(@Valid WalletsSaveReqVO createReqVO);

    /**
     * 更新代理商钱包
     *
     * @param updateReqVO 更新信息
     */
    void updates(@Valid WalletsSaveReqVO updateReqVO);

    /**
     * 删除代理商钱包
     *
     * @param id 编号
     */
    void deletes(Long id);

    /**
     * 获得代理商钱包
     *
     * @param id 编号
     * @return 代理商钱包
     */
    WalletsDO gets(Long id);

    /**
     * 获得代理商钱包分页
     *
     * @param pageReqVO 分页查询
     * @return 代理商钱包分页
     */
    PageResult<WalletsDO> getsPage(WalletsPageReqVO pageReqVO);


    WalletsDO getWalletBalance();

    PageResult<walletDetailRespVO> getWalletList(@Valid WalletPageReqVO walletPageReqVO);

    PageResult<WalletTransactionQueryReqVO> getWalletTransactionList();

    /**
     * 根据代理商id获取钱包详情
     * @param agentId
     * @return
     * */
    WalletsDO getAgentWallet(Long agentId);
}