package com.yunyi.express2b.module.wallet.service.wallets;

import com.yunyi.express2b.framework.security.core.util.SecurityFrameworkUtils;
import com.yunyi.express2b.module.agent.api.AgentApi;
import com.yunyi.express2b.module.agent.api.dto.AgentInfoDTO;
import com.yunyi.express2b.module.wallet.controller.admin.requests.vo.WalletPageReqVO;
import com.yunyi.express2b.module.wallet.controller.admin.requests.vo.walletDetailRespVO;
import com.yunyi.express2b.module.wallet.dal.dataobject.transactions.TransactionsDO;
import com.yunyi.express2b.module.wallet.enums.AgentWalletEnum;
import com.yunyi.express2b.module.wallet.service.transactions.TransactionsService;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.yunyi.express2b.module.wallet.controller.admin.wallets.vo.*;
import com.yunyi.express2b.module.wallet.dal.dataobject.wallets.WalletsDO;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;

import com.yunyi.express2b.module.wallet.dal.mysql.wallets.WalletsMapper;

import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.module.agent.enums.ErrorCodeConstants.AGENT_NOT_EXISTS;
import static com.yunyi.express2b.module.wallet.enums.ErrorCodeConstants.*;

/**
 * 代理商钱包 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class WalletsServiceImpl implements WalletsService {

    @Resource
    private WalletsMapper WalletsMapper;
    @Resource
    private AgentApi agentApi;
    @Resource
    private TransactionsService transactionsService;

    @Override
    public Long creates(WalletsSaveReqVO createReqVO) {
        // 插入
        WalletsDO wallet = BeanUtils.toBean(createReqVO, WalletsDO.class);
        WalletsMapper.insert(wallet);
        // 返回
        return wallet.getId();
    }

    @Override
    public void updates(WalletsSaveReqVO updateReqVO) {
        // 校验存在
        validatesExists(updateReqVO.getId());
        // 更新
        WalletsDO updateObj = BeanUtils.toBean(updateReqVO, WalletsDO.class);
        WalletsMapper.updateById(updateObj);
    }

    @Override
    public void deletes(Long id) {
        // 校验存在
        validatesExists(id);
        // 删除
        WalletsMapper.deleteById(id);
    }

    private void validatesExists(Long id) {
        if (WalletsMapper.selectById(id) == null) {
            throw exception(WALLET_NOT_EXISTS);
        }
    }

    @Override
    public WalletsDO gets(Long id) {
        return WalletsMapper.selectWalletsByAgentId(id);
    }

    @Override
    public PageResult<WalletsDO> getsPage(WalletsPageReqVO pageReqVO) {
        return WalletsMapper.selectPage(pageReqVO);
    }

    @Override
    public WalletsDO getWalletBalance() {
        Long loginUserId = SecurityFrameworkUtils.getLoginUserId();
        WalletsDO walletsDO = WalletsMapper.selectWalletByAgentId(loginUserId);
        return walletsDO;
    }

    @Override
    public PageResult<walletDetailRespVO> getWalletList(WalletPageReqVO walletPageReqVO) {
        //获取用户信息
        AgentInfoDTO agentInfo = agentApi.getAgentInfo(walletPageReqVO.getAgentId());
        if (agentInfo == null) {
            throw exception(AGENT_NOT_EXISTS);
        }

        //查用户钱包
        WalletsDO walletsDO = WalletsMapper.selectWalletByAgentId(Long.valueOf(agentInfo.getWalletId()));

        // 创建返回对象
        walletDetailRespVO walletDetailRespVO = new walletDetailRespVO();
        walletDetailRespVO.setAgentId(walletPageReqVO.getAgentId());
        walletDetailRespVO.setAgentName(walletPageReqVO.getAgentName()); // 使用从API获取的代理商名称
        walletDetailRespVO.setAvailableBalance(walletsDO.getAvailableBalance());
        walletDetailRespVO.setFrozenBalance(walletsDO.getFrozenBalance());
        walletDetailRespVO.setTotalProfitEarned(walletsDO.getTotalProfitEarned());//累计利润总额
        walletDetailRespVO.setTotalCommissionEarned(walletsDO.getTotalCommissionEarned());//累计分润总额
        walletDetailRespVO.setTotalWithdrawnAmount(walletsDO.getTotalWithdrawnAmount());//累计已提现总额
        walletDetailRespVO.setStatus(walletsDO.getStatus()); // 应使用数据库中的状态
        walletDetailRespVO.setStatusName(AgentWalletEnum.getNameByStatus(walletsDO.getStatus()));
        walletDetailRespVO.setCreateTime(String.valueOf(walletsDO.getCreateTime()));
        walletDetailRespVO.setUpdateTime(String.valueOf(walletsDO.getUpdateTime()));

        // 创建列表并添加结果
        List<walletDetailRespVO> list = new ArrayList<>();
        list.add(walletDetailRespVO);

        // 设置分页结果并返回
        PageResult<walletDetailRespVO> pageResult = new PageResult<>();
        pageResult.setList(list);
        pageResult.setTotal((long) list.size()); // 总数为1
        return pageResult;
    }

    @Override
    public PageResult<WalletTransactionQueryReqVO> getWalletTransactionList() {
        Long loginUserId = SecurityFrameworkUtils.getLoginUserId();
        loginUserId=101L;
        AgentInfoDTO agentInfo = agentApi.getAgentInfo(loginUserId);
        //根据传过来的用户id查询用户信息
        if (agentInfo == null) {
            throw exception(AGENT_NOT_EXISTS);
        }
        //根据用户id查寻用户钱包信息
        WalletsDO Wallet = gets(Long.valueOf(agentInfo.getWalletId()));
        if (Wallet == null){
            throw exception(WALLET_NOT_EXISTS);
        }
        //根据钱包id查寻钱包流水信息
        TransactionsDO transactions = transactionsService.getTransactions(Wallet.getId());
        if (transactions != null) {
            throw exception(TRANSACTIONS_NOT_EXISTS);
        }
        WalletTransactionQueryReqVO walletTransactionQueryReqVO = new WalletTransactionQueryReqVO();
        //copy字段
        BeanUtils.copyProperties(transactions, walletTransactionQueryReqVO);
        BeanUtils.copyProperties(Wallet, walletTransactionQueryReqVO);



        List<WalletTransactionQueryReqVO> list = new ArrayList<>();
        list.add(walletTransactionQueryReqVO);

        // 设置分页结果并返回
        PageResult<WalletTransactionQueryReqVO> pageResult = new PageResult<>();
        pageResult.setList(list);
        pageResult.setTotal((long) list.size()); // 总数为1
        return pageResult;
    }

    /**
     * 获取指定代理的电子钱包信息。
     *
     * @param agentId
     * @return
     */
    @Override
    public WalletsDO getAgentWallet(Long agentId) {
        return WalletsMapper.selectAgentWalletByAgentId(agentId);
    }
}