package com.yunyi.express2b.module.wallet.controller.admin.wallets;
import com.yunyi.express2b.module.wallet.controller.admin.requests.vo.WalletPageReqVO;
import com.yunyi.express2b.module.wallet.controller.admin.requests.vo.walletDetailRespVO;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;
import org.springframework.stereotype.Component;
import com.yunyi.express2b.framework.common.pojo.PageParam;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import static com.yunyi.express2b.framework.common.pojo.CommonResult.success;
import com.yunyi.express2b.framework.excel.core.util.ExcelUtils;
import com.yunyi.express2b.framework.apilog.core.annotation.ApiAccessLog;
import static com.yunyi.express2b.framework.apilog.core.enums.OperateTypeEnum.*;
import com.yunyi.express2b.module.wallet.controller.admin.wallets.vo.*;
import com.yunyi.express2b.module.wallet.dal.dataobject.wallets.WalletsDO;
import com.yunyi.express2b.module.wallet.service.wallets.WalletsService;

@Tag(name = "管理后台 - 代理商钱包")
@RestController
@RequestMapping("/wallet")
@Validated
@Component("adminWalletsController")
public class WalletsController {

    @Resource
    private WalletsService WalletsService;

    @PostMapping("/create")
    @Operation(summary = "创建代理商钱包")
    @PreAuthorize("@ss.hasPermission('wallet:s:create')")
    public CommonResult<Long> creates(@Valid @RequestBody WalletsSaveReqVO createReqVO) {
        return success(WalletsService.creates(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新代理商钱包")
    @PreAuthorize("@ss.hasPermission('wallet:s:update')")
    public CommonResult<Boolean> updates(@Valid @RequestBody WalletsSaveReqVO updateReqVO) {
        WalletsService.updates(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除代理商钱包")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('wallet:s:delete')")
    public CommonResult<Boolean> deletes(@RequestParam("id") Long id) {
        WalletsService.deletes(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得代理商钱包")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('wallet:s:query')")
    public CommonResult<WalletsRespVO> gets(@RequestParam("id") Long id) {
        WalletsDO s = WalletsService.gets(id);
        return success(BeanUtils.toBean(s, WalletsRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得代理商钱包分页")
    @PreAuthorize("@ss.hasPermission('wallet:s:query')")
    public CommonResult<PageResult<WalletsRespVO>> getsPage(@Valid WalletsPageReqVO pageReqVO) {
        PageResult<WalletsDO> pageResult = WalletsService.getsPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, WalletsRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出代理商钱包 Excel")
    @PreAuthorize("@ss.hasPermission('wallet:s:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportsExcel(@Valid WalletsPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<WalletsDO> list = WalletsService.getsPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "代理商钱包.xls", "数据", WalletsRespVO.class,
                        BeanUtils.toBean(list, WalletsRespVO.class));
    }
    @GetMapping("/list")
    @Operation(summary = "获得代理商钱包列表")
    @PreAuthorize("@ss.hasPermission('wallet:s:query')")
    public CommonResult<PageResult<walletDetailRespVO>> getWalletList(@Valid @RequestBody WalletPageReqVO walletPageReqVO) {
        PageResult<walletDetailRespVO> pageResult = WalletsService.getWalletList(walletPageReqVO);
        return success(pageResult);
    }
    @GetMapping("/transactions")
    @Operation(summary = "分页查询指定代理商的钱包流水")
    @PreAuthorize("@ss.hasPermission('wallet:s:query')")
    public CommonResult<PageResult<WalletTransactionQueryReqVO>> getTransactions() {
        PageResult<WalletTransactionQueryReqVO> pageResult = WalletsService.getWalletTransactionList();
        return success(pageResult);
    }

    @GetMapping("/agent-wallet")
    @Operation(summary = "获得代理商钱包")
    public CommonResult<WalletsRespVO> getAgentWallet(@RequestParam("agentId") Long agentId) {
        WalletsDO agentWallet = WalletsService.getAgentWallet(agentId);
        return success(BeanUtils.toBean(agentWallet, WalletsRespVO.class));
    }
}