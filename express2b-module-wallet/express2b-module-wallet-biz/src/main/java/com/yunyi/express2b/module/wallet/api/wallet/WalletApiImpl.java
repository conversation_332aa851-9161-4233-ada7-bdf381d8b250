package com.yunyi.express2b.module.wallet.api.wallet;

import com.yunyi.express2b.framework.common.exception.ServerException;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.framework.tenant.core.aop.TenantIgnore;
import com.yunyi.express2b.module.wallet.api.WalletApi.WalletApi;
import com.yunyi.express2b.module.wallet.api.WalletApi.dto.TransactionsSaveReqDO;
import com.yunyi.express2b.module.wallet.api.WalletApi.dto.WalletDetailsDTO;
import com.yunyi.express2b.module.wallet.api.finalClass.WalletConstants;
import com.yunyi.express2b.module.wallet.api.wallet.dto.WalletCreateDTO;
import com.yunyi.express2b.module.wallet.controller.admin.transactions.vo.TransactionsSaveReqVO;
import com.yunyi.express2b.module.wallet.controller.admin.wallets.vo.WalletsSaveReqVO;
import com.yunyi.express2b.module.wallet.dal.dataobject.wallets.WalletsDO;
import com.yunyi.express2b.module.wallet.service.transactions.TransactionsService;
import com.yunyi.express2b.module.wallet.service.wallets.WalletsService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.module.agent.enums.ErrorCodeConstants.AGENT_NOT_EXISTS;
import static com.yunyi.express2b.module.wallet.enums.ErrorCodeConstants.WALLET_NOT_EXISTS;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/5/15 11:57
 */
@Service
@Slf4j
public class WalletApiImpl implements WalletApi {
    @Resource
    private WalletsService walletsService;
    @Resource
    private TransactionsService transactionsService;


    //根据代理商id获取钱包的详情
    @Override
    public WalletDetailsDTO getWalletDetails(Long agentId) {
        // 1. 参数校验
        if (agentId == null) {
            throw exception(AGENT_NOT_EXISTS);
        }
        // 2. 查询钱包信息
        WalletsDO wallet = walletsService.gets(agentId);
        if (wallet == null) {
            throw exception(WALLET_NOT_EXISTS);
        }
        // 3. 转换为DTO
        WalletDetailsDTO walletDetailsDTO= BeanUtils.toBean(wallet, WalletDetailsDTO.class);
        // 4. 返回结果
        return walletDetailsDTO;
    }
    //根据用户id创建用户的钱包
    @Override
    public Long createWallet(Long agentId,Long tenantId) {
        // 1. 参数校验
        if (agentId == null) {
            throw exception(AGENT_NOT_EXISTS);
        }
        if (tenantId == null) {
            throw new ServerException(500,"创建钱包失败");
        }
        // 2. 构建创建钱包DO
        WalletsDO createDTO = new WalletsDO();
        createDTO.setAgentId(agentId);
        createDTO.setAvailableBalance(WalletConstants.WALLET_STATUS_NORMAL); // 初始可用余额为0
        createDTO.setFrozenBalance(WalletConstants.WALLET_STATUS_NORMAL); // 初始冻结余额为0
        createDTO.setTotalProfitEarned(WalletConstants.WALLET_STATUS_NORMAL); // 初始累计利润为0
        createDTO.setTotalCommissionEarned(WalletConstants.WALLET_STATUS_NORMAL); // 初始累计分润为0
        createDTO.setTotalWithdrawnAmount(WalletConstants.WALLET_STATUS_NORMAL); // 初始累计提现为0
        createDTO.setStatus(WalletConstants.TRANSACTION_STATUS_PENDING); // 设置钱包状态
        createDTO.setTenantId(tenantId);
        // 3. 转换为VO对象
        WalletsSaveReqVO saveReqVO= BeanUtils.toBean(createDTO, WalletsSaveReqVO.class);
        // 4. 调用Service创建钱包
        return walletsService.creates(saveReqVO);
    }

    @Override
    public Long createTransactions(TransactionsSaveReqDO createReqVO) {
        // 插入
        TransactionsSaveReqVO transactions = BeanUtils.toBean(createReqVO, TransactionsSaveReqVO.class);
        transactionsService.createTransactions(transactions);
        // 返回
        return transactions.getId();
    }

    /**
     * 更新钱包的冻结余额
     * @param agentId,frozenBalance
     * @return
     * */
    @Override
    public Long updateWallet(Long agentId, Integer frozenBalance) {
        if(agentId==null){
            throw new ServerException(500,"更新钱包失败");
        }
        if(frozenBalance==null){
            throw new ServerException(500,"更新钱包失败");
        }
        WalletsDO agentWallet = walletsService.getAgentWallet(agentId);
        transactionsService.updateWalletForzenBalance(agentId,frozenBalance);
        return agentWallet.getId();
    }

}
