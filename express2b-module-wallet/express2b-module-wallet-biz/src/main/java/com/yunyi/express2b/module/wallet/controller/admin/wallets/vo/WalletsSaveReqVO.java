package com.yunyi.express2b.module.wallet.controller.admin.wallets.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDate;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 代理商钱包新增/修改 Request VO")
@Data
public class WalletsSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "26633")
    private Long id;

    @Schema(description = "代理商用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "24147")
    @NotNull(message = "代理商用户ID不能为空")
    private Long agentId;

    @Schema(description = "可用余额 (单位: 分)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "可用余额 (单位: 分)不能为空")
    private Integer availableBalance;

    @Schema(description = "冻结余额 (单位: 分)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "冻结余额 (单位: 分)不能为空")
    private Integer frozenBalance;

    @Schema(description = "累计利润总额 (单位: 分)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "累计利润总额 (单位: 分)不能为空")
    private Integer totalProfitEarned;

    @Schema(description = "累计分润总额 (单位: 分)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "累计分润总额 (单位: 分)不能为空")
    private Integer totalCommissionEarned;

    @Schema(description = "累计已提现总额 (单位: 分)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "累计已提现总额 (单位: 分)不能为空")
    private Integer totalWithdrawnAmount;

    @Schema(description = "钱包状态 (具体取值见字典说明)", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "钱包状态 (具体取值见字典说明)不能为空")
    private Integer status;

    @Schema(description = "最晚提现日 (针对最早一笔可用资金)")
    private LocalDate lastWithdrawalDeadline;

    @Schema(description = "租户id")
    private Long tenantId;


}