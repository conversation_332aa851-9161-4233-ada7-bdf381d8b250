package com.yunyi.express2b.module.wallet.api.transations;

import cn.hutool.core.collection.CollectionUtil;
import com.yunyi.express2b.framework.common.exception.ServerException;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.module.agent.api.AgentApi;
import com.yunyi.express2b.module.agent.api.dto.AgentActivityDTO;
import com.yunyi.express2b.module.agent.api.dto.UpLevelRuleDTO;
import com.yunyi.express2b.module.wallet.api.TransationsApi.TransactionsApi;
import com.yunyi.express2b.module.wallet.api.TransationsApi.dto.TransactionsDTO;
import com.yunyi.express2b.module.wallet.api.TransationsApi.dto.TransactionsDetailsDTO;
import com.yunyi.express2b.module.wallet.api.WalletApi.dto.TransactionsSaveReqDO;
import com.yunyi.express2b.module.wallet.convert.TransactionsMapStructMapper;
import com.yunyi.express2b.module.wallet.dal.dataobject.transactions.TransactionsDO;
import com.yunyi.express2b.module.wallet.dal.dataobject.wallets.WalletsDO;
import com.yunyi.express2b.module.wallet.dal.mysql.transactions.TransactionsMapper;
import com.yunyi.express2b.module.wallet.dal.mysql.wallets.WalletsMapper;
import com.yunyi.express2b.module.wallet.enums.WalletTransactionEnum;
import com.yunyi.express2b.module.wallet.enums.WalletTransactionTypeEnum;
import com.yunyi.express2b.module.wallet.service.transactions.TransactionsService;
import com.yunyi.express2b.module.wallet.service.wallets.WalletsService;
import com.yunyi.framework.api.login.utils.TransactionNoGenerator;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.module.agent.enums.ConstValue.DEFAULT_ACTIVITY_COUNT;
import static com.yunyi.express2b.module.agent.enums.ErrorCodeConstants.AGENT_NOT_EXISTS;

@Slf4j
@Service
public class TransactionsApiImpl implements TransactionsApi {
    @Resource
    private TransactionsService transactionsService;
    @Resource
    private TransactionsMapper transactionsMapper;
    @Resource
    private AgentApi agentApi;
    @Resource
    private WalletsService walletsService;
    @Resource
    private WalletsMapper walletsMapper;

      /**
       *根据代理商id获取钱包流水表
       * @param agentId
       * @return
       * */
    @Override
    public TransactionsDetailsDTO getTransactionsDetailsByAgentId(Long agentId) {
        // 1. 参数校验
        if (agentId == null) {
            throw exception(AGENT_NOT_EXISTS);
        }
//        查询钱包流水信息
        TransactionsDO transactionsDOs = transactionsService.getTransactionsDetailsByAgentId(agentId);
        // 3. 转换为DTO
        TransactionsDetailsDTO transactionsDetailsDTO = BeanUtils.toBean(transactionsDOs, TransactionsDetailsDTO.class);
        // 4. 返回结果
        return transactionsDetailsDTO;
    }

    /**
     * 根据代理商团队的代理商id查询累计结算总数
     *
     * @param agentIds
     * @return
     */
    @Override
    public List<TransactionsDTO> getAmountSUM(List<Long> agentIds) {
        return BeanUtils.toBean(transactionsMapper.getAmountSUM(agentIds), TransactionsDTO.class);
    }

    /**
     * 根据代理商id获取钱包流水表
     *
     * @param agentId
     * @return
     */
    @Override
    public List<TransactionsDetailsDTO> getTransactionsDetails(Long agentId) {
        List<TransactionsDO> transactionsDOList = transactionsService.getTransactionsDetails(agentId);
        if (CollectionUtil.isEmpty(transactionsDOList)) {
            log.info("未查询到钱包流水信息");
        }
        List<TransactionsDetailsDTO> transactionsDetailsDTOList = TransactionsMapStructMapper.INSTANCE.doListToDtoList(transactionsDOList);
        return transactionsDetailsDTOList;
    }

    /**
     * 根据代理商id查询区段时间钱包流水总额
     *
     * @param agentId
     * @return
     */
    @Override
    public Integer getSumBetweenTime(Long agentId) {
        UpLevelRuleDTO upLevelRuleDTO = agentApi.getUpLevelRule(agentId);
        return transactionsMapper.getSumByBetweenTime(agentId, upLevelRuleDTO.getUpgradeObservationDays());
    }

    /**
     * 获取代理商团队时间区段流水总额
     *
     * @param agentId
     * @return
     */
    @Override
    public Integer getTeamSumBetweenTime(Long agentId) {
        //获取团队ids
        List<Long> list = agentApi.getTeamIdByAgentId(agentId);
        //由于数据库数据不正确所以需要这样判断是否为空，并将本身的代理商id加入列表里，正确的团队关系表数据应有自己与自己的层深为零的数据
        if (list.isEmpty()) {
            list.add(agentId);
        }
        UpLevelRuleDTO upLevelRuleDTO = agentApi.getUpLevelRule(agentId);
        return transactionsMapper.getTeamSumByBetweenTime(list, upLevelRuleDTO.getUpgradeObservationDays());
    }

    /**
     * 坏账处理
     * @param transactionsSaveReqDO
     * @return
     */
    @Override
    @Transactional
    public Long badDebtProcessing(TransactionsSaveReqDO transactionsSaveReqDO) {
        TransactionsDO transactionsDO = new TransactionsDO();
        try {
            transactionsDO.setAgentId(transactionsSaveReqDO.getAgentId());
            Long agentId = transactionsSaveReqDO.getAgentId();
            WalletsDO agentWallet = walletsService.getAgentWallet(agentId);
            Long id = agentWallet.getId();
            transactionsDO.setWalletId(id);
            transactionsDO.setPayOrderSn(TransactionNoGenerator.payOutsNumber("TX", "wx", "eb2", new Date(), Math.abs(transactionsSaveReqDO.getAmount())));
            transactionsDO.setTransactionId(TransactionNoGenerator.payOutsNumber("TX", "wx", "eb2", new Date(), Math.abs(transactionsSaveReqDO.getAmount())));
            transactionsDO.setTransactionType(WalletTransactionTypeEnum.BAD_DEBT_DEDUCTION.getStatus());
            transactionsDO.setAmount(0-transactionsSaveReqDO.getAmount());
            transactionsDO.setBalanceBeforeTransaction(agentWallet.getAvailableBalance() + agentWallet.getFrozenBalance());
            transactionsDO.setBalanceAfterTransaction(agentWallet.getAvailableBalance() + agentWallet.getFrozenBalance() - transactionsDO.getAmount());
            if (transactionsSaveReqDO.getAmount() <= agentWallet.getAvailableBalance()) {
                agentWallet.setAvailableBalance(agentWallet.getAvailableBalance() - transactionsDO.getAmount());
                walletsMapper.updateById(agentWallet);
            }else
            if (transactionsSaveReqDO.getAmount() <= agentWallet.getFrozenBalance() + agentWallet.getAvailableBalance()) {
                Integer availableBalance = transactionsDO.getAmount() - agentWallet.getAvailableBalance();
                agentWallet.setFrozenBalance(agentWallet.getFrozenBalance() - availableBalance);
                agentWallet.setAvailableBalance(0);
                walletsMapper.updateById(agentWallet);
            }else
            if (transactionsSaveReqDO.getAmount() > agentWallet.getFrozenBalance() + agentWallet.getAvailableBalance()) {
                Integer availableBalance = transactionsDO.getAmount() - agentWallet.getAvailableBalance();
                Integer frozenBalance = availableBalance - agentWallet.getFrozenBalance();
                agentWallet.setAvailableBalance(0 - frozenBalance);
                agentWallet.setFrozenBalance(0);
                walletsMapper.updateById(agentWallet);
            }
            // 先改钱包再去改
            transactionsDO.setAvailableBalanceAfterTransaction(agentWallet.getAvailableBalance());
            transactionsDO.setFrozenBalanceAfterTransaction(agentWallet.getFrozenBalance());
            transactionsDO.setRelatedOrderId(transactionsSaveReqDO.getRelatedOrderId());
            transactionsDO.setStatus(WalletTransactionEnum.COMPLETED.getStatus());
            transactionsMapper.insert(transactionsDO);
            return transactionsDO.getId();
        } catch (ServerException e) {
            throw new ServerException(500, "坏账处理失败");
        }
    }

    /**
     * 坏账收回处理
     * @param transactionsSaveReqDO
     * @return
     */
    @Override
    @Transactional
    public Long badDebtRecovery(TransactionsSaveReqDO transactionsSaveReqDO) {
        TransactionsDO transactionsDO = new TransactionsDO();
        try {
            transactionsDO.setAgentId(transactionsSaveReqDO.getAgentId());
            Long agentId = transactionsSaveReqDO.getAgentId();
            WalletsDO agentWallet = walletsService.getAgentWallet(agentId);
            Long id = agentWallet.getId();
            transactionsDO.setWalletId(id);
            transactionsDO.setPayOrderSn(TransactionNoGenerator.payOutsNumber("TX", "wx", "eb2", new Date(), Math.abs(transactionsSaveReqDO.getAmount())));
            transactionsDO.setTransactionId(TransactionNoGenerator.payOutsNumber("TX", "wx", "eb2", new Date(), Math.abs(transactionsSaveReqDO.getAmount())));
            transactionsDO.setTransactionType(WalletTransactionTypeEnum.BAD_DEBT_REFUND.getStatus());
            transactionsDO.setAmount(0-transactionsSaveReqDO.getAmount());
            transactionsDO.setBalanceBeforeTransaction(agentWallet.getAvailableBalance() + agentWallet.getFrozenBalance());
            transactionsDO.setBalanceAfterTransaction(agentWallet.getAvailableBalance() + agentWallet.getFrozenBalance() + transactionsDO.getAmount());
            agentWallet.setAvailableBalance(agentWallet.getAvailableBalance()+transactionsSaveReqDO.getAmount());
            walletsMapper.updateById(agentWallet);
            // 先改钱包再去改
            transactionsDO.setAvailableBalanceAfterTransaction(agentWallet.getAvailableBalance());
            transactionsDO.setFrozenBalanceAfterTransaction(agentWallet.getFrozenBalance());
            transactionsDO.setRelatedOrderId(transactionsSaveReqDO.getRelatedOrderId());
            transactionsDO.setStatus(WalletTransactionEnum.AVAILABLE.getStatus());
            transactionsMapper.insert(transactionsDO);
            return transactionsDO.getId();
        } catch (ServerException e) {
            throw new ServerException(500, "坏账收回处理失败");
        }
    }

    /**
     * 查询代理商及其直属团队钱包流水状态信息
     * @param agentActivityDTO
     * @param teamIds
     * @return
     */
    @Override
    public AgentActivityDTO getAgentWalletTransactionsActivity(AgentActivityDTO agentActivityDTO, List<Long> teamIds) {
        //获取代理商钱包流水状态活跃度
        agentActivityDTO = transactionsMapper.getAgentWalletTransactionsActivity(agentActivityDTO);
        if(teamIds.isEmpty()){
            agentActivityDTO
                    .setWeekTeamBadOrderCount(DEFAULT_ACTIVITY_COUNT)
                    .setMonthTeamBadOrderCount(DEFAULT_ACTIVITY_COUNT)
                    .setLastWeekTeamBadOrderCount(DEFAULT_ACTIVITY_COUNT)
                    .setLastMonthTeamBadOrderCount(DEFAULT_ACTIVITY_COUNT)
                    .setWeekTeamCommissionCount(DEFAULT_ACTIVITY_COUNT)
                    .setMonthTeamCommissionCount(DEFAULT_ACTIVITY_COUNT)
                    .setLastWeekTeamCommissionCount(DEFAULT_ACTIVITY_COUNT)
                    .setLastMonthTeamCommissionCount(DEFAULT_ACTIVITY_COUNT);
        }else {
            agentActivityDTO = transactionsMapper.getTeamWalletTransactionsActivity(agentActivityDTO,teamIds);
        }
        return agentActivityDTO;
    }
}
