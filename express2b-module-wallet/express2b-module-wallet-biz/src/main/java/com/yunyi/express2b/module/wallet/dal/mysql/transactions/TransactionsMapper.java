package com.yunyi.express2b.module.wallet.dal.mysql.transactions;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yunyi.express2b.framework.mybatis.core.mapper.BaseMapperX;
import com.yunyi.express2b.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.yunyi.express2b.module.agent.api.dto.AgentActivityDTO;
import com.yunyi.express2b.module.wallet.dal.dataobject.transactions.TransactionsDO;
import com.yunyi.express2b.module.wallet.enums.WalletTransactionEnum;
import com.yunyi.express2b.module.wallet.enums.WalletTransactionTypeEnum;
import org.apache.ibatis.annotations.Mapper;
import com.yunyi.express2b.module.wallet.controller.admin.transactions.vo.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.yunyi.express2b.module.agent.enums.ConstValue.*;

/**
 * 钱包流水 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TransactionsMapper extends BaseMapperX<TransactionsDO> {

    default PageResult<TransactionsDO> selectPage(TransactionsPageReqVO reqVO) {
        LambdaQueryWrapperX<TransactionsDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(TransactionsDO::getAgentId, reqVO.getAgentId());
        if(reqVO.getPayOrderSn() != null){
            queryWrapper.like(TransactionsDO::getPayOrderSn,reqVO.getPayOrderSn());
        }
        if(reqVO.getTransactionId()!=null){
            queryWrapper.like(TransactionsDO::getTransactionId,reqVO.getTransactionId());
        }
        if(reqVO.getStartTime()!= null&&reqVO.getEndTime()!=null){
        queryWrapper.between(TransactionsDO::getCreateTime, reqVO.getStartTime(), reqVO.getEndTime());}
        // 添加交易类型条件
        queryWrapper.eqIfPresent(TransactionsDO::getTransactionType, reqVO.getTransactionType());
        if (reqVO.getTimeRange() != null) {
            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime startTime = null;
            // 根据时间范围参数计算开始时间
            startTime = endTime.minusDays(reqVO.getTimeRange());
            if (startTime != null) {
                queryWrapper.between(TransactionsDO::getCreateTime, startTime, endTime);
            }
        }
        return selectPage(reqVO, queryWrapper);
    }

  default   TransactionsDO selectTransationsDetailsByAgentId(Long agentId){
        return selectOne(new LambdaQueryWrapperX<TransactionsDO>()
                .eq(TransactionsDO::getAgentId,agentId));
  }




   default List<TransactionsDO> selectFreezeBalanceExpire(Date date){
        return selectList(new LambdaQueryWrapperX<TransactionsDO>()
                .eq(TransactionsDO::getStatus, WalletTransactionEnum.FROZEN)
                .le(TransactionsDO::getAvailableAt,date)
                .eq(TransactionsDO::getIsUnfrozen,0));
   }

   //根据代理商的id查询钱包流水记录
    default List<TransactionsDO> getTransactionsDetails(Long agentId){
        return selectList(new LambdaQueryWrapperX<TransactionsDO>()
                .eq(TransactionsDO::getAgentId,agentId));
    }

    /**
     * 根据团队的代理商id查询团队流水
     * @param agentIds
     * @return
     */
    default List<TransactionsDO> getAmountSUM(List<Long> agentIds){
        return selectList(new LambdaQueryWrapperX<TransactionsDO>()
                .select(TransactionsDO::getAgentId,
                        TransactionsDO::getAmount)
                .in(TransactionsDO::getAgentId,agentIds)
                );
    }

    /**
     * 根据代理商id查询区段时间的流水总数
     * @param agentId
     * @return
     */
    default Integer getSumByBetweenTime(Long agentId,Long day){
        Long orderSum = selectCount(new MPJLambdaWrapperX<TransactionsDO>()
                .eq(TransactionsDO::getAgentId, agentId)
                .between(TransactionsDO::getCreateTime, LocalDateTime.now().minusDays(day), LocalDateTime.now())
        );
        Integer count = orderSum == null ? 0 : orderSum.intValue();
        return  count;
    }

    /**
     * 根据团队代理商id查询时间区段流水总额
     * @param list
     * @return
     */
    default Integer getTeamSumByBetweenTime(List<Long> list,Long day){
        Long orderSum = selectCount(new MPJLambdaWrapperX<TransactionsDO>()
                .in(TransactionsDO::getAgentId, list)
                .between(TransactionsDO::getCreateTime, LocalDateTime.now().minusDays(day), LocalDateTime.now())
        );
        Integer count = orderSum == null ? 0 : orderSum.intValue();
        return count;
    }

    default  PageResult<TransactionsDO> selectAvailableBalance(Integer name){
        LambdaQueryWrapperX<TransactionsDO> queryWrapper = new LambdaQueryWrapperX<>();
        LocalDate endTime = LocalDate.now();
        LocalDate startTime = null;
        // 根据时间范围参数计算开始时间
        startTime = endTime.minusDays(name);
        LambdaQueryWrapper<TransactionsDO> withdrawable = queryWrapper.between(TransactionsDO::getCreateTime, startTime, endTime);
        return selectPage(new TransactionsPageReqVO(),withdrawable);
    }

    /**
     * 查询代理商钱包流水状态分析
     * @return
     */
    default AgentActivityDTO getAgentWalletTransactionsActivity(AgentActivityDTO agentActivityDTO){

        //获取本周代理商钱包流水状态
        Map<String ,Long> weekWalletTransactionsMap = selectList(new LambdaQueryWrapperX<TransactionsDO>()
                .select(TransactionsDO::getTransactionType)
                .eq(TransactionsDO::getAgentId, agentActivityDTO.getAgentId())
                .between(TransactionsDO::getCreateTime, WEEK_START, NOW_TIME)
        ).stream().collect(Collectors.groupingBy(TransactionsDO::getTransactionType,Collectors.counting()));
        Long weekBadOrderCount = weekWalletTransactionsMap.getOrDefault(WalletTransactionTypeEnum.BAD_DEBT_DEDUCTION.getStatus(),DEFAULT_ACTIVITY_COUNT);
        Long weekCommissionCount = weekWalletTransactionsMap.getOrDefault(WalletTransactionTypeEnum.COMMISSION_EARNED.getStatus(),DEFAULT_ACTIVITY_COUNT);

        //获取本月代理商钱包流水状态
        Map<String,Long> monthWalletTransactionsMap = selectList(new LambdaQueryWrapperX<TransactionsDO>()
                .select(TransactionsDO::getTransactionType)
                .eq(TransactionsDO::getAgentId, agentActivityDTO.getAgentId())
                .between(TransactionsDO::getCreateTime, MONTH_START, NOW_TIME)
        ).stream().collect(Collectors.groupingBy(TransactionsDO::getTransactionType,Collectors.counting()));
        Long monthBadOrderCount = monthWalletTransactionsMap.getOrDefault(WalletTransactionTypeEnum.BAD_DEBT_DEDUCTION.getStatus(),DEFAULT_ACTIVITY_COUNT);
        Long monthCommissionCount = monthWalletTransactionsMap.getOrDefault(WalletTransactionTypeEnum.COMMISSION_EARNED.getStatus(),DEFAULT_ACTIVITY_COUNT);

        //获取上周代理商钱包流水状态
        Map<String,Long> lastWeekWalletTransactionsMap = selectList(new LambdaQueryWrapperX<TransactionsDO>()
                .select(TransactionsDO::getTransactionType)
                .eq(TransactionsDO::getAgentId, agentActivityDTO.getAgentId())
                .between(TransactionsDO::getCreateTime, LAST_WEEK_START, LAST_WEEK_END)
        ).stream().collect(Collectors.groupingBy(TransactionsDO::getTransactionType,Collectors.counting()));
        Long lastWeekBadOrderCount = lastWeekWalletTransactionsMap.getOrDefault(WalletTransactionTypeEnum.BAD_DEBT_DEDUCTION.getStatus(),DEFAULT_ACTIVITY_COUNT);
        Long lastWeekCommissionCount = lastWeekWalletTransactionsMap.getOrDefault(WalletTransactionTypeEnum.COMMISSION_EARNED.getStatus(),DEFAULT_ACTIVITY_COUNT);

        //获取上月代理商钱包流水状态
        Map<String,Long> lastMonthWalletTransactionsMap = selectList(new LambdaQueryWrapperX<TransactionsDO>()
                .select(TransactionsDO::getTransactionType)
                .eq(TransactionsDO::getAgentId, agentActivityDTO.getAgentId())
                .between(TransactionsDO::getCreateTime, LAST_MONTH_START, LAST_MONTH_END)
        ).stream().collect(Collectors.groupingBy(TransactionsDO::getTransactionType,Collectors.counting()));
        Long lastMonthBadOrderCount = lastMonthWalletTransactionsMap.getOrDefault(WalletTransactionTypeEnum.BAD_DEBT_DEDUCTION.getStatus(),DEFAULT_ACTIVITY_COUNT);
        Long lastMonthCommissionCount = lastMonthWalletTransactionsMap.getOrDefault(WalletTransactionTypeEnum.COMMISSION_EARNED.getStatus(),DEFAULT_ACTIVITY_COUNT);

        //封装数据
        return agentActivityDTO
                .setWeekBadOrderCount(weekBadOrderCount)
                .setWeekCommissionCount(weekCommissionCount)
                .setMonthBadOrderCount(monthBadOrderCount)
                .setMonthCommissionCount(monthCommissionCount)
                .setLastWeekBadOrderCount(lastWeekBadOrderCount)
                .setLastWeekCommissionCount(lastWeekCommissionCount)
                .setLastMonthBadOrderCount(lastMonthBadOrderCount)
                .setLastMonthCommissionCount(lastMonthCommissionCount);
    }

    /**
     * 查询代理商直属团队钱包流水状态数据
     * @param agentActivityDTO
     * @param teamIds
     * @return
     */
    default AgentActivityDTO getTeamWalletTransactionsActivity(AgentActivityDTO agentActivityDTO ,List<Long> teamIds){
        //获取本周代理商团队钱包流水状态
        Map<String ,Long> weekWalletTransactionsMap = selectList(new LambdaQueryWrapperX<TransactionsDO>()
                .select(TransactionsDO::getTransactionType)
                .in(TransactionsDO::getAgentId, teamIds)
                .between(TransactionsDO::getCreateTime, WEEK_START, NOW_TIME)
        ).stream().collect(Collectors.groupingBy(TransactionsDO::getTransactionType,Collectors.counting()));
        Long weekTeamBadOrderCount = weekWalletTransactionsMap.getOrDefault(WalletTransactionTypeEnum.BAD_DEBT_DEDUCTION.getStatus(),DEFAULT_ACTIVITY_COUNT);
        Long weekTeamCommissionCount = weekWalletTransactionsMap.getOrDefault(WalletTransactionTypeEnum.COMMISSION_EARNED.getStatus(),DEFAULT_ACTIVITY_COUNT);
        //获取本月代理商团队钱包流水状态
        Map<String,Long> monthWalletTransactionsMap = selectList(new LambdaQueryWrapperX<TransactionsDO>()
                .select(TransactionsDO::getTransactionType)
                .in(TransactionsDO::getAgentId, teamIds)
                .between(TransactionsDO::getCreateTime, MONTH_START, NOW_TIME)
        ).stream().collect(Collectors.groupingBy(TransactionsDO::getTransactionType,Collectors.counting()));
        Long monthTeamBadOrderCount = monthWalletTransactionsMap.getOrDefault(WalletTransactionTypeEnum.BAD_DEBT_DEDUCTION.getStatus(),DEFAULT_ACTIVITY_COUNT);
        Long monthTeamCommissionCount = monthWalletTransactionsMap.getOrDefault(WalletTransactionTypeEnum.COMMISSION_EARNED.getStatus(),DEFAULT_ACTIVITY_COUNT);
        //获取上周代理商团队钱包流水状态
        Map<String,Long> lastWeekWalletTransactionsMap = selectList(new LambdaQueryWrapperX<TransactionsDO>()
                .select(TransactionsDO::getTransactionType)
                .in(TransactionsDO::getAgentId, teamIds)
                .between(TransactionsDO::getCreateTime, LAST_WEEK_START, LAST_WEEK_END)
        ).stream().collect(Collectors.groupingBy(TransactionsDO::getTransactionType,Collectors.counting()));
        Long lastWeekTeamBadOrderCount = lastWeekWalletTransactionsMap.getOrDefault(WalletTransactionTypeEnum.BAD_DEBT_DEDUCTION.getStatus(),DEFAULT_ACTIVITY_COUNT);
        Long lastWeekTeamCommissionCount = lastWeekWalletTransactionsMap.getOrDefault(WalletTransactionTypeEnum.COMMISSION_EARNED.getStatus(),DEFAULT_ACTIVITY_COUNT);
        //获取上月代理商团队钱包流水状态
        Map<String,Long> lastMonthWalletTransactionsMap = selectList(new LambdaQueryWrapperX<TransactionsDO>()
                .select(TransactionsDO::getTransactionType)
                .in(TransactionsDO::getAgentId, teamIds)
                .between(TransactionsDO::getCreateTime, LAST_MONTH_START, LAST_MONTH_END)
        ).stream().collect(Collectors.groupingBy(TransactionsDO::getTransactionType,Collectors.counting()));
        Long lastMonthTeamBadOrderCount = lastMonthWalletTransactionsMap.getOrDefault(WalletTransactionTypeEnum.BAD_DEBT_DEDUCTION.getStatus(),DEFAULT_ACTIVITY_COUNT);
        Long lastMonthTeamCommissionCount = lastMonthWalletTransactionsMap.getOrDefault(WalletTransactionTypeEnum.COMMISSION_EARNED.getStatus(),DEFAULT_ACTIVITY_COUNT);
        //封装数据
        return agentActivityDTO
                .setWeekTeamBadOrderCount(weekTeamBadOrderCount)
                .setWeekTeamCommissionCount(weekTeamCommissionCount)
                .setMonthTeamBadOrderCount(monthTeamBadOrderCount)
                .setMonthTeamCommissionCount(monthTeamCommissionCount)
                .setLastWeekTeamBadOrderCount(lastWeekTeamBadOrderCount)
                .setLastWeekTeamCommissionCount(lastWeekTeamCommissionCount)
                .setLastMonthTeamBadOrderCount(lastMonthTeamBadOrderCount)
                .setLastMonthTeamCommissionCount(lastMonthTeamCommissionCount);
    }

}