package com.yunyi.express2b.module.wallet.service.transactions;

import com.yunyi.express2b.module.infra.api.config.ConfigApi;
import com.yunyi.express2b.module.wallet.dal.dataobject.wallets.WalletsDO;
import com.yunyi.express2b.module.wallet.dal.mysql.wallets.WalletsMapper;
import com.yunyi.express2b.module.wallet.enums.WalletTransactionEnum;
import com.yunyi.express2b.module.wallet.enums.WalletTransactionTypeEnum;
import com.yunyi.framework.api.login.utils.TransactionNoGenerator;
import org.apache.ibatis.annotations.Param;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import com.yunyi.express2b.module.wallet.controller.admin.transactions.vo.*;
import com.yunyi.express2b.module.wallet.dal.dataobject.transactions.TransactionsDO;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;

import com.yunyi.express2b.module.wallet.dal.mysql.transactions.TransactionsMapper;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.module.wallet.enums.ErrorCodeConstants.*;

/**
 * 钱包流水 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TransactionsServiceImpl implements TransactionsService {

    @Resource
    private TransactionsMapper transactionsMapper;
    @Resource
    private WalletsMapper walletsMapper;
    @Resource
    private ConfigApi configApi;

    @Override
    public Long createTransactions(TransactionsSaveReqVO createReqVO) {
        // 插入
        TransactionsDO transactions = BeanUtils.toBean(createReqVO, TransactionsDO.class);
        String thawTime = configApi.getConfigValueByKey("thaw_time");
        transactions.setAvailableAt(LocalDateTime.now().plusDays(Integer.parseInt(thawTime)));
        transactionsMapper.insert(transactions);
        // 返回
        return transactions.getId();
    }

    @Override
    public void updateTransactions(TransactionsSaveReqVO updateReqVO) {
        // 校验存在
        validateTransactionsExists(updateReqVO.getId());
        // 更新
        TransactionsDO updateObj = BeanUtils.toBean(updateReqVO, TransactionsDO.class);
        transactionsMapper.updateById(updateObj);
    }

    @Override
    public void deleteTransactions(Long id) {
        // 校验存在
        validateTransactionsExists(id);
        // 删除
        transactionsMapper.deleteById(id);
    }

    private void validateTransactionsExists(Long id) {
        if (transactionsMapper.selectById(id) == null) {
            throw exception(TRANSACTIONS_NOT_EXISTS);
        }
    }


    /**
     * 根据id获取交易数据
     * @param id 交易id
     * @return
     * */
    @Override
    public TransactionsDO getTransactions(Long id) {
        TransactionsDO transactionsDO = transactionsMapper.selectById(id);
        String transactionType = transactionsDO.getTransactionType();
        transactionsDO.setTransactionType(WalletTransactionTypeEnum.getNameByStatus(transactionType));
        String status = transactionsDO.getStatus();
        transactionsDO.setStatus(WalletTransactionEnum.getNameByStatus(status));
        return transactionsDO;
    }

    /**
     * 获取交易分页数据并转换枚举状态为可读名称
     *
     * @param pageReqVO 分页查询条件，包含分页参数及筛选条件
     * @return 包含转换后交易数据的分页结果，其中交易类型和状态字段已被转换为对应枚举名称
     */
    @Override
    public PageResult<TransactionsDO> getTransactionsPage(TransactionsPageReqVO pageReqVO) {
        PageResult<TransactionsDO> transactionsDOPageResult = transactionsMapper.selectPage(pageReqVO);
        List<TransactionsDO> list = transactionsDOPageResult.getList();
        for (TransactionsDO transactionsDO : list) {
            String transactionType = transactionsDO.getTransactionType();
            transactionsDO.setTransactionType(WalletTransactionTypeEnum.getNameByStatus(transactionType));
            String status = transactionsDO.getStatus();
            transactionsDO.setStatus(WalletTransactionEnum.getNameByStatus(status));
        }
        return transactionsDOPageResult;
    }

    /**
     * 获取交易详情
     *
     * @param agentId
     * @return
     */
    @Override
    public TransactionsDO getTransactionsDetailsByAgentId(Long agentId) {
        return transactionsMapper.selectTransationsDetailsByAgentId(agentId);
    }



    /**
     * 解冻
     * */

    @Override
    @Transactional

    public void FreezeBalance() {
        // 获取当前时间并转换为LocalDateTime
        LocalDateTime now = LocalDateTime.now();
//       获取全部符合条件的数据
        List<TransactionsDO> transactionsDOList = transactionsMapper.selectFreezeBalanceExpire(new Date());
        if (!transactionsDOList.isEmpty()) {
            for (TransactionsDO originalTransaction : transactionsDOList) {
                originalTransaction.setIsUnfrozen(1);
                transactionsMapper.updateById(originalTransaction);
                TransactionsDO newTransaction = new TransactionsDO();
                // 先复制所有属性
                BeanUtils.copyProperties(originalTransaction, newTransaction);
                // 再手动重置id
                newTransaction.setId(null);
//                平台唯一交易流水号加一
                String transactionId = TransactionNoGenerator.payOutsNumber("TX", "wx", "eb2", new Date(), Math.abs(newTransaction.getAmount()));
                newTransaction.setTransactionId(transactionId);
//                设置解冻状态
                newTransaction.setStatus(String.valueOf(WalletTransactionEnum.AVAILABLE));
                WalletsDO walletsDO = walletsMapper.selectWalletsByAgentId(originalTransaction.getAgentId());
                newTransaction.setAvailableBalanceAfterTransaction(walletsDO.getAvailableBalance()+originalTransaction.getAmount());
                newTransaction.setFrozenBalanceAfterTransaction(walletsDO.getFrozenBalance()-originalTransaction.getAmount());
                newTransaction.setCreateTime(now);
                newTransaction.setIsUnfrozen(1);
                transactionsMapper.insert(newTransaction);
                Long agentId = newTransaction.getAgentId();
                // 获取流水的冻结余额
                Integer frozenBalanceAfterTransaction = originalTransaction.getFrozenBalanceAfterTransaction();
                updateWallet(agentId,frozenBalanceAfterTransaction);
            }
        }
    }

    /**
     * 获取钱包流水详情
     * */
    @Override
    public List<TransactionsDO> getTransactionsDetails(Long agentId) {
        List<TransactionsDO> transactionsDOList = transactionsMapper.getTransactionsDetails(agentId);
        return transactionsDOList;
    }

    /**
     * 更新钱包的冻结余额
     * */
    @Override
    public void updateWalletForzenBalance(Long agentId, Integer frozenBalance) {
        walletsMapper.updateWalletForzenBalance(agentId,frozenBalance);
    }

    /**
     * 解冻的更新钱包
     * */
    public void updateWallet(Long agentId,Integer frozenBalanceAfterTransaction){
        // 获取钱包
        WalletsDO walletsDO = walletsMapper.selectWalletsByAgentId(agentId);
//                获取钱包的冻结余额
        Integer frozenBalance = walletsDO.getFrozenBalance();
        // 更新钱包的冻结余额
        int i = frozenBalance - frozenBalanceAfterTransaction;
        walletsDO.setFrozenBalance(i);
        walletsDO.setAvailableBalance(walletsDO.getAvailableBalance() + frozenBalanceAfterTransaction);
        walletsMapper.updateById(walletsDO);
    }
}