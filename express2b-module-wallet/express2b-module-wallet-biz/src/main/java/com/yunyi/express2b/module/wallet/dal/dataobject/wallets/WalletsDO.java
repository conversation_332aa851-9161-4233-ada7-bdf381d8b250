package com.yunyi.express2b.module.wallet.dal.dataobject.wallets;

import lombok.*;

import java.time.LocalDate;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.yunyi.express2b.framework.mybatis.core.dataobject.BaseDO;

/**
 * 代理商钱包 DO
 *
 * <AUTHOR>
 */
@TableName("wallets_agent")
@KeySequence("agent_wallets_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WalletsDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 代理商用户ID
     */
    private Long agentId;
    /**
     * 可用余额 (单位: 分)
     */
    private Integer availableBalance;
    /**
     * 冻结余额 (单位: 分)
     */
    private Integer frozenBalance;
    /**
     * 累计利润总额 (单位: 分)
     */
    private Integer totalProfitEarned;
    /**
     * 累计分润总额 (单位: 分)
     */
    private Integer totalCommissionEarned;
    /**
     * 累计已提现总额 (单位: 分)
     */
    private Integer totalWithdrawnAmount;
    /**
     * 钱包状态 (具体取值见字典说明)
     */
    private Integer status;
    /**
     * 最晚提现日 (针对最早一笔可用资金)
     */
    private LocalDate lastWithdrawalDeadline;

    /**
     * 租户id
     * */
    private Long tenantId;

}