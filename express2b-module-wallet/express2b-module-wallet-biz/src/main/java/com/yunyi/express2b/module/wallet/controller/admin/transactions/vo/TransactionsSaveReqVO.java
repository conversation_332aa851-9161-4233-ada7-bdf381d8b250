package com.yunyi.express2b.module.wallet.controller.admin.transactions.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 钱包流水新增/修改 Request VO")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransactionsSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "8215")
    private Long id;

    @Schema(description = "钱包ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "10144")
    @NotNull(message = "钱包ID不能为空")
    private Long walletId;

    @Schema(description = "代理商用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "15490")
    @NotNull(message = "代理商用户ID不能为空")
    private Long agentId;

    @Schema(description = "统一登陆平台的订单支付编号")
    private String payOrderSn;

    @Schema(description = "平台唯一交易流水号", requiredMode = Schema.RequiredMode.REQUIRED, example = "30384")
    @NotEmpty(message = "平台唯一交易流水号不能为空")
    private String transactionId;

    @Schema(description = "交易类型 (具体取值见字典说明)", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotEmpty(message = "交易类型 (具体取值见字典说明)不能为空")
    private String transactionType;

    @Schema(description = "交易金额 (正为收入,负为支出) (单位: 分)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "交易金额 (正为收入,负为支出) (单位: 分)不能为空")
    private Integer amount;

    @Schema(description = "交易前账户总余额 (可用+冻结) (单位: 分)")
    private Integer balanceBeforeTransaction;

    @Schema(description = "交易后账户总余额 (可用+冻结) (单位: 分)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "交易后账户总余额 (可用+冻结) (单位: 分)不能为空")
    private Integer balanceAfterTransaction;

    @Schema(description = "交易后可用余额 (单位: 分)")
    private Integer availableBalanceAfterTransaction;

    @Schema(description = "交易后冻结余额 (单位: 分)")
    private Integer frozenBalanceAfterTransaction;

    @Schema(description = "关联订单号", example = "24563")
    private String relatedOrderId;

    @Schema(description = "关联提现申请ID", example = "8490")
    private Long relatedWithdrawalId;

    @Schema(description = "关联风险事件ID或其他业务ID", example = "13849")
    private String relatedEventId;

    @Schema(description = "流水状态 (具体取值见字典说明)", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotEmpty(message = "流水状态 (具体取值见字典说明)不能为空")
    private String status;

    @Schema(description = "交易描述", example = "你猜")
    private String description;

    @Schema(description = "资金预计可用时间 (解冻时间)")
    private LocalDateTime availableAt;

    @Schema(description = "备注信息 (如操作人,调账原因)", example = "你猜")
    private String remark;

    @Schema(description = "操作人")
    private String creator;

}