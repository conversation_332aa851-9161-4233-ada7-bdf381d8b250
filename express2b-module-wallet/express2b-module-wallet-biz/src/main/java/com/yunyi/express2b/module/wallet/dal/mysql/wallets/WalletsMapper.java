package com.yunyi.express2b.module.wallet.dal.mysql.wallets;

import java.util.*;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yunyi.express2b.framework.mybatis.core.mapper.BaseMapperX;
import com.yunyi.express2b.module.wallet.dal.dataobject.wallets.WalletsDO;
import org.apache.ibatis.annotations.Mapper;
import com.yunyi.express2b.module.wallet.controller.admin.wallets.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 代理商钱包 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface WalletsMapper extends BaseMapperX<WalletsDO> {

    default PageResult<WalletsDO> selectPage(WalletsPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<WalletsDO>()
                .eqIfPresent(WalletsDO::getAgentId, reqVO.getAgentId())
                .eqIfPresent(WalletsDO::getAvailableBalance, reqVO.getAvailableBalance())
                .eqIfPresent(WalletsDO::getFrozenBalance, reqVO.getFrozenBalance())
                .eqIfPresent(WalletsDO::getTotalProfitEarned, reqVO.getTotalProfitEarned())
                .eqIfPresent(WalletsDO::getTotalCommissionEarned, reqVO.getTotalCommissionEarned())
                .eqIfPresent(WalletsDO::getTotalWithdrawnAmount, reqVO.getTotalWithdrawnAmount())
                .eqIfPresent(WalletsDO::getStatus, reqVO.getStatus())
                .eqIfPresent(WalletsDO::getLastWithdrawalDeadline, reqVO.getLastWithdrawalDeadline())
                .betweenIfPresent(WalletsDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(WalletsDO::getId));
    }

    default WalletsDO selectWalletByAgentId(Long loginUserId){
        return selectOne(new LambdaQueryWrapperX<WalletsDO>()
                .eq(WalletsDO::getAgentId, loginUserId));
    }


   default WalletsDO selectWalletsByAgentId(Long agentId){
            return selectOne(new LambdaQueryWrapperX<WalletsDO>()
                    .eq(WalletsDO::getAgentId, agentId));
   }

   default WalletsDO selectAgentWalletByAgentId(Long agentId){
        return selectOne(new LambdaQueryWrapperX<WalletsDO>()
                .eq(WalletsDO::getAgentId, agentId));
   }

//    default void updateWalletForzenBalance(Long agentId, Integer frozenBalance){
//        LambdaUpdateWrapper<WalletsDO> wrapper = new LambdaUpdateWrapper<>();
//        wrapper.set(WalletsDO::getFrozenBalance, frozenBalance)
//                .eq(WalletsDO::getAgentId, agentId);
//    }

    default int updateWalletForzenBalance(Long agentId, Integer frozenBalance){
        return update(new WalletsDO(),new LambdaUpdateWrapper<WalletsDO>()
                .set(WalletsDO::getFrozenBalance, frozenBalance)
                .eq(WalletsDO::getAgentId, agentId));
    }
}