package com.yunyi.express2b.module.wallet.api.wallet.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;

/**
 * WalletDetailsDTO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/5/15 11:51
 */

@Data
public class WalletCreateDTO {

    /**
     * 代理商用户ID
     */
    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "26633")
    private Long id;

    @NotNull(message = "代理商用户ID不能为空")
    private Long agentId;

    @NotNull(message = "可用余额 (单位: 分)不能为空")
    private Integer availableBalance;

    @NotNull(message = "冻结余额 (单位: 分)不能为空")
    private Integer frozenBalance;

    @NotNull(message = "累计利润总额 (单位: 分)不能为空")
    private Integer totalProfitEarned;

    @NotNull(message = "累计分润总额 (单位: 分)不能为空")
    private Integer totalCommissionEarned;

    @NotNull(message = "累计已提现总额 (单位: 分)不能为空")
    private Integer totalWithdrawnAmount;

    @NotNull(message = "钱包状态 (具体取值见字典说明)不能为空")
    private Integer status;

    private Long tenantId;


}
