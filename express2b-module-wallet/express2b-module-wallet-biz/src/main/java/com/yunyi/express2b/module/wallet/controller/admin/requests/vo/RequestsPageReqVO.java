package com.yunyi.express2b.module.wallet.controller.admin.requests.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yunyi.express2b.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.yunyi.express2b.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 提现申请分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class RequestsPageReqVO extends PageParam {

    @Schema(description = "钱包ID", example = "27708")
    private Long walletId;

    @Schema(description = "代理商用户ID", example = "19995")
    private Long agentId;

    @Schema(description = "提现申请唯一编号")
    private String requestNo;

    @Schema(description = "申请提现金额 (单位: 分)")
    private Integer amount;

    @Schema(description = "实际到账金额 (扣除手续费后) (单位: 分)")
    private Integer actualAmount;

    @Schema(description = "手续费金额 (单位: 分)")
    private Integer feeAmount;

    @Schema(description = "提现状态 (具体取值见字典说明)", example = "2")
    private String status;

    @Schema(description = "提现渠道 (WECHAT_PROFIT_SHARING)")
    private String channel;

    @Schema(description = "渠道账户信息 (如微信OpenID, 商户号)")
    private String channelAccountInfo;

    @Schema(description = "渠道交易号 (微信分账订单号/批次号)", example = "27661")
    private String channelTransactionId;

    @Schema(description = "失败原因", example = "不对")
    private String failureReason;

    @Schema(description = "审核备注", example = "随便")
    private String auditRemark;

    @Schema(description = "审核员ID", example = "30451")
    private Long auditorId;

    @Schema(description = "申请时间")
    private LocalDateTime requestedAt;

    @Schema(description = "审核时间")
    private LocalDateTime auditedAt;

    @Schema(description = "开始处理时间（调用三方接口）")
    private LocalDateTime processingStartedAt;

    @Schema(description = "完成时间/失败时间")
    private LocalDateTime completedAt;

    @Schema(description = "是否自动提现触发")
    private Boolean isAutoTriggered;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

}