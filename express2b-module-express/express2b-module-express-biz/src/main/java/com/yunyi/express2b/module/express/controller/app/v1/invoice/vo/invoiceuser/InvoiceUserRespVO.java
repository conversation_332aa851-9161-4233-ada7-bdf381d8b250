package com.yunyi.express2b.module.express.controller.app.v1.invoice.vo.invoiceuser;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 用户开票信息登记 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InvoiceUserRespVO {

    @Schema(description = "记录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "10829")
    @ExcelProperty("记录ID")
    private Long id;

    @Schema(description = "用户ID对应统一登录平台的ssoUserId", requiredMode = Schema.RequiredMode.REQUIRED, example = "9189")
    @ExcelProperty("用户ID对应统一登录平台的ssoUserId")
    private Long memberId;

    @Schema(description = "发票抬头（单位或个人名称）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("发票抬头（单位或个人名称）")
    private String invoiceTitle;

    @Schema(description = "纳税人识别号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("纳税人识别号")
    private String taxNumber;

    @Schema(description = "单位地址")
    @ExcelProperty("单位地址")
    private String companyAddress;

    @Schema(description = "单位电话")
    @ExcelProperty("单位电话")
    private String companyPhone;

    @Schema(description = "开户行", example = "王五")
    @ExcelProperty("开户行")
    private String bankName;

    @Schema(description = "银行基本户账号", example = "8473")
    @ExcelProperty("银行基本户账号")
    private String bankAccount;

    @Schema(description = "接收邮箱", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("接收邮箱")
    private String email;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}