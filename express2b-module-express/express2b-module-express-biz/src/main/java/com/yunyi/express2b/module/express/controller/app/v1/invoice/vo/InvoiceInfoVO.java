package com.yunyi.express2b.module.express.controller.app.v1.invoice.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "开票信息")
public class InvoiceInfoVO {

    @Schema(description = "发票抬头（单位或个人名称）", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private String invoiceTitle;

    @Schema(description = "纳税人识别号", requiredMode = Schema.RequiredMode.REQUIRED, example = "*********")
    private String taxNumber;

    @Schema(description = "接收邮箱", requiredMode = Schema.RequiredMode.REQUIRED, example = "<EMAIL>")
    private String email;
}
