package com.yunyi.express2b.module.express.service.order;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.framework.security.core.util.SecurityFrameworkUtils;
import com.yunyi.express2b.module.agent.api.AgentApi;
import com.yunyi.express2b.module.agent.api.dto.AgentInfoDTO;
import com.yunyi.express2b.module.crm.api.card.paycard.vo.PayCardVo;
import com.yunyi.express2b.module.crm.api.card.refundcard.RefundCardApi;
import com.yunyi.express2b.module.express.controller.admin.order.dto.OrderCallBackRequestDTO;
import com.yunyi.express2b.module.express.controller.admin.order.vo.OrderCancelVO;
import com.yunyi.express2b.module.express.controller.admin.order.vo.OrderSaveReqVO;
import com.yunyi.express2b.module.express.controller.admin.refundorder.vo.RefundOrderSaveReqVO;
import com.yunyi.express2b.module.express.dal.dataobject.order.OrderDO;
import com.yunyi.express2b.module.express.dal.dataobject.paymentorder.PaymentOrderDO;
import com.yunyi.express2b.module.express.dal.mysql.order.OrderMapper;
import com.yunyi.express2b.module.express.dal.mysql.paymentorder.PaymentOrderMapper;
import com.yunyi.express2b.module.express.enums.ErrorCodeConstants;
import com.yunyi.express2b.module.express.enums.OrderCallbackStatusEnum;
import com.yunyi.express2b.module.express.enums.OrderStatusTypeEnum;
import com.yunyi.express2b.module.express.enums.PromotionEnum;
import com.yunyi.express2b.module.express.enums.social.ExpressLogRecordConstants;
import com.yunyi.express2b.module.express.mapstruct.OrderMapStructMapper;
import com.yunyi.express2b.module.express.mq.producer.OrderProfitSharingProducer;
import com.yunyi.express2b.module.express.mq.producer.OrderStatusProducer;
import com.yunyi.express2b.module.express.service.order.bo.OrderCallBackRequestBO;
import com.yunyi.express2b.module.express.service.pay.RefundService;
import com.yunyi.express2b.module.express.service.sendmessage.MessageUtils;
import com.yunyi.express2b.module.express.utils.OrderConstants;
import com.yunyi.express2b.module.infra.api.config.ConfigApi;
import com.yunyi.express2b.module.pricing.api.NewTemplateApi;
import com.yunyi.express2b.module.pricing.api.dto.DetailShareReqDTO;
import com.yunyi.express2b.module.pricing.api.vo.PricingRuleApiPageReqVO;
import com.yunyi.express2b.module.wallet.api.TransationsApi.TransactionsApi;
import com.yunyi.express2b.module.wallet.api.WalletApi.dto.TransactionsSaveReqDO;
import com.yunyi.framework.api.logistics.api.order.vo.CancelOrderRequest;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.Executor;

import static com.alibaba.druid.util.StringUtils.isEmpty;
import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.module.express.enums.ErrorCodeConstants.ORDER_NOT_EXISTS;
import static com.yunyi.express2b.module.express.enums.ErrorCodeConstants.ORDER_STATUS_INVALID;

@Service
@Slf4j
public class OrderLifecycleServiceImpl implements OrderLifecycleService {

    @Resource
    private OrderMapper orderMapper;
    @Resource
    private PaymentOrderMapper paymentOrderMapper;
    @Resource
    private RefundService refundService;
    @Resource
    private RefundCardApi refundCardApi;
    @Resource
    private OrderPromotionService orderPromotionService;
    @Resource
    private OrderLogisticsService orderLogisticsService;

    @Resource
    @Qualifier("myUpdateOrderStatusExecutor")
    private Executor myUpdateOrderStatusExecutor;
    @Resource
    @Qualifier("myUpdatePriceExecutor")
    private Executor myUpdatePriceExecutor;
    @Resource
    private AgentApi agentApi;
    @Resource
    private ConfigApi configApi;
    @Resource
    private MessageUtils messageUtils;
    @Resource
    private TransactionsApi transactionsApi;

    @Override
    @LogRecord(type = ExpressLogRecordConstants.ORDER_CANCEL,
            fail = "{{#orderCancelVO.orderNo}}-订单取消失败:，失败原因:{{#_errorMsg}}",
            success = "接收参数:{{#orderCancelVO}}}",
            bizNo = "{{#userId}}")
    @Transactional(rollbackFor = Exception.class)
    public OrderCancelVO cancelOrder(OrderCancelVO orderCancelVO) {
        LogRecordContext.putVariable("userId", SecurityFrameworkUtils.getLoginUserId());
        log.info("【开始执行取消订单的方法--------------】：{}", orderCancelVO);
        LogRecordContext.putVariable("orderCancelVO", orderCancelVO);

        OrderDO order = orderMapper.selectOrder(orderCancelVO.getOrderNo());
        if (ObjectUtil.isNull(order)) {
            throw exception(ErrorCodeConstants.ORDER_NOT_EXISTS);
        }

        if (!isCancellable(order.getStatus())) {
            throw exception(ErrorCodeConstants.ORDER_STATUS_INVALID);
        }

        OrderStatusTypeEnum status = OrderStatusTypeEnum.valueOf(order.getStatus());
        switch (status) {
            case CREATED -> handleCancelCreatedOrder(order);
            case PAID -> handleCancelPaidOrder(order);
            case WAIT -> handleCancelWaitOrder(order);
            case PENDING_ADDITIONAL_FEE -> handleCancelPendingFeeOrder(order);
            case PICKUP_PENDING -> handleCancelPickupPendingOrder(order);
            default -> throw exception(ORDER_STATUS_INVALID);
        }
        return orderCancelVO;
    }

    private boolean isCancellable(String status) {
        return Arrays.asList(
                OrderStatusTypeEnum.CREATED.getStatus(),
                OrderStatusTypeEnum.PAID.getStatus(),
                OrderStatusTypeEnum.WAIT.getStatus(),
                OrderStatusTypeEnum.PENDING_ADDITIONAL_FEE.getStatus(),
                OrderStatusTypeEnum.PICKUP_PENDING.getStatus()
        ).contains(status);
    }

    private void handleCancelCreatedOrder(OrderDO order) {
        updateOrderStatusByNos(order.getOrderNo(), OrderStatusTypeEnum.CANCELED.getStatus());
        recordStatusChange(order.getId(), order.getOrderNo(), OrderStatusTypeEnum.CREATED.getStatus(), OrderStatusTypeEnum.CANCELED.getStatus());
        refundCardIfNecessary(order);
    }

    private void handleCancelPaidOrder(OrderDO order) {
        updateOrderStatusByNos(order.getOrderNo(), OrderStatusTypeEnum.CANCELED_AFTER_PAYMENT.getStatus());
        recordStatusChange(order.getId(), order.getOrderNo(), OrderStatusTypeEnum.PAID.getStatus(), OrderStatusTypeEnum.CANCELED_AFTER_PAYMENT.getStatus());
        orderPromotionService.asyncNotifyCancelOrder(order);
        createRefundOrders(order);
        updateOrderStatusByNos(order.getOrderNo(), OrderStatusTypeEnum.REFUND_PROCESSING.getStatus());
        recordStatusChange(order.getId(), order.getOrderNo(), OrderStatusTypeEnum.CANCELED_AFTER_PAYMENT.getStatus(), OrderStatusTypeEnum.REFUND_PROCESSING.getStatus());
        refundCardIfNecessary(order);
    }

    private void handleCancelWaitOrder(OrderDO order) {
        callUcmToCancel(order);
        updateOrderStatusByNos(order.getOrderNo(), OrderStatusTypeEnum.CANCELED_AFTER_PAYMENT.getStatus());
        recordStatusChange(order.getId(), order.getOrderNo(), OrderStatusTypeEnum.WAIT.getStatus(), OrderStatusTypeEnum.CANCELED_AFTER_PAYMENT.getStatus());
        orderPromotionService.asyncNotifyCancelOrder(order);
        createRefundOrders(order);
        updateOrderStatusByNos(order.getOrderNo(), OrderStatusTypeEnum.REFUND_PROCESSING.getStatus());
        recordStatusChange(order.getId(), order.getOrderNo(), OrderStatusTypeEnum.CANCELED_AFTER_PAYMENT.getStatus(), OrderStatusTypeEnum.REFUND_PROCESSING.getStatus());
    }

    private void handleCancelPendingFeeOrder(OrderDO order) {
        callUcmToCancel(order);
        updateOrderStatusByNos(order.getOrderNo(), OrderStatusTypeEnum.CANCELED_AFTER_PAYMENT.getStatus());
        recordStatusChange(order.getId(), order.getOrderNo(), OrderStatusTypeEnum.PENDING_ADDITIONAL_FEE.getStatus(), OrderStatusTypeEnum.CANCELED_AFTER_PAYMENT.getStatus());
        orderPromotionService.asyncNotifyCancelOrder(order);
        createRefundOrders(order);
        updateOrderStatusByNos(order.getOrderNo(), OrderStatusTypeEnum.REFUND_PROCESSING.getStatus());
        recordStatusChange(order.getId(), order.getOrderNo(), OrderStatusTypeEnum.CANCELED_AFTER_PAYMENT.getStatus(), OrderStatusTypeEnum.REFUND_PROCESSING.getStatus());
    }

    private void handleCancelPickupPendingOrder(OrderDO order) {
        callUcmToCancel(order);
        updateOrderStatusByNos(order.getOrderNo(), OrderStatusTypeEnum.CANCELED_AFTER_PAYMENT.getStatus());
        recordStatusChange(order.getId(), order.getOrderNo(), OrderStatusTypeEnum.PICKUP_PENDING.getStatus(), OrderStatusTypeEnum.CANCELED_AFTER_PAYMENT.getStatus());
        orderPromotionService.asyncNotifyCancelOrder(order);
        createRefundOrders(order);
        updateOrderStatusByNos(order.getOrderNo(), OrderStatusTypeEnum.REFUND_PROCESSING.getStatus());
        recordStatusChange(order.getId(), order.getOrderNo(), OrderStatusTypeEnum.CANCELED_AFTER_PAYMENT.getStatus(), OrderStatusTypeEnum.REFUND_PROCESSING.getStatus());
    }


    private void callUcmToCancel(OrderDO order) {
        CancelOrderRequest cancelOrderRequest = new CancelOrderRequest();
        cancelOrderRequest.setUcmOrderSn(order.getUcmOrderSn());
        cancelOrderRequest.setCancelMsg("取消");
        orderLogisticsService.ucmCancelOrder(cancelOrderRequest);
    }


    private void refundCardIfNecessary(OrderDO order) {
        if (order.getCardNumber() != null) {
            PayCardVo payCardVo = new PayCardVo();
            payCardVo.setOrderId(order.getId());
            payCardVo.setCardNumber(order.getCardNumber());
            refundCardApi.refundCard(payCardVo);
        }
    }

    private void createRefundOrders(OrderDO order) {
        List<PaymentOrderDO> paymentOrderList = paymentOrderMapper.selectListById(order.getId());
        ArrayList<RefundOrderSaveReqVO> refundOrderSaveReqVO = new ArrayList<>();
        for (PaymentOrderDO paymentOrderDO : paymentOrderList) {
            RefundOrderSaveReqVO saveReqVO = new RefundOrderSaveReqVO();
            saveReqVO.setPaymentOrderId(paymentOrderDO.getId());
            saveReqVO.setAmount(order.getReceivedAmount());
            saveReqVO.setRefundReason("用户取消订单");
            refundOrderSaveReqVO.add(saveReqVO);
        }
        for (RefundOrderSaveReqVO reqVO : refundOrderSaveReqVO) {
            refundService.createRefundOrderInfo(reqVO);
        }
    }

    @Override
    @LogRecord(type = ExpressLogRecordConstants.ORDER_CALL_BACK,
            fail = "运力下单回调失败，失败原因：{{#_errorMsg}}",
            success = "运力下单回调，接收回调参数{{#orderCallback}}",
            bizNo = "{{#userId}}")
    @Transactional(rollbackFor = Exception.class)
    public void orderCallback(OrderCallBackRequestDTO orderCallBackRequestDTO) {
        log.info("【执行下单回调，接收回调参数-------------------】:{}", orderCallBackRequestDTO);
        LogRecordContext.putVariable("orderCallback", orderCallBackRequestDTO);

        if (orderCallBackRequestDTO == null || isEmpty(orderCallBackRequestDTO.toString())) {
            throw exception(ErrorCodeConstants.PARAMETER_IS_EMPTY);
        }
        OrderCallBackRequestBO orderCallBackRequestBO = OrderMapStructMapper.INSTANCE.orderCallBackRequestVOToBO(orderCallBackRequestDTO);
        OrderCallBackRequestBO.OrderCallbackData data = orderCallBackRequestBO.getData();

        if (data == null || StringUtils.isEmpty(data.getUcmOrderSn())) {
            throw exception(ErrorCodeConstants.PARAMETER_IS_EMPTY);
        }
        LogRecordContext.putVariable("orderCallbackData", data);
        OrderDO orderDO = orderMapper.selectOrderBycallBackParameter(data.getUcmOrderSn());
        if (ObjectUtil.isEmpty(orderDO)) {
            throw exception(ErrorCodeConstants.ORDER_NOT_EXISTS);
        }

        LogRecordContext.putVariable("userId", orderDO.getPersonalId());

        OrderCallbackStatusEnum status = OrderCallbackStatusEnum.fromCode(data.getStatus());
        switch (status) {
            case SUCCESS -> handleCallbackSuccess();
            case ORDER_ACCEPTED, ORDER_CREATED, COLLECTING, PICKED_UP ->
                    handleCallbackAccepted(data, orderDO);
            case WEIGHT_MODIFIED -> handleCallbackWeightModified(data, orderDO);
            case SETTLED -> handleCallbackSettled(data, orderDO);
            case SIGNED -> handleCallbackSigned(orderDO);
            case COLLECTION_FAILED, ORDER_CREATION_FAILED, USER_CANCELLED,
                 ORDER_CANCELLED, ORDER_PLACEMENT_FAILED ->
                    handleCallbackCancelled(orderDO);
            case IN_TRANSIT, DELIVERY_IN_PROGRESS ->
                    handleCallbackInTransit(data, orderDO);
            default -> {
                log.warn("未处理的状态码: {}，描述: {}", data.getStatus(), status.getDescription());
                throw exception(ORDER_STATUS_INVALID, data.getStatus().toString());
            }
        }
    }

    private void handleCallbackSuccess() {
        // 下单成功->对应小B的待接单
        // 调用运力下单时已经做了订单状态更新
        log.info("回调状态：下单成功，无需处理。");
    }

    private void handleCallbackAccepted(OrderCallBackRequestBO.OrderCallbackData data, OrderDO orderDO) {
        if (!(OrderStatusTypeEnum.WAIT.getStatus().equals(orderDO.getStatus()))) {
            throw exception(ORDER_STATUS_INVALID);
        }
        OrderDO orderUpdate = new OrderDO();
        orderUpdate.setId(orderDO.getId());
        orderUpdate.setUcmOrderSn(data.getUcmOrderSn());
        orderUpdate.setTrackingNumber(data.getExpressSn());
        orderUpdate.setReceiverStaffName(data.getCourierName());
        orderUpdate.setReceiverStaffMobile(data.getCourierMobile());
        orderUpdate.setReceiverStaffPickupCode(data.getPickupCode());
        orderUpdate.setStatus(OrderStatusTypeEnum.PICKUP_PENDING.getStatus());
        orderMapper.updateOrderBycallBackParameter(orderUpdate);
        recordStatusChange(orderDO.getId(), orderDO.getOrderNo(), OrderStatusTypeEnum.WAIT.getStatus(), OrderStatusTypeEnum.PICKUP_PENDING.getStatus());
    }

    private void handleCallbackWeightModified(OrderCallBackRequestBO.OrderCallbackData data, OrderDO orderDO) {
        // 此处省略了复杂的计费逻辑，仅做流程演示
        // 实际应调用计价服务重新计算费用
        double profit = Double.parseDouble(configApi.getConfigValueByKey("profit"));
        int freight = data.getFreight();
        int newAmount = countProfit(freight, profit);
        int oldAmount = orderDO.getReceivedAmount();

        if (oldAmount < newAmount) {
            int difference = newAmount - oldAmount;
            log.info("【订单需补付金额】: {}", difference);

            orderPromotionService.asyncNotifyUpdateOrderAmount(orderDO, myUpdatePriceExecutor);

            OrderDO orderUpdate = new OrderDO();
            orderUpdate.setId(orderDO.getId());
            orderUpdate.setStatus(OrderStatusTypeEnum.PENDING_ADDITIONAL_FEE.getStatus());
            orderUpdate.setItemWeight(data.getWeight());
            orderUpdate.setPrepaidAmount(difference);
            int row = orderMapper.updateOrderInfo(orderUpdate);
            if (row > 0) {
                log.info("【更新订单成功，受影响行数】:{}", row);
            }
            // 重新发起支付
        }
    }

    private void handleCallbackSettled(OrderCallBackRequestBO.OrderCallbackData data, OrderDO orderDO) {
        String oldStatus = orderDO.getStatus();
        updateOrderStatusByNos(orderDO.getOrderNo(), OrderStatusTypeEnum.PARTIAL_PAYMENT_PENDING.getStatus());
        orderPromotionService.asyncNotifyUpdateOrderStatus(orderDO, PromotionEnum.PARTIAL_PAYMENT_PENDING.getCode(), myUpdateOrderStatusExecutor);
        recordStatusChange(orderDO.getId(), orderDO.getOrderNo(), oldStatus, OrderStatusTypeEnum.PARTIAL_PAYMENT_PENDING.getStatus());

        // 处理续重分润
        handleProfitSharing(orderDO, data.getWeight());

        // 坏账退还
        handleBadDebtRecovery(orderDO);
    }

    private void handleCallbackSigned(OrderDO orderDO) {
        String oldStatus = orderDO.getStatus();
        updateOrderStatusByNos(orderDO.getOrderNo(), OrderStatusTypeEnum.COMPLETED.getStatus());
        orderPromotionService.asyncNotifyUpdateOrderStatus(orderDO, PromotionEnum.COMPLETED.getCode(), myUpdateOrderStatusExecutor);
        recordStatusChange(orderDO.getId(), orderDO.getOrderNo(), oldStatus, OrderStatusTypeEnum.COMPLETED.getStatus());
    }

    private void handleCallbackCancelled(OrderDO orderDO) {
        log.warn("订单准备取消，来自运力回调");
        CancelOrderRequest cancelOrderRequest = new CancelOrderRequest();
        cancelOrderRequest.setUcmOrderSn(orderDO.getUcmOrderSn());
        cancelOrderRequest.setCancelMsg("运力回调取消");
        orderLogisticsService.ucmCancelOrder(cancelOrderRequest);
    }

    private void handleCallbackInTransit(OrderCallBackRequestBO.OrderCallbackData data, OrderDO orderDO) {
        log.info("订单运输中，运单号：{}", data.getExpressSn());
        String oldStatus = orderDO.getStatus();
        updateOrderStatusByNos(orderDO.getOrderNo(), OrderStatusTypeEnum.IN_TRANSIT.getStatus());
        messageUtils.sendStatusUpdateMessage(orderDO);
        recordStatusChange(orderDO.getId(), orderDO.getOrderNo(), oldStatus, OrderStatusTypeEnum.IN_TRANSIT.getStatus());
    }


    private void handleProfitSharing(OrderDO orderDO, Integer newWeight) {
        AgentInfoDTO agentInfoDTO = agentApi.getAgentByMemberId(orderDO.getPersonalId());
        if (agentInfoDTO != null) {
            DetailShareReqDTO reqDTO = new DetailShareReqDTO();
            reqDTO.setOrderId(orderDO.getId());
            reqDTO.setOrderNo(orderDO.getOrderNo());
            reqDTO.setAgentId(agentInfoDTO.getId());
            reqDTO.setWeight(Long.valueOf(newWeight - orderDO.getItemWeight()));
            reqDTO.setTargetType(OrderConstants.targetType);
            reqDTO.setCalculateTime(LocalDateTime.now());
            reqDTO.setStatus(OrderConstants.detailShareStatus);

            PricingRuleApiPageReqVO pricingRuleApiPageReqVO = new PricingRuleApiPageReqVO();
            pricingRuleApiPageReqVO.setAgentId(agentInfoDTO.getId());
            pricingRuleApiPageReqVO.setFirstWeightMarkup(orderDO.getItemWeight());
            pricingRuleApiPageReqVO.setAdditionalWeightMarkup(newWeight - orderDO.getItemWeight());

            OrderProfitSharingProducer producer = SpringUtil.getBean(OrderProfitSharingProducer.class);
            producer.sendRoleRefreshMessage(reqDTO, pricingRuleApiPageReqVO);
        }
    }

    private void handleBadDebtRecovery(OrderDO orderDO) {
        AgentInfoDTO agentInfoDTO = agentApi.getAgentByMemberId(orderDO.getPersonalId());
        if (agentInfoDTO != null) {
            TransactionsSaveReqDO req = new TransactionsSaveReqDO();
            req.setWalletId(Long.valueOf(agentInfoDTO.getWalletId()));
            req.setAgentId(agentInfoDTO.getId());
            // 金额应为第二笔支付的金额，此处为示例
            req.setAmount(200);
            transactionsApi.badDebtRecovery(req);
        }
    }

    private void recordStatusChange(Long orderId, String orderNo, String startStatus, String endStatus) {
        OrderStatusProducer producer = SpringUtil.getBean(OrderStatusProducer.class);
        producer.sendRoleRefreshMessage(
                Math.toIntExact(orderId),
                orderNo,
                startStatus,
                endStatus,
                null
        );
    }

    public Integer countProfit(Integer amount, Double profit) {
        return (int) (amount * profit);
    }

    @Override
    public Integer updateOrderStatusByNos(String orderNo, String status) {
        return orderMapper.updateOrderByNos(orderNo, status);
    }

    @Override
    public void updateOrder(OrderSaveReqVO updateReqVO) {
        // 校验存在
        validateOrderExists(updateReqVO.getId());
        // 更新
        OrderDO updateObj = BeanUtils.toBean(updateReqVO, OrderDO.class);
        orderMapper.updateById(updateObj);
    }

    /**
     * 校验订单是否存在
     *
     * @param id
     */
    private void validateOrderExists(Long id) {
        if (orderMapper.selectById(id) == null) {
            throw exception(ORDER_NOT_EXISTS);
        }
    }

    @Override
    public void updateBadDebtCheckedFlag(Long orderId) {
        orderMapper.updateBadDebtCheckedFlag(orderId);
    }
} 