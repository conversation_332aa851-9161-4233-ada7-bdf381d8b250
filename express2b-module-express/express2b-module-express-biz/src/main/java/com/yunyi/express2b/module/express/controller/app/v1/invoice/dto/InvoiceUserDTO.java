package com.yunyi.express2b.module.express.controller.app.v1.invoice.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Schema(description = "用户开票信息登记新增/修改 Request VO")
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class InvoiceUserDTO {

    @Schema(description = "用户ID对应统一登录平台的ssoUserId", requiredMode = Schema.RequiredMode.REQUIRED, example = "9189")
    private Long memberId;

    @Schema(description = "发票抬头（单位或个人名称）", requiredMode = Schema.RequiredMode.REQUIRED)
    private String invoiceTitle;

    @Schema(description = "纳税人识别号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String taxNumber;

    @Schema(description = "单位地址")
    private String companyAddress;

    @Schema(description = "单位电话")
    private String companyPhone;

    @Schema(description = "开户行", example = "王五")
    private String bankName;

    @Schema(description = "银行基本户账号", example = "8473")
    private String bankAccount;

    @Schema(description = "接收邮箱", requiredMode = Schema.RequiredMode.REQUIRED)
    private String email;

    @Schema(description = "用户手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String memberPhone;

}