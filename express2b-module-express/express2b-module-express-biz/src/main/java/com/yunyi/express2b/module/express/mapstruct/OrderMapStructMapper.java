package com.yunyi.express2b.module.express.mapstruct;

import com.yunyi.express2b.module.express.api.order.dto.OrderDTO;
import com.yunyi.express2b.module.express.controller.admin.order.dto.OrderCallBackRequestDTO;
import com.yunyi.express2b.module.express.controller.admin.order.vo.CustomerServicesResponseVO;
import com.yunyi.express2b.module.express.controller.admin.order.vo.OrderCreateRequest;
import com.yunyi.express2b.module.express.controller.admin.order.vo.OrderRespVO;
import com.yunyi.express2b.module.express.controller.app.v1.order.vo.FeeDetailReqVO;
import com.yunyi.express2b.module.express.dal.dataobject.order.OrderDO;
import com.yunyi.express2b.module.express.service.order.bo.OrderCallBackRequestBO;
import com.yunyi.framework.api.login.api.cs.vo.CustomerServicesResponse;
import com.yunyi.framework.api.logistics.api.express.vo.QueryExpressPriceResponse;
import com.yunyi.framework.api.logistics.api.order.vo.CreateOrderRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/4/3 11:48
 */

@Mapper
public interface OrderMapStructMapper {

    OrderMapStructMapper INSTANCE = Mappers.getMapper(OrderMapStructMapper.class);

    List<OrderRespVO> doListToVoList(List<OrderDO> orderDOList);

    List<CustomerServicesResponseVO> doListToCustomerServicesResponseVOList(List<CustomerServicesResponse.CustomerServiceData> data);

    List<FeeDetailReqVO> ToFeeDetailReqVOList(List<QueryExpressPriceResponse> list);

    List<OrderCreateRequest> ToOrderVoList(List<OrderDO> orderDOList);

    List<OrderDTO> DoToDtoList(List<OrderDO> orderDOList);


    @Mappings({
            @Mapping(source = "itemWeight", target = "weight"),
            @Mapping(source = "itemCategory", target = "cargo"),
            @Mapping(source = "senderPhone", target = "senderMobile"),
            @Mapping(source = "receiverPhone", target = "receiverMobile"),
            @Mapping(source = "itemQuantity", target = "packageCount"),
            @Mapping(source = "orderNo", target = "orderSn"),
            @Mapping(source = "appointmentTime", target = "dayType"),
            @Mapping(source = "productCode", target = "productCode")
    })
    CreateOrderRequest orderSaveReqVOToRequest(OrderCreateRequest orderCreateRequest);

//    @Mappings({
//            @Mapping(target = "freight" ,expression="java(orderCallBackRequestDTO.getData.getFreight.multiply(java.math.BigDecimal.valueOf(100)).intValueExact())"),
//            @Mapping(target = "originalPrice" ,expression="java(orderCallBackRequestDTO.getData.getOriginalPrice.multiply(java.math.BigDecimal.valueOf(100)).intValueExact())"),
//            @Mapping(target = "feePrice" ,expression="java(orderCallBackRequestDTO.getData.getFeePrice.multiply(java.math.BigDecimal.valueOf(100)).intValueExact())"),
//            @Mapping(target = "defPrice" ,expression="java(orderCallBackRequestDTO.getData.getDefPrice.multiply(java.math.BigDecimal.valueOf(100)).intValueExact())")
//    })
//    OrderCallBackRequestBO orderCallBackRequestVOToBO(OrderCallBackRequestDTO orderCallBackRequestDTO);


    @Mappings({
            @Mapping(source = "data.freight", target = "data.freight", qualifiedByName = "convertYuanToFen"),
            @Mapping(source = "data.originalPrice", target = "data.originalPrice", qualifiedByName = "convertYuanToFen"),
            @Mapping(source = "data.feePrice", target = "data.feePrice", qualifiedByName = "convertYuanToFen"),
            @Mapping(source = "data.defPrice", target = "data.defPrice", qualifiedByName = "convertYuanToFen"),
            @Mapping(source = "data.feeDetails", target = "data.feeDetails", qualifiedByName = "convertFeeDetails")
    })
    OrderCallBackRequestBO orderCallBackRequestVOToBO(OrderCallBackRequestDTO orderCallBackRequestDTO);

    @Named("convertYuanToFen")
    default Integer convertYuanToFen(BigDecimal amount) {
        if (amount == null) return null;
        return amount.multiply(BigDecimal.valueOf(100))
                .setScale(0, RoundingMode.HALF_UP)
                .intValueExact();
    }

    @Named("convertFeeDetails")
    default List<OrderCallBackRequestBO.FeeDetail> convertFeeDetails(List<OrderCallBackRequestDTO.FeeDetail> sourceList) {
        if (sourceList == null) return Collections.emptyList();

        return sourceList.stream()
                .map(fd -> {
                    OrderCallBackRequestBO.FeeDetail feeDetail = new OrderCallBackRequestBO.FeeDetail();
                    feeDetail.setType(fd.getType());
                    feeDetail.setDesc(fd.getDesc());
                    feeDetail.setPayStatus(fd.getPayStatus());
                    feeDetail.setPrice(convertYuanToFen(fd.getPrice()));
                    return feeDetail;
                })
                .collect(Collectors.toList());
    }
}
