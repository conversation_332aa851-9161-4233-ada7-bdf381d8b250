package com.yunyi.express2b.module.express.controller.app.v1.invoice;

import com.yunyi.express2b.module.express.controller.app.v1.invoice.dto.invoiceorder.InvoiceOrderPageDTO;
import com.yunyi.express2b.module.express.controller.app.v1.invoice.vo.invoiceorder.InvoiceOrderVO;
import com.yunyi.express2b.module.express.controller.app.v1.invoice.dto.invoiceorder.InvoiceOrderDTO;
import com.yunyi.express2b.module.express.dal.dataobject.invoice.InvoiceOrderDO;
import com.yunyi.express2b.module.express.enums.ErrorCodeConstants;
import com.yunyi.express2b.module.express.service.invoice.invoiceorder.InvoiceOrderService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.yunyi.express2b.framework.common.pojo.PageParam;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import static com.yunyi.express2b.framework.common.pojo.CommonResult.success;

import com.yunyi.express2b.framework.excel.core.util.ExcelUtils;

import com.yunyi.express2b.framework.apilog.core.annotation.ApiAccessLog;
import org.springframework.web.multipart.MultipartFile;

import static com.yunyi.express2b.framework.apilog.core.enums.OperateTypeEnum.*;



@Tag(name = "用户 APP - 订单发票")
@RestController
@RequestMapping("/v1/express/invoice-order")
@Validated
public class AppInvoiceOrderController {

    @Resource
    private InvoiceOrderService invoiceOrderService;

    @PostMapping("/create")
    @Operation(summary = "创建订单发票")
    public CommonResult<String> createInvoiceOrder(@Valid @RequestBody InvoiceOrderDTO invoiceOrderDTO) {
        return invoiceOrderService.createInvoiceOrder(invoiceOrderDTO);
    }

    @GetMapping("/page")
    @Operation(summary = "展示用户开票申请记录")
    public CommonResult<PageResult<InvoiceOrderVO>> getInvoiceOrderPage(@Valid InvoiceOrderPageDTO invoiceOrderPageDTO) {
        PageResult<InvoiceOrderDO> pageResult = invoiceOrderService.getInvoiceOrderPage(invoiceOrderPageDTO);
        return success(BeanUtils.toBean(pageResult, InvoiceOrderVO.class));
    }

    @GetMapping("/get")
    @Operation(summary = "查看指定开票记录详情")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<InvoiceOrderVO> getInvoiceOrder(@RequestParam("id") Long id) {
        return success(invoiceOrderService.getInvoiceOrder(id));
    }

    @GetMapping("/download")
    @Operation(summary = "发票下载")
    public CommonResult<String> downloadInvoice(@RequestParam("id") Long id) {
        return invoiceOrderService.downloadInvoiceOrder(id);
    }

    @GetMapping("/export")
    @Operation(summary = "导出发票")
    @ApiAccessLog(operateType = EXPORT)
    public void exportInvoices(HttpServletResponse response, @RequestParam("ids") List<Long> ids) {
        invoiceOrderService.exportInvoices(response, ids);
    }

    @PostMapping("/import")
    @Operation(summary = "批量导入开票结果")
    public CommonResult<String> importInvoices(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return CommonResult.error(ErrorCodeConstants.FILE_IS_EMPTY);
        }
        if (!Objects.requireNonNull(file.getOriginalFilename()).endsWith(".zip")) {
            return CommonResult.error(ErrorCodeConstants.FILE_FORMAT_ERROR);
        }
        return invoiceOrderService.importInvoices(file);
    }

    @PutMapping("/update")
    @Operation(summary = "审核发票申请")
    public CommonResult<Boolean> updateInvoiceOrder(@RequestBody InvoiceOrderDTO invoiceOrderDTO) {
        invoiceOrderService.updateInvoiceOrder(invoiceOrderDTO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除订单发票")
    @Parameter(name = "id", description = "编号", required = true)
    public CommonResult<Boolean> deleteInvoiceOrder(@RequestParam("id") Long id) {
        invoiceOrderService.deleteInvoiceOrder(id);
        return success(true);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出订单发票 Excel")
    @ApiAccessLog(operateType = EXPORT)
    public void exportInvoiceOrderExcel(@Valid InvoiceOrderPageDTO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<InvoiceOrderDO> list = invoiceOrderService.getInvoiceOrderPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "订单发票.xls", "数据", InvoiceOrderVO.class,
                        BeanUtils.toBean(list, InvoiceOrderVO.class));
    }

}