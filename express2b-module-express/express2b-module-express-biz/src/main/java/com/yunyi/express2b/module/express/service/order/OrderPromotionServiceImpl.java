package com.yunyi.express2b.module.express.service.order;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.security.core.util.SecurityFrameworkUtils;
import com.yunyi.express2b.module.crm.api.personInfo.PersonInfoApi;
import com.yunyi.express2b.module.crm.api.personInfo.dto.PersonalInfoSaveReqDTO;
import com.yunyi.express2b.module.express.controller.admin.order.vo.OfficialAccountResponseVO;
import com.yunyi.express2b.module.express.controller.admin.order.vo.ReceiveMessageRequest;
import com.yunyi.express2b.module.express.controller.app.v1.order.vo.PosterTemplateVO;
import com.yunyi.express2b.module.express.controller.app.v1.order.vo.UpdateUserRequest;
import com.yunyi.express2b.module.express.dal.dataobject.order.OrderDO;
import com.yunyi.express2b.module.express.dal.mysql.order.OrderMapper;
import com.yunyi.express2b.module.express.enums.ErrorCodeConstants;
import com.yunyi.express2b.module.express.enums.OrderStatusTypeEnum;
import com.yunyi.express2b.module.express.enums.social.ExpressLogRecordConstants;
import com.yunyi.express2b.module.express.utils.GenerateOrderNumberUtil;
import com.yunyi.express2b.module.express.utils.OrderConstants;
import com.yunyi.express2b.module.infra.api.config.ConfigApi;
import com.yunyi.framework.api.login.api.cs.vo.CustomerServicesRequest;
import com.yunyi.framework.api.login.api.cs.vo.CustomerServicesResponse;
import com.yunyi.framework.api.login.api.discount.DiscountApi;
import com.yunyi.framework.api.login.api.discount.vo.*;
import com.yunyi.framework.api.login.api.message.MessageApi;
import com.yunyi.framework.api.login.api.message.vo.TemplatePageRequest;
import com.yunyi.framework.api.login.api.message.vo.TemplatePageResponse;
import com.yunyi.framework.api.login.api.qrcode.orcodeApi;
import com.yunyi.framework.api.login.api.qrcode.vo.CodeRequest;
import com.yunyi.framework.api.login.api.qrcode.vo.PushRequest;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 订单推广服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/4/16 16:58
 */
@Service
@Slf4j
public class OrderPromotionServiceImpl implements OrderPromotionService {

    @Resource
    private OrderMapper orderMapper;
    @Resource
    private ConfigApi configApi;
    @Resource
    private DiscountApi discountApi;
    @Resource
    private orcodeApi orcodeApi;
    @Resource
    private com.yunyi.framework.api.login.api.cs.CustomerServicesApi customerServicesApi;
    @Resource
    private ObjectMapper objectMapper;
    @Resource
    private PersonInfoApi personInfoApi;
    @Resource
    private MessageApi messageApi;

    @Resource
    @Qualifier("myCancelOrderExecutor")
    private Executor myCancelOrderExecutor;

    @Value("${yunyi.ssoUserId}")
    private Integer ssoUserId; //推广平台默认用户id

    @Value("${yunyi.clientId}")
    private Integer clientId;

    @Value("${yunyi.path}")
    private String path;


    @Override
    public Boolean createOrderToPromo() {
        List<OrderDO> orderDOList = orderMapper.selectPaidOrderList(OrderStatusTypeEnum.PAID_STATUSES);
        log.info("查询已付款的订单数量：{}", orderDOList.size());
        for (OrderDO orderDO : orderDOList) {
            //生成的唯一标识
            String taskId = GenerateOrderNumberUtil.generateTaskId("TS",
                    StringUtils.leftPad(String.valueOf(orderDO.getPersonalId()), 2, '0'),
                    StringUtils.leftPad(String.valueOf(orderDO.getBrandId()), 2, '0'));
            //查当前订单的分润金额
            Integer shareAmount = orderDO.getShareAmount();
            Integer sharePriceInCents = 0;
            if (orderDO.getShareAmount() == null || orderDO.getShareAmount() == 0) {
                //TODO
                log.info("订单分润金额不存在，查询默认分润金额");
                sharePriceInCents = new BigDecimal(configApi.getConfigValueByKey("shareprice"))
                        .multiply(BigDecimal.valueOf(100))
                        .intValueExact();


            }
            Integer profit = new BigDecimal(configApi.getConfigValueByKey("profit"))
                    .multiply(BigDecimal.valueOf(100))
                    .intValueExact();
            CreatOrderRequest creatOrderRequest = new CreatOrderRequest();
            creatOrderRequest.setSsoUserId(Math.toIntExact(orderDO.getPersonalId()));
            creatOrderRequest.setTaskId(taskId);
            creatOrderRequest.setOrderSn(orderDO.getOrderNo());
            creatOrderRequest.setTotalAmount(orderDO.getCostAmount());
            creatOrderRequest.setPayAmount(orderDO.getReceivedAmount());
            creatOrderRequest.setMarkup(sharePriceInCents);
            creatOrderRequest.setProfit(profit);
            creatOrderRequest.setStatus(1);

            try {
                CommonResult<JSONObject> creatorder = discountApi.creatorder(creatOrderRequest);
                if (creatorder != null && creatorder.getData().getString("code").equals(OrderConstants.code)) {
                    return Boolean.TRUE;
                } else if (creatorder != null && creatorder.getData().getString("code").equals(OrderConstants.noUserId)) {
                    log.info("用户不存在，使用默认用户:{}", ssoUserId);
                    creatOrderRequest.setSsoUserId(ssoUserId);
                    CommonResult<JSONObject> creatorder1 = discountApi.creatorder(creatOrderRequest);
                    if (creatorder != null && creatorder.getData().getString("code").equals(OrderConstants.code)) {
                        return Boolean.TRUE;
                    }
                }
            } catch (Exception e) {
                log.error("调用接口失败，订单号：{}", orderDO.getOrderNo(), e);
            }
        }
        return Boolean.FALSE;
    }

    @Override
    @LogRecord(type = ExpressLogRecordConstants.ONLINE_CUSTOMER,
            fail = "获取在线客服失败，失败原因：「{{#_errorMsg}}」",
            success = "在线客服信息:{{#customer}}",
            bizNo = "{{#userId}}")
    public CustomerServicesResponse getOnlineCustomerData(CustomerServicesRequest request) {
        log.info("【在线客服】获取在线客服数据，请求参数：{}", request);
        LogRecordContext.putVariable("userId", SecurityFrameworkUtils.getLoginUserId());
        try {
            // 调用服务层接口获取客服信息
            CommonResult<CustomerServicesResponse> result = customerServicesApi.customerServices(request);
            log.info("【在线客服】调用服务层接口获取客服信息，响应数据：{}", result);

            if (result == null) {
                throw exception(ErrorCodeConstants.QUERY_DATA_IS_EMPTY);
            }

            if (result.getCode().equals(OrderConstants.successCode)) {
                log.info("【在线客服】成功获取客服信息，原始响应数据：{}", result.getData());

                // 使用 Jackson 反序列化为 CustomerServicesResponse
                CustomerServicesResponse response = new CustomerServicesResponse();

                @SuppressWarnings("unchecked")
                List<Map<String, Object>> dataList = (List<Map<String, Object>>) result.getData();

                List<CustomerServicesResponse.CustomerServiceData> serviceDataList = dataList.stream()
                        .map(map -> objectMapper.convertValue(map, CustomerServicesResponse.CustomerServiceData.class))
                        .toList();

                response.setData(serviceDataList);
                log.info("【在线客服】解析后的客服数据：{}", response);
                LogRecordContext.putVariable("customer", response);
                return response;
            } else {
                log.warn("【在线客服】服务端返回失败，code: {}, msg: {}", result.getCode(), result.getMsg());
                throw exception(ErrorCodeConstants.ONLINE_CUSTOMER_SERVICE_UNAVAILABLE);
            }

        } catch (Exception e) {
            log.error("【在线客服】调用异常，请求参数：{}", request, e);
            throw exception(ErrorCodeConstants.CALLING_INTERFACE_ERROR);
        }
    }

    @Override
    @LogRecord(type = ExpressLogRecordConstants.DOWNLOAD_POSTER,
            fail = "下载海报失败，失败原因：「{{#_errorMsg}}」",
            success = "海报的url:{{#urls}}",
            bizNo = "{{#userId}}")
    public List<PosterTemplateVO> downloadPoster() {
        log.info("开始执行下载海报的方法");
        if (SecurityFrameworkUtils.getLoginUserId() == null) {
            throw exception(ErrorCodeConstants.USER_NOT_EXISTS);
        }
        LogRecordContext.putVariable("userId", SecurityFrameworkUtils.getLoginUserId());
        CodeRequest codeRequest = new CodeRequest();
        codeRequest.setParam(SecurityFrameworkUtils.getLoginUserId().toString());
        codeRequest.setClientId(clientId);
        codeRequest.setPath(path);

        try {
            CommonResult<JSONObject> result = orcodeApi.orcodePosters(codeRequest);
            log.info("【调用下载海报的接口返回数据】：{}", result);
            if (result != null && OrderConstants.successCode.equals(result.getCode())) {
                JSONObject dataJson = result.getData();
                List<PosterTemplateVO> posterList = new ArrayList<>();
                List<String> urlList = new ArrayList<>();
                if (dataJson != null && dataJson.containsKey("data")) {
                    JSONArray dataArray = dataJson.getJSONArray("data");
                    if (dataArray != null) {


                        for (int i = 0; i < dataArray.size(); i++) {
                            JSONObject item = dataArray.getJSONObject(i);
                            PosterTemplateVO vo = new PosterTemplateVO();
                            vo.setTemplate(item.getString("template"));
                            vo.setUrl(item.getString("url"));
                            posterList.add(vo);
                            urlList.add(item.getString("url"));
                        }
                    }
                    LogRecordContext.putVariable("urls", urlList);
                }
                return posterList;
            } else {
                log.warn("下载海报接口返回非成功状态码: {}", result != null ? result.getCode() : "null");
                return Collections.emptyList();
            }

        } catch (Exception e) {
            log.error("调用下载海报接口异常", e);
            throw exception(ErrorCodeConstants.CALLING_INTERFACE_ERROR, e.getMessage());
        }
    }

    @Override
    public JSONObject generateApplets() {
        log.info("开始执行生成小程序的方法 ");
        CodeRequest codeRequest = new CodeRequest();
        codeRequest.setParam(SecurityFrameworkUtils.getLoginUserId().toString());
        codeRequest.setClientId(clientId);
        codeRequest.setPath(path);
        CommonResult<JSONObject> result = null;
        try {
            result = orcodeApi.orcodeLink(codeRequest);
            log.info("【调用生成小程序的接口返回数据】：{}", result);

        } catch (Exception e) {
            throw exception(ErrorCodeConstants.CALLING_INTERFACE_ERROR);
        }
        return result.getData();
    }

    @Override
    @LogRecord(type = ExpressLogRecordConstants.QR_CODE,
            fail = "创建二维码失败，失败原因：「{{#_errorMsg}}」",
            success = "在线客服信息:{{#customer}}",
            bizNo = "{{#userId}}")
    public JSONObject createQrCode() {
        log.info("开始执行创建二维码的方法");
        if (SecurityFrameworkUtils.getLoginUserId() == null) {
            throw exception(ErrorCodeConstants.USER_NOT_EXISTS);
        }
        CodeRequest codeRequest = new CodeRequest();
        codeRequest.setParam("u=" + SecurityFrameworkUtils.getLoginUserId());
        codeRequest.setClientId(clientId);
        codeRequest.setPath(path);
        CommonResult<JSONObject> result = null;
        try {
            result = orcodeApi.orcode(codeRequest);
            log.info("【调用生成小程序的接口返回数据】：{}", result);
        } catch (Exception e) {
            throw exception(ErrorCodeConstants.CALLING_INTERFACE_ERROR);
        }
        return result.getData();
    }

    @Override
    public JSONObject updatePersonal(UpdateUserRequest updateUserRequest) {
        log.info("开始执行更新个人数据的方法,接收参数：{}", updateUserRequest);
        ModifyUserRequest modifyUserRequest = new ModifyUserRequest();
        modifyUserRequest.setUserName(updateUserRequest.getUserName());
        modifyUserRequest.setMobile(updateUserRequest.getMobile());
        modifyUserRequest.setIcon(updateUserRequest.getIcon());
        modifyUserRequest.setSsoUserId(updateUserRequest.getSsoUserId());
        modifyUserRequest.setVersion(updateUserRequest.getVersion());
        CommonResult<JSONObject> result = null;
        try {
            result = discountApi.modifyUser(modifyUserRequest);
        } catch (Exception e) {
            log.error("调用 modifyUser 接口异常", e);
            throw exception(ErrorCodeConstants.CALLING_INTERFACE_ERROR, e.getMessage());
        }
        return result.getData();
    }

    @Override
    @LogRecord(type = ExpressLogRecordConstants.OFFICIAL_ACCOUNT_LINK,
            fail = "获取微信关注公众号链接失败，失败原因：「{{#_errorMsg}}」",
            success = "获取链接:{{#url}}",
            bizNo = "{{#userId}}")
    public OfficialAccountResponseVO officialAccountLink() {
        log.info("开始执行获取公众号链接的方法");
        LogRecordContext.putVariable("userId", SecurityFrameworkUtils.getLoginUserId());
        PushRequest pushRequest = new PushRequest();
        pushRequest.setClientId(clientId);
        OfficialAccountResponseVO vo = new OfficialAccountResponseVO();
        try {
            CommonResult<JSONObject> result = orcodeApi.pushSubscribe(pushRequest);
            if (result != null && OrderConstants.successCode.equals(result.getCode())) {
                log.info("【调用公众号链接的接口返回数据】：{}", result);
                JSONObject data = result.getData();
                String url = data.getJSONObject("data").getString("url");
                log.info("【url】：{}", url);
                LogRecordContext.putVariable("url", url);
                return vo.setUrl(url);
            }
        } catch (Exception e) {
            throw exception(ErrorCodeConstants.CALLING_INTERFACE_ERROR);
        }
        return vo;
    }

    @Override
    @LogRecord(type = ExpressLogRecordConstants.CONCERN,
            fail = "公众号关注或取关回调失败，失败原因：「{{#_errorMsg}}」",
            success = "更新用户信息表，受影响行数:{{#row}}",
            bizNo = "{{#userId}}")
    public Long officialAccountAttentionOrClearance(PushRequest pushRequest) {
        log.info("开始执行公众号关注或取关的回调方法,接收参数：{}", pushRequest);
        if (SecurityFrameworkUtils.getLoginUserId() == null) {
            throw exception(ErrorCodeConstants.USER_NOT_EXISTS);
        }
        LogRecordContext.putVariable("userId", pushRequest.getSsoUserId());
        if (pushRequest == null) {
            throw exception(ErrorCodeConstants.PARAMETER_IS_EMPTY);
        }
        //根据用户id修改关注或者取消公众号状态
        PersonalInfoSaveReqDTO dto = new PersonalInfoSaveReqDTO();
        dto.setMemberId(SecurityFrameworkUtils.getLoginUserId());
        int row = personInfoApi.updatePersonalInfo(dto);
        if (row > 0) {
            LogRecordContext.putVariable("row", row);
        }
        return Long.valueOf(row);
    }

    @Override
    @LogRecord(type = ExpressLogRecordConstants.OPEN_PLATFORM_AGENT,
            fail = "开放平台代理失败，失败原因：「{{#_errorMsg}}」",
            success = "响应信息:{{#result}}",
            bizNo = "{{#userId}}")
    public JSONObject openPlatformAgent() {
        log.info("【开始执行获取开放平台代理的方法】");
        if (SecurityFrameworkUtils.getLoginUserId() == null) {
            throw exception(ErrorCodeConstants.USER_NOT_EXISTS);
        }
        LogRecordContext.putVariable("userId", SecurityFrameworkUtils.getLoginUserId());
        OpenpRequest openpRequest = new OpenpRequest();
        openpRequest.setSsoUserId(String.valueOf(SecurityFrameworkUtils.getLoginUserId()));
        openpRequest.setRecommenderSsoId(String.valueOf(SecurityFrameworkUtils.getLoginUserId()));
        CommonResult<JSONObject> result = null;
        try {
            result = discountApi.openplatformproxy(openpRequest);
        } catch (Exception e) {
            throw exception(ErrorCodeConstants.CALLING_INTERFACE_ERROR);
        }
        LogRecordContext.putVariable("result", result.getData());
        return result.getData();
    }

    @Override
    public CompletableFuture<Boolean> asyncNotifyUpdateOrderStatus(OrderDO orderDO, Integer status, Executor executor) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 构建请求参数
                UpOrderStatusRequest requestVO = new UpOrderStatusRequest();
                requestVO.setSsoUserId(Math.toIntExact(orderDO.getPersonalId()));
                requestVO.setOrderSn(orderDO.getOrderNo());
                requestVO.setStatus(status);

                // 调用接口
                CommonResult<JSONObject> result = discountApi.upOrderStatus(requestVO);
                log.info("【调用修改订单状态接口响应】: {}", result);

                if (result == null || result.getData() == null) {
                    log.warn("接口返回为空或数据为空，订单号：{}", orderDO.getOrderNo());
                    return false;
                }

                boolean success = OrderConstants.code.equals(result.getData().getString("code"));

                if (success) {
                    log.info("异步通知更新订单状态成功，订单号：{}", orderDO.getOrderNo());
                } else {
                    log.warn("异步通知更新订单状态失败，订单号：{}", orderDO.getOrderNo());
                }

                return success;

            } catch (Exception e) {
                log.error("异步更新更新订单状态异常，订单号：{}", orderDO.getOrderNo(), e);
                return false;
            }
        }, executor);
    }

    @Override
    public CompletableFuture<Boolean> asyncNotifyUpdateOrderAmount(OrderDO orderDO, Executor executor) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 构建请求参数
                CreatOrderRequest requestVO = new CreatOrderRequest();
                requestVO.setSsoUserId(Math.toIntExact(orderDO.getPersonalId()));
                requestVO.setOrderSn(orderDO.getOrderNo());
                requestVO.setTotalAmount(orderDO.getCostAmount());
                requestVO.setPayAmount(orderDO.getReceivedAmount());
                requestVO.setMarkup(orderDO.getRetailAmount() - orderDO.getCostAmount());
                requestVO.setProfit(2000);
                requestVO.setStatus(1);

                // 调用接口
                CommonResult<JSONObject> result = discountApi.upOrderAmount(requestVO);
                log.info("【调用订单金额更新接口响应】: {}", result);

                if (result == null || result.getData() == null) {
                    log.warn("接口返回为空或数据为空，订单号：{}", orderDO.getOrderNo());
                    return false;
                }

                boolean success = OrderConstants.code.equals(result.getData().getString("code"));

                if (success) {
                    log.info("异步通知更新订单金额成功，订单号：{}", orderDO.getOrderNo());
                } else {
                    log.warn("异步通知更新订单金额失败，订单号：{}", orderDO.getOrderNo());
                }

                return success;

            } catch (Exception e) {
                log.error("异步更新订单金额异常，订单号：{}", orderDO.getOrderNo(), e);
                return false;
            }
        }, executor);
    }

    @Override
    public CompletableFuture<Boolean> asyncNotifyCancelOrder(OrderDO orderDO) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 构建请求参数
                CancelRequest requestVO = new CancelRequest();
                requestVO.setSsoUserId(Math.toIntExact(orderDO.getPersonalId()));
                requestVO.setOrderSn(orderDO.getOrderNo());
                requestVO.setStatus(1);

                // 调用接口
                CommonResult<JSONObject> result = discountApi.cancelOrder(requestVO);
                log.info("【调用推广平台取消订单接口响应】: {}", result);

                if (result == null || result.getData() == null) {
                    log.warn("接口返回为空或数据为空，订单号：{}", orderDO.getOrderNo());
                    return false;
                }

                boolean success = OrderConstants.code.equals(result.getData().getString("code"));

                if (success) {
                    log.info("异步通知取消订单成功，订单号：{}", orderDO.getOrderNo());
                } else {
                    log.warn("异步通知取消订单失败，订单号：{}", orderDO.getOrderNo());
                }

                return success;

            } catch (Exception e) {
                log.error("异步通知取消订单异常，订单号：{}", orderDO.getOrderNo(), e);
                return false;
            }
        }, myCancelOrderExecutor);
    }

    @Override
    public String receiveOrderMessage(ReceiveMessageRequest request) {
        log.info("【receiveOrderMessage】接收到消息:{}", request);
        return "SUCCESS";
    }

    @Override
    public TemplatePageResponse sendtemplete() {
        //组装请求参数
        TemplatePageRequest request = new TemplatePageRequest();
        CommonResult<TemplatePageResponse> templatePageResponseCommonResult = messageApi.templatesPage(request);
        if (templatePageResponseCommonResult.getCode() != 0 || !templatePageResponseCommonResult.isSuccess()) {
            log.error("查询订阅列表: {}", request);
            throw exception(ErrorCodeConstants.FAILED_QUERY_SUBSCRIPTION, "查询订阅列表失败");
        }
        log.info("查询订阅列表消息成功，结果: {}", templatePageResponseCommonResult);
        return templatePageResponseCommonResult.getData();
    }
} 