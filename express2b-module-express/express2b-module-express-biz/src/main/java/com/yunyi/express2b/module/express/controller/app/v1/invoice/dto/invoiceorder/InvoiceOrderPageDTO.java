package com.yunyi.express2b.module.express.controller.app.v1.invoice.dto.invoiceorder;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yunyi.express2b.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.yunyi.express2b.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "用户 APP - 订单发票分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InvoiceOrderPageDTO extends PageParam {

    @Schema(description = "记录ID", example = "2925")
    private Long id;

    @Schema(description = "订单编号")
    private String orderNo;

    @Schema(description = "发票code")
    private String invoiceCode;

    @Schema(description = "发票编号")
    private String invoiceNumber;

    @Schema(description = "发票url", example = "https://www.iocoder.cn")
    private String invoiceUrl;

    @Schema(description = "发票金额")
    private Double amount;

    @Schema(description = "接收邮箱")
    private String email;

    @Schema(description = "开票状态(APPROVAL-待审核, INVOICING-开票中, INVOICED-已开票, INVOICE_FAILED-开票失败, REJECTED-已驳回, CANCELLED-已作废)", example = "1")
    private String invoiceStatus;

    @Schema(description = "驳回/失败原因", example = "不香")
    private String rejectReason;

    @Schema(description = "审核人ID", example = "432")
    private String reviewerId;

    @Schema(description = "审核时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] reviewTime;

    @Schema(description = "开票时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] invoiceTime;

    @Schema(description = "客服工号")
    private String customerServiceNo;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "创建者")
    private String creator;

}