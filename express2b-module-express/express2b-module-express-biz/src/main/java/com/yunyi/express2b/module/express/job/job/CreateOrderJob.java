package com.yunyi.express2b.module.express.job.job;

import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.framework.quartz.core.handler.JobHandler;
import com.yunyi.express2b.framework.tenant.core.aop.TenantIgnore;
import com.yunyi.express2b.module.express.controller.admin.order.vo.OrderCancelVO;
import com.yunyi.express2b.module.express.controller.admin.order.vo.OrderCreateRequest;
import com.yunyi.express2b.module.express.dal.dataobject.order.OrderDO;
import com.yunyi.express2b.module.express.dal.dataobject.products.ProductsDO;
import com.yunyi.express2b.module.express.dal.mysql.order.OrderMapper;
import com.yunyi.express2b.module.express.dal.mysql.products.ProductsMapper;
import com.yunyi.express2b.module.express.enums.OrderStatusTypeEnum;
import com.yunyi.express2b.module.express.mapstruct.OrderMapStructMapper;
import com.yunyi.express2b.module.express.service.order.OrderLifecycleService;
import com.yunyi.express2b.module.express.service.order.OrderLogisticsService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/4/16 16:58
 */
@Slf4j
@Component
public class CreateOrderJob implements JobHandler {

    @Resource
    private OrderMapper orderMapper;

    @Resource
    private ProductsMapper productsMapper;

    @Resource
    private RedisTemplate<String, OrderCreateRequest> redisTemplate;
    @Resource
    private OrderLogisticsService orderLogisticsService;
    @Resource
    private OrderLifecycleService orderLifecycleService;

    // 定义 Redis Key 前缀
    private static final String FAILED_ORDER_KEY = "failed";
    @Override
    @TenantIgnore
    public String execute(String param) throws Exception {
        log.info("【开始执行运力下单的定时任务】----------------");

        //查询已支付的订单集合
        List<OrderDO> paidOrders = orderMapper.selectPaidOrders(OrderStatusTypeEnum.PAID.getStatus());
        log.info("【查询到 {} 笔已支付订单】：{}，订单编号：{}", paidOrders.size(), paidOrders.stream()
                .map(OrderDO::getOrderNo)
                .collect(Collectors.toList()));
        for (OrderDO orderDO : paidOrders) {
            //查询订单下的商品集合
            List<ProductsDO> productsDOList = productsMapper.selectProductList(orderDO.getId());
            for (ProductsDO productsDO : productsDOList) {
                BeanUtils.copyProperties(productsDO, orderDO);
            }
        }
        List<OrderCreateRequest> orderSaveReqVOList = OrderMapStructMapper.INSTANCE.ToOrderVoList(paidOrders);

        List<OrderCreateRequest> failedRequests = new ArrayList<>();
        for (OrderCreateRequest request : orderSaveReqVOList) {
            try {
                orderLogisticsService.createTracking(request);
                log.info("【订单 {} 下单成功】{}", request.getOrderNo());
            } catch (Exception e) {
                log.error("【订单 {} 下单失败】{}", request.getOrderNo(), e.getMessage());
                // 使用 Redis 缓存失败订单，并进行重试机制
                saveFailedOrderToRedis(request);
            }
        }
        // 2. 重试 Redis 中的失败订单
        retryFailedOrders();

        return String.format("任务完成，成功处理 %d 笔，失败 %d 笔",
                orderSaveReqVOList.size() - failedRequests.size(), failedRequests.size());
    }
    /**
     * 将失败订单写入 Redis 缓存
     */
    private void saveFailedOrderToRedis(OrderCreateRequest request) {
        String key = FAILED_ORDER_KEY+request.getOrderNo();
        try {
            redisTemplate.opsForList().leftPush(key, request);
            redisTemplate.expire(key, 5, TimeUnit.MINUTES);
        } catch (Exception e) {
            log.error("【缓存失败订单 {} 到 Redis 异常】{}", request.getOrderNo(), e.getMessage());
        }
        log.info("【已缓存失败订单】订单号：{}", request.getOrderNo());
    }

    /**
     * 从 Redis 中获取失败订单并进行重试（最多3次）
     */
    public void retryFailedOrders() {
        log.info("【开始重试缓存中的失败订单】----------------");

        for (OrderCreateRequest request : getFailedOrdersFromRedis()) {
            int retryCount = 0;
            boolean success = false;

            while (retryCount < 3 && !success) {
                try {
                    log.info("【第 {} 次重试订单：{}】", retryCount + 1, request.getOrderNo());
                    orderLogisticsService.createTracking(request);
                    // 成功则跳出循环
                    success = true;
                    log.info("【订单 {} 重试成功】", request.getOrderNo());
                    // 清除缓存
                    removeFailedOrderFromRedis(request.getOrderNo());
                } catch (Exception e) {
                    retryCount++;
                    log.warn("【订单 {} 第 {} 次重试失败】", request.getOrderNo(), retryCount);
                    if (retryCount >= 3) {
                        log.error("【订单 {} 重试3次均失败，准备取消订单】原因：{}", request.getOrderNo(), e.getMessage());
                        //直接进行退款
                        OrderCancelVO orderCancelVO=new OrderCancelVO();
                        orderCancelVO.setOrderNo(request.getOrderNo());
                        orderCancelVO.setRefundReason("下单信息有误，直接取消订单");
                        orderLifecycleService.cancelOrder(orderCancelVO);
                        // 清除当前订单缓存
                        removeFailedOrderFromRedis(request.getOrderNo());
                    } else {
                        log.warn("【订单 {} 第 {} 次重试失败，将在下一轮继续尝试】", request.getOrderNo(), retryCount);
                        try {
                            Thread.sleep(5000); // 间隔5秒再重试
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                        }
                    }
                }
            }
        }
    }

    /**
     * 从 Redis 获取所有失败订单
     */
    private List<OrderCreateRequest> getFailedOrdersFromRedis() {
        List<OrderCreateRequest> failedRequests = new ArrayList<>();
        Set<String> keys = redisTemplate.keys(FAILED_ORDER_KEY + "*");
        if (keys != null && !keys.isEmpty()) {
            for (String key : keys) {
                List<OrderCreateRequest> requests = redisTemplate.opsForList().range(key, 0, -1);
                if (requests != null && !requests.isEmpty()) {
                    failedRequests.addAll(requests);
                }
            }
        }
        return failedRequests;
    }

    /**
     * 从 Redis 删除已处理的失败订单
     */
    private void removeFailedOrderFromRedis(String orderNo) {
        String key = FAILED_ORDER_KEY + orderNo;
        redisTemplate.delete(key);
    }
}
