package com.yunyi.express2b.module.express.service.invoice.invoiceorder;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yunyi.express2b.framework.common.exception.ServiceException;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.excel.core.util.ExcelUtils;
import com.yunyi.express2b.framework.security.core.util.SecurityFrameworkUtils;
import com.yunyi.express2b.module.express.controller.app.v1.invoice.dto.invoiceorderrelation.InvoiceOrderRelationDTO;
import com.yunyi.express2b.module.express.controller.app.v1.invoice.dto.invoiceorder.InvoiceOrderPageDTO;
import com.yunyi.express2b.module.express.controller.app.v1.invoice.dto.invoiceorder.InvoiceOrderDTO;
import com.yunyi.express2b.module.express.controller.app.v1.invoice.dto.invoiceorderrelation.InvoiceOrderRelationPageDTO;
import com.yunyi.express2b.module.express.controller.app.v1.invoice.vo.invoiceorder.InvoiceOrderVO;
import com.yunyi.express2b.module.express.controller.app.v1.invoice.vo.invoiceorderrelation.InvoiceOrderRelationVO;
import com.yunyi.express2b.module.express.dal.dataobject.invoice.InvoiceOrderDO;
import com.yunyi.express2b.module.express.dal.dataobject.invoice.InvoiceOrderRelationDO;
import com.yunyi.express2b.module.express.dal.mysql.invoice.InvoiceOrderMapper;
import com.yunyi.express2b.module.express.dal.mysql.invoice.InvoiceOrderRelationMapper;
import com.yunyi.express2b.module.express.enums.ErrorCodeConstants;
import com.yunyi.express2b.module.express.utils.ZipUtils;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import org.springframework.web.multipart.MultipartFile;


import java.io.ByteArrayInputStream;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.*;

import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;


/**
 * 订单发票 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class InvoiceOrderServiceImpl implements InvoiceOrderService {

    @Resource
    private InvoiceOrderMapper invoiceOrderMapper;
    @Resource
    private InvoiceOrderRelationMapper invoiceOrderRelationMapper;

    /**
     * 创建订单发票
     *
     * @param invoiceOrderDTO 创建订单发票
     * @return 订单发票编号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<String> createInvoiceOrder(InvoiceOrderDTO invoiceOrderDTO) {
        try {
            // 获取当前用户的ssoUserId
            Long loginUserId = SecurityFrameworkUtils.getLoginUserId();
            // 执行发票表的插入操作
            if (loginUserId == null) {
                return CommonResult.error(ErrorCodeConstants.CREATE_INVOICE_ORDER_FAIL);
            }
            invoiceOrderDTO.setCreator(loginUserId.toString());
            invoiceOrderDTO.setInvoiceStatus("APPROVAL");
            InvoiceOrderDO invoiceOrder = BeanUtils.toBean(invoiceOrderDTO, InvoiceOrderDO.class);
            int i = invoiceOrderMapper.insert(invoiceOrder);
            if (i != 1) {
                return CommonResult.error(ErrorCodeConstants.CREATE_INVOICE_ORDER_FAIL);
            }
            // 执行订单发票关联表的批量插入操作
            List<InvoiceOrderRelationDTO> invoiceOrderRelations = invoiceOrderDTO.getInvoiceOrderRelations();
            for (InvoiceOrderRelationDTO invoiceOrderRelation : invoiceOrderRelations) {
                invoiceOrderRelation.setInvoiceId(invoiceOrder.getId());
                invoiceOrderRelation.setCreator(loginUserId.toString());
            }
            Boolean b = invoiceOrderRelationMapper.insertBatch(BeanUtils.toBean(invoiceOrderRelations, InvoiceOrderRelationDO.class));
            if (!b) {
                return CommonResult.error(ErrorCodeConstants.CREATE_INVOICE_ORDER_FAIL);
            }
        }
        catch (Exception e) {
            log.error("[createInvoiceOrder][创建订单发票失败，参数({})]", invoiceOrderDTO, e);
            throw exception(ErrorCodeConstants.CREATE_INVOICE_ORDER_FAIL);
        }
        return CommonResult.success("成功");
    }

    /**
     * 审核发票申请
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateInvoiceOrder(InvoiceOrderDTO invoiceOrderDTO) {
        try {
            // 获取ids
            List<Long> ids = invoiceOrderDTO.getIds();
            if (CollectionUtils.isEmpty(ids)) {
                return;
            }
            // 修改ids集合对应的每条发票申请记录的invoiceStatus
            int updateRows = invoiceOrderMapper.update(null, new LambdaUpdateWrapper<InvoiceOrderDO>()
                    .set(InvoiceOrderDO::getInvoiceStatus, invoiceOrderDTO.getInvoiceStatus())
                    .in(InvoiceOrderDO::getId, ids));
            if (updateRows != ids.size()) {
                throw exception(ErrorCodeConstants.UPDATE_INVOICE_ORDER_FAIL);
            }
        }
        catch (Exception e) {
            log.error("[updateInvoiceOrder][审核发票申请失败，参数({})]", invoiceOrderDTO, e);
            throw exception(ErrorCodeConstants.UPDATE_INVOICE_ORDER_FAIL);
        }
    }

    /**
     * 查看指定开票记录详情
     */
    @Override
    public InvoiceOrderVO getInvoiceOrder(Long id) {
        InvoiceOrderVO invoiceOrderVO;
        try {
            // 通过主键查询开票记录信息
            InvoiceOrderDO invoiceOrderDO = invoiceOrderMapper.selectById(id);
            invoiceOrderVO = BeanUtils.toBean(invoiceOrderDO, InvoiceOrderVO.class);
            InvoiceOrderRelationPageDTO invoiceOrderRelationPageDTO = new InvoiceOrderRelationPageDTO();
            invoiceOrderRelationPageDTO.setInvoiceId(id);
            PageResult<InvoiceOrderRelationDO> invoiceOrderRelationDOPageResult = invoiceOrderRelationMapper.selectPage(invoiceOrderRelationPageDTO);
            invoiceOrderVO.setInvoiceOrderRelations(BeanUtils.toBean(invoiceOrderRelationDOPageResult.getList(), InvoiceOrderRelationVO.class));
        }
        catch (Exception e) {
            log.error("[getInvoiceOrder][获取订单发票失败，参数({})]", id, e);
            throw exception(ErrorCodeConstants.GET_INVOICE_ORDER_FAIL);
        }

        return invoiceOrderVO;
    }

    /**
     * 获得订单发票分页
     */
    @Override
    public PageResult<InvoiceOrderDO> getInvoiceOrderPage(InvoiceOrderPageDTO invoiceOrderPageDTO) {
        invoiceOrderPageDTO.setCreator(Objects.requireNonNull(SecurityFrameworkUtils.getLoginUserId()).toString());
        return invoiceOrderMapper.selectPage(invoiceOrderPageDTO);
    }

    /**
     * 获取发票下载链接
     */
    @Override
    public CommonResult<String> downloadInvoiceOrder(Long id) {
        String invoiceUrl;
        try {
            // 获取指定开票记录的发票下载链接并校验
            invoiceUrl = invoiceOrderMapper.selectById(id).getInvoiceUrl();
            if (invoiceUrl == null || invoiceUrl.isEmpty()) {
                return CommonResult.error(ErrorCodeConstants.GET_INVOICE_DOWNLOAD_URL_FAIL);
            }
        }
        catch (Exception e) {
            log.error("[downloadInvoiceOrder][下载订单发票失败，参数({})]", id, e);
            throw exception(ErrorCodeConstants.GET_INVOICE_DOWNLOAD_URL_FAIL);
        }
        return CommonResult.success(invoiceUrl);
    }

    /**
     * 导出发票
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void exportInvoices(HttpServletResponse response, List<Long> ids) {
        try {
            // 构建查询条件
            LambdaQueryWrapper<InvoiceOrderDO> queryWrapper = Wrappers.lambdaQuery(InvoiceOrderDO.class)
                    .in(InvoiceOrderDO::getId, ids)
                    .eq(InvoiceOrderDO::getInvoiceStatus, "INVOICING");
            // 查询数据
            List<InvoiceOrderDO> invoiceList = invoiceOrderMapper.selectList(queryWrapper);
            if (CollectionUtils.isEmpty(invoiceList)) {
                throw exception(ErrorCodeConstants.EXPORT_INVOICE_FAIL);
            }
            // 4. 转换为VO对象
            List<InvoiceOrderVO> voList = BeanUtils.toBean(invoiceList, InvoiceOrderVO.class);
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("已审核发票数据", StandardCharsets.UTF_8).replaceAll("\\+", "%20");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");

            // 6. 写入Excel
            ExcelUtils.write(response, "已审核发票数据.xlsx", "发票数据", InvoiceOrderVO.class, voList);
        }
        catch (Exception e) {
            log.error("[exportInvoices][导出订单发票失败，参数({})]", response, e);
            throw exception(ErrorCodeConstants.EXPORT_INVOICE_FAIL);
        }
    }

    /**
     * 导入发票
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<String> importInvoices(MultipartFile file) {
        try {
            // 解压ZIP文件
            Map<String, byte[]> extractedFiles = ZipUtils.extractZipFile(file.getInputStream(), Charset.forName("GBK"));
            // 分离Excel和PDF文件
            byte[] excelData = null;
            Map<String, byte[]> pdfFiles = new HashMap<>();
            for (Map.Entry<String, byte[]> entry : extractedFiles.entrySet()) {
                String fileName = entry.getKey();
                if (fileName.endsWith(".xlsx") || fileName.endsWith(".xls")) {
                    excelData = entry.getValue();
                } else if (fileName.endsWith(".pdf")) {
                    // 提取纯文件名（不带路径和扩展名）
                    String pureName = fileName.substring(fileName.lastIndexOf("/") + 1).replace(".pdf", "");
                    pdfFiles.put(pureName, entry.getValue());
                }
            }
            if (excelData == null) {
                return CommonResult.error(ErrorCodeConstants.INVOICE_IMPORT_ERROR, "未找到Excel文件");
            }
            // 解析Excel文件
            List<InvoiceOrderVO> records = parseExcelData(excelData);
            if (records.isEmpty()) {
                return CommonResult.error(ErrorCodeConstants.INVOICE_IMPORT_ERROR, "Excel文件内容为空");
            }
            // 创建批量更新容器
            List<InvoiceOrderDO> batchUpdateList  = new ArrayList<>();
            int successCount = 0;
            StringBuilder errorMsg = new StringBuilder();
            for (InvoiceOrderVO vo : records) {
                try {
                    // 准备待更新对象
                    InvoiceOrderDO updateDO = prepareUpdateDO(vo, pdfFiles);
                    batchUpdateList.add(updateDO);
                    successCount++;
                }
                catch (Exception e) {
                    errorMsg.append("申请ID ").append(vo.getId())
                            .append(" 处理失败: ").append(e.getMessage()).append("; ");
                }
            }
            // 执行批量更新
            if (!batchUpdateList.isEmpty()) {
                Boolean b = invoiceOrderMapper.updateBatch(batchUpdateList);
                if (!b) {
                    return CommonResult.error(ErrorCodeConstants.INVOICE_IMPORT_ERROR, "更新数据库失败");
                }
            }
            // 返回处理结果
            String resultMsg = String.format("导入完成，成功%d条，失败%d条", successCount, records.size() - successCount);
            if (!errorMsg.isEmpty()) {
                resultMsg += "。错误详情: " + errorMsg;
            }
            return CommonResult.success(resultMsg);
        }
        catch (Exception e) {
            log.error("[importInvoices][导入开票结果异常]", e);
            return CommonResult.error(ErrorCodeConstants.INVOICE_IMPORT_ERROR, e.getMessage());
        }
    }

    /**
     * 解析Excel数据并转换为发票订单列表
     */
    private List<InvoiceOrderVO> parseExcelData(byte[] excelData) throws  Exception{
        List<InvoiceOrderVO> records = new ArrayList<>();
        try(Workbook workbook = WorkbookFactory.create(new ByteArrayInputStream(excelData))) {
            Sheet sheet = workbook.getSheetAt(0);
            for (Row row : sheet) {
                if (row.getRowNum() == 0) continue; // 跳过标题行
                InvoiceOrderVO vo = new InvoiceOrderVO();
                vo.setId(Long.valueOf(getCellStringValue(row.getCell(0)))); // 申请ID
                vo.setInvoiceStatus(getCellStringValue(row.getCell(1))); // 开票状态
                vo.setRejectReason(getCellStringValue(row.getCell(2))); // 失败原因
                records.add(vo);
            }
        }
        return records;
    }

    /**
     * 验证导入的记录数据
     */
    private InvoiceOrderDO prepareUpdateDO(InvoiceOrderVO vo, Map<String, byte[]> pdfFiles) {
        // 验证记录数据
        validateImportRecord(vo);
        // 查询开票申请记录
        InvoiceOrderDO invoiceOrder = invoiceOrderMapper.selectById(vo.getId());
        if (invoiceOrder == null) {
            log.error("[processSingleRecord][申请ID {} 的开票申请记录不存在]", vo.getId());
            throw new ServiceException(ErrorCodeConstants.INVOICE_IMPORT_ERROR);
        }
        // 创建更新DO对象
        InvoiceOrderDO updateDO = new InvoiceOrderDO();
        updateDO.setId(invoiceOrder.getId());
        updateDO.setInvoiceStatus(vo.getInvoiceStatus());
        // 更新开票状态
        if ("INVOICED".equals(vo.getInvoiceStatus())) {
            // todo 检查PDF文件是否存在，pdfFiles的key是id字符串
            if (!pdfFiles.containsKey(String.valueOf(vo.getId()))) {
                log.error("[processSingleRecord][申请ID {} 的开票结果 PDF 文件不存在]", vo.getId());
                throw new ServiceException(ErrorCodeConstants.INVOICE_IMPORT_ERROR);
            }
            // todo 上传PDF到OSS
        }
        else {
            // 更新为失败状态
            updateDO.setRejectReason(vo.getRejectReason());
        }
        return updateDO;
    }

    /**
     * 验证导入的记录数据
     */
    private void validateImportRecord(InvoiceOrderVO vo) {
        if (vo.getId() == null) {
            throw new ServiceException(ErrorCodeConstants.INVOICE_IMPORT_ERROR);
        }

        if (!"INVOICED".equals(vo.getInvoiceStatus()) && !"INVOICE_FAILED".equals(vo.getInvoiceStatus())) {
            throw new ServiceException(ErrorCodeConstants.INVOICE_IMPORT_ERROR);
        }

        if ("INVOICE_FAILED".equals(vo.getInvoiceStatus()) && StringUtils.isEmpty(vo.getRejectReason())) {
            throw new ServiceException(ErrorCodeConstants.INVOICE_IMPORT_ERROR);
        }
    }

    /**
     * 获取单元格的字符串值
     */
    private String getCellStringValue(Cell cell) {
        if (cell == null) {
            return "";
        }
        return switch (cell.getCellType()) {
            case STRING -> cell.getStringCellValue().trim();
            case NUMERIC -> String.valueOf((long) cell.getNumericCellValue());
            default -> "";
        };
    }

    /**
     * todo 删除开票申请记录
     */
    @Override
    public void deleteInvoiceOrder(Long id) {
        // 校验存在
        validateInvoiceOrderExists(id);
        // 删除
        invoiceOrderMapper.deleteById(id);
    }

    private void validateInvoiceOrderExists(Long id) {
        if (invoiceOrderMapper.selectById(id) == null) {
            throw exception(ErrorCodeConstants.ORDER_NOT_EXISTS);
        }
    }
}