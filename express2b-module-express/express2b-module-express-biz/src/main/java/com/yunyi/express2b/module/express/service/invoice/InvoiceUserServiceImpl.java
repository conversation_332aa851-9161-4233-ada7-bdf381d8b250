package com.yunyi.express2b.module.express.service.invoice;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yunyi.express2b.framework.common.exception.ServiceException;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.security.core.util.SecurityFrameworkUtils;
import com.yunyi.express2b.module.crm.dal.dataobject.personalinfo.PersonalInfoDO;
import com.yunyi.express2b.module.crm.dal.mysql.personalinfo.PersonalInfoMapper;
import com.yunyi.express2b.module.express.controller.app.v1.invoice.vo.invoiceuser.InvoiceUserPageReqVO;
import com.yunyi.express2b.module.express.controller.app.v1.invoice.dto.InvoiceUserDTO;
import com.yunyi.express2b.module.express.controller.app.v1.invoice.vo.invoiceuser.InvoiceUserSaveRespVO;
import com.yunyi.express2b.module.express.dal.dataobject.invoice.InvoiceUserDO;
import com.yunyi.express2b.module.express.dal.mysql.invoice.InvoiceUserMapper;
import com.yunyi.express2b.module.express.enums.ErrorCodeConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;


import java.util.ArrayList;
import java.util.List;

import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;


/**
 * 用户开票信息登记 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class InvoiceUserServiceImpl implements InvoiceUserService {

    @Resource
    private InvoiceUserMapper invoiceUserMapper;
    @Resource
    private PersonalInfoMapper personalInfoMapper;

    /**
     * 创建用户开票信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<InvoiceUserSaveRespVO> createInvoiceUser(InvoiceUserDTO invoiceUserDTO) {
        try {
            // 避免空指针异常
            if (invoiceUserDTO == null) {
                invoiceUserDTO = new InvoiceUserDTO();
            }
            // 获取当前登录用户的ssoUserId
            invoiceUserDTO.setMemberId(SecurityFrameworkUtils.getLoginUserId());
            invoiceUserDTO.setMemberId(114514L);
            // 如果参数中发票抬头、纳税人识别号、邮箱任意为空，则执行查询当前用户是否存在开票信息
            if (invoiceUserDTO.getInvoiceTitle() == null || invoiceUserDTO.getTaxNumber() == null || invoiceUserDTO.getEmail() == null) {
                InvoiceUserDO invoiceUser = invoiceUserMapper.getInvoiceUserByMemberId(invoiceUserDTO.getMemberId());
                if (invoiceUser != null) {
                    return CommonResult.success(BeanUtils.toBean(invoiceUser, InvoiceUserSaveRespVO.class));
                }
                return CommonResult.error(ErrorCodeConstants.PARAMETER_IS_EMPTY);
            }
            // 插入
            InvoiceUserDO invoiceUser = BeanUtils.toBean(invoiceUserDTO, InvoiceUserDO.class);
            int i = invoiceUserMapper.insert(invoiceUser);
            if (i != 1) {
                return CommonResult.error(ErrorCodeConstants.CREATE_INVOICE_USER_FAIL);
            }
            invoiceUserDTO.setMemberId(null);
        }
        catch (Exception e) {
            log.error("创建用户开票信息失败", e);
            throw new ServiceException(ErrorCodeConstants.CREATE_INVOICE_USER_FAIL);
        }
        return CommonResult.success(BeanUtils.toBean(invoiceUserDTO, InvoiceUserSaveRespVO.class));
    }

    /**
     * 修改用户开票信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateInvoiceUser(InvoiceUserDTO invoiceUserDTO) {
        // 通过手机号验证该用户是否存在
        QueryWrapper<PersonalInfoDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq( "member_phone", invoiceUserDTO.getMemberPhone());
        if (personalInfoMapper.selectOne(queryWrapper) == null) {
            throw new ServiceException(ErrorCodeConstants.USER_NOT_EXIST);
        }
        // 执行修改操作并校验
        InvoiceUserDO updateObj = BeanUtils.toBean(invoiceUserDTO, InvoiceUserDO.class);
        if (invoiceUserMapper.updateById(updateObj) != 1) {
            throw new ServiceException(ErrorCodeConstants.UPDATE_INVOICE_USER_FAIL);
        }
    }

    @Override
    public void deleteInvoiceUser(Long id) {
        // 校验存在
        validateInvoiceUserExists(id);
        // 删除
        invoiceUserMapper.deleteById(id);
    }

    private void validateInvoiceUserExists(Long id) {
        if (invoiceUserMapper.selectById(id) == null) {
            throw exception(ErrorCodeConstants.PARAMETER_IS_EMPTY);
        }
    }

    @Override
    public InvoiceUserDO getInvoiceUser(Long id) {
        return invoiceUserMapper.selectById(id);
    }

    /**
     * 获得用户开票信息分页（查看开票历史与状态）
     *
     * @param pageReqVO 分页查询
     * @return 用户开票信息分页
     */
    @Override
    public PageResult<InvoiceUserDO> getInvoiceUserPage(InvoiceUserPageReqVO pageReqVO) {
        return invoiceUserMapper.selectPage(pageReqVO);
    }

}