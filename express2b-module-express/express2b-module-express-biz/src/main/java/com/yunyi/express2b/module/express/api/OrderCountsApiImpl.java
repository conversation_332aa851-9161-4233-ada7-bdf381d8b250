package com.yunyi.express2b.module.express.api;

import cn.hutool.core.util.ObjectUtil;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.security.core.util.SecurityFrameworkUtils;
import com.yunyi.express2b.module.agent.api.AgentApi;
import com.yunyi.express2b.module.agent.api.dto.AgentActivityDTO;
import com.yunyi.express2b.module.agent.api.dto.AgentInfoDTO;
import com.yunyi.express2b.module.express.api.order.OrderCountsApi;
import com.yunyi.express2b.module.express.api.order.dto.OrderCountsDTO;
import com.yunyi.express2b.module.express.api.order.dto.OrderDTO;
import com.yunyi.express2b.module.express.api.vo.RequestOrderVO;
import com.yunyi.express2b.module.express.dal.dataobject.order.OrderDO;
import com.yunyi.express2b.module.express.dal.mysql.order.OrderMapper;
import com.yunyi.express2b.module.express.dal.mysql.paymentorder.PaymentOrderMapper;
import com.yunyi.express2b.module.express.dal.mysql.products.ProductsMapper;
import com.yunyi.express2b.module.express.enums.ErrorCodeConstants;
import com.yunyi.express2b.module.express.mapstruct.OrderMapStructMapper;
import com.yunyi.express2b.module.wallet.api.TransationsApi.TransactionsApi;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.module.agent.enums.ConstValue.DEFAULT_ACTIVITY_COUNT;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/6/3 14:30
 */
@Service
@Slf4j
public class OrderCountsApiImpl implements OrderCountsApi {
    @Resource
    private OrderMapper orderMapper;
    @Resource
    private AgentApi agentApi;
    @Resource
    private ProductsMapper productsMapper;
    @Resource
    private PaymentOrderMapper paymentOrderMapper;
    @Resource
    private TransactionsApi transactionsApi;

    /**
     * 查询订单数量
     * @return
     */
    @Override
    public OrderCountsDTO selectOrderCount() {
        Long loginUserId = SecurityFrameworkUtils.getLoginUserId();
        if (loginUserId == null) {
            throw exception(ErrorCodeConstants.USER_NOT_EXISTS);
        }

        AgentInfoDTO agentInfoDTO = agentApi.getAgentByMemberId(loginUserId);
        if (ObjectUtil.isEmpty(agentInfoDTO)) {
            throw exception(ErrorCodeConstants.AGENT_NOT_EXISTS);
        }

        //为OrderCountsDTO设置默认值，否则当没有订单时，会报空指针异常(新注册账户下常见)
        OrderCountsDTO orderCountsDTO = new OrderCountsDTO()
                .setSelectAllOrderCount(0L)
                .setSelectCompletedOrderCount(0L)
                .setSelectPaidOrderCount(0L)
                .setSelectTodayOrderCount(0L);

        //根据当前用户id查询代理商id
        OrderCountsDTO getOrderCountsDTO= orderMapper.selectOrderCounts(agentInfoDTO.getId());

        //判断getOrderCountsDTO是否为空
        if (!ObjectUtil.isEmpty(getOrderCountsDTO)) {
            orderCountsDTO = getOrderCountsDTO;
        }

        //获取今日订单量
        log.info("【开始执行（今日累计订单量）的方法】---------------------");
        //当天的时间范围
        LocalDateTime startTime = LocalDate.now().atStartOfDay();
        LocalDateTime endTime = startTime.plusDays(1);
        // 创建一个DateTimeFormatter对象，定义所需的格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 使用formatter格式化LocalDateTime对象
        String startTime1 = startTime.format(formatter);
        String endTime1 = endTime.format(formatter);
        Long aLong = orderMapper.selectOrderCountByAgentId(startTime, endTime, agentInfoDTO.getId());
        orderCountsDTO.setSelectTodayOrderCount(aLong);
        return orderCountsDTO;
    }

    @Override
    public PageResult<OrderDTO> selectOrderList(RequestOrderVO orderRequestVO) {
//        if(orderRequestVO.getStatus().equals(OrderStatusTypeEnum.PAID.getStatus())){
//            List<String> status = Arrays.asList(OrderStatusTypeEnum.PAID.getStatus(),
//                    OrderStatusTypeEnum.PICKUP_PENDING.getStatus(),
//                    OrderStatusTypeEnum.WAIT.getStatus(),
//                    OrderStatusTypeEnum.COMPLETED.getStatus(),
//                    OrderStatusTypeEnum.IN_TRANSIT.getStatus(),
//                    OrderStatusTypeEnum.PARTIAL_PAYMENT_PENDING.getStatus());
//        }
        log.info("【开始执行（查询订单列表）的方法】---------------------");
        Long loginUserId = SecurityFrameworkUtils.getLoginUserId();
        if (loginUserId == null) {
            throw exception(ErrorCodeConstants.USER_NOT_EXISTS);
        }
        AgentInfoDTO agentInfoDTO = agentApi.getAgentByMemberId(loginUserId);
        PageResult<OrderDO> orderDOList = orderMapper.selectOrderListByStatus(agentInfoDTO.getId(), orderRequestVO);
        log.info("【查询订单列表】:{}", orderDOList);

        List<OrderDTO> orderDTOList = OrderMapStructMapper.INSTANCE.DoToDtoList(orderDOList.getList());
        log.info("【转化后查询订单列表】:{}", orderDTOList);
        PageResult<OrderDTO> pageResult = new PageResult<>(orderDTOList, orderDOList.getTotal());
        return pageResult;
    }
    /**
     * 在某一时间范围查询代理商订单数量
     * @param agentId
     * @return
     */
    @Override
    public Long getAgentOrderSum(Long agentId, LocalDateTime startTime, LocalDateTime endTime) {
        return orderMapper.getAgentOrderSum(agentId,startTime,endTime);
    }

    /**
     * 查询代理商订单活跃度
     * @param agentId
     * @return
     */
    @Override
    public List<AgentActivityDTO> getAgentActivity(List<Long> agentId) {
        List<AgentActivityDTO> agentActivityDTOS = new ArrayList<>();
        for (Long id: agentId) {
            //获取代理商活跃度
            AgentActivityDTO agentActivityDTO = orderMapper.getAgentOrderActivity(id);
            //获取代理商付款与未付款订单数
            agentActivityDTO = paymentOrderMapper.getAgentUnpaidOrderActivity(agentActivityDTO);
            //获取代理商团队id
            List<Long> teamIds = agentApi.getAgentDirectIdList(id);
            //给空团队添加默认数据防止空指针异常,同时减少访问数据库次数
            if(teamIds.isEmpty()){
                agentActivityDTO
                        .setWeekTeamOrderCount(DEFAULT_ACTIVITY_COUNT)
                        .setWeekTeamOrderAmount(DEFAULT_ACTIVITY_COUNT)
                        .setLastWeekTeamOrderCount(DEFAULT_ACTIVITY_COUNT)
                        .setLastWeekTeamOrderAmount(DEFAULT_ACTIVITY_COUNT)
                        .setMonthTeamOrderCount(DEFAULT_ACTIVITY_COUNT)
                        .setMonthTeamOrderAmount(DEFAULT_ACTIVITY_COUNT)
                        .setLastMonthTeamOrderCount(DEFAULT_ACTIVITY_COUNT)
                        .setLastMonthTeamOrderAmount(DEFAULT_ACTIVITY_COUNT)
                        .setWeekTeamUnpaidOrderCount(DEFAULT_ACTIVITY_COUNT)
                        .setLastWeekTeamUnpaidOrderCount(DEFAULT_ACTIVITY_COUNT)
                        .setMonthTeamUnpaidOrderCount(DEFAULT_ACTIVITY_COUNT)
                        .setLastMonthTeamUnpaidOrderCount(DEFAULT_ACTIVITY_COUNT)
                        .setWeekTeamPaidOrderCount(DEFAULT_ACTIVITY_COUNT)
                        .setLastWeekTeamPaidOrderCount(DEFAULT_ACTIVITY_COUNT)
                        .setMonthTeamPaidOrderCount(DEFAULT_ACTIVITY_COUNT)
                        .setLastMonthTeamPaidOrderCount(DEFAULT_ACTIVITY_COUNT);
            }else{
                //获取代理商团队活跃度
                agentActivityDTO = orderMapper.getTeamOrderActivity(agentActivityDTO,teamIds);
                //获取代理商团队付款与未付款订单数
                agentActivityDTO = paymentOrderMapper.getTeamUnpaidOrderActivity(agentActivityDTO,teamIds);
            }
            //获取代理商推广人数
            agentActivityDTO = agentApi.getAgentPromotersCount(agentActivityDTO,teamIds);
            //获取代理商钱包流水状态数
            agentActivityDTO = transactionsApi.getAgentWalletTransactionsActivity(agentActivityDTO,teamIds);

            //存储数据
            agentActivityDTOS.add(agentActivityDTO);
        }
        return agentActivityDTOS;
    }

}
