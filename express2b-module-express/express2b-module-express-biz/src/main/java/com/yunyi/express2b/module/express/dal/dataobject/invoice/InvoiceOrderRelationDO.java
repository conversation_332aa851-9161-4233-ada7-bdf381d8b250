package com.yunyi.express2b.module.express.dal.dataobject.invoice;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.yunyi.express2b.framework.mybatis.core.dataobject.BaseDO;

/**
 * 发票订单关联 DO
 *
 * <AUTHOR>
 */
@TableName("express2_invoice_order_relation")
@KeySequence("express2_invoice_order_relation_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceOrderRelationDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 发票ID
     */
    private Long invoiceId;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 订单金额
     */
    private Double orderAmount;
    /**
     * 创建者
     */
    private String creator;

}