package com.yunyi.express2b.module.express.api;

import com.yunyi.express2b.module.express.api.order.OrderStatusApi;
import com.yunyi.express2b.module.express.api.order.dto.WorkbenchInterfaceDTO;
import com.yunyi.express2b.module.express.dal.mysql.order.OrderMapper;
import com.yunyi.express2b.module.express.dal.mysql.paymentorder.PaymentOrderMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/4/8 14:45
 */

@Service
public class OrderStatusApiImpl implements OrderStatusApi {
    @Resource
    private OrderMapper orderMapper;

    @Resource
    private PaymentOrderMapper paymentOrderMapper;

    /**
     * 根据用户id和未完成的状态查询结果
     * @param personId
     * @return
     */
    @Override
    public Boolean isOrderStatus(Long personId) {
        Long sum = orderMapper.selectOrderByPersonId(personId);
        if (sum > 0) {
            return false;
        }
        return true;
    }




    /**
     * 订单数 订单付款数 已入账订单数（） 带入账订单数（） 销售额 实时对比数据下方的对比昨日
     * @param workbenchInterfaceDTO
     * @return
     */
    @Override
    public WorkbenchInterfaceDTO workbenchInterface(WorkbenchInterfaceDTO workbenchInterfaceDTO) {
        // 获取今日和昨日的时间范围
        TimeRange todayRange = getTimeRange(LocalDateTime.now());
        TimeRange yesterdayRange = getTimeRange(LocalDateTime.now().minusDays(1));

        // 设置今日时间范围并查询
        workbenchInterfaceDTO.setStartTime(todayRange.start);
        workbenchInterfaceDTO.setEndTime(todayRange.end);
        WorkbenchInterfaceDTO workbenchDay = paymentOrderMapper.getWorkbenchInterface(workbenchInterfaceDTO);

        // 设置昨日时间范围并查询
        workbenchInterfaceDTO.setStartTime(yesterdayRange.start);
        workbenchInterfaceDTO.setEndTime(yesterdayRange.end);
        WorkbenchInterfaceDTO workbenchYesterday = paymentOrderMapper.getWorkbenchInterface(workbenchInterfaceDTO);

        // 创建返回结果对象
        WorkbenchInterfaceDTO result = new WorkbenchInterfaceDTO();

        // 复制基础数据
        copyBaseData(result, workbenchDay);

        // 计算并设置变化比率
        calculateAndSetRatios(result, workbenchDay, workbenchYesterday);

        // 判断变化量并设置状态
        result.setQuantityStatus(result.getOrderQuantitysize() != null && result.getOrderQuantitysize() > 0 ? "1" : "0");
        result.setPaymentQuantitysizeStatus(result.getOrderPaymentQuantitysize() != null && result.getOrderPaymentQuantitysize() > 0 ? "1" : "0");
        result.setAlreadyCreditedStatus(result.getAlreadyCreditedsize() != null && result.getAlreadyCreditedsize() > 0 ? "1" : "0");
        result.setNumberRecordedOrdersStatus(result.getNumberRecordedOrderssize() != null && result.getNumberRecordedOrderssize() > 0 ? "1" : "0");
        result.setSalesVolumeStatus(result.getSalesVolumesize() != null && result.getSalesVolumesize() > 0 ? "1" : "0");

        return result;
    }

    /**
     * 获取指定日期的时间范围（凌晨 0 点到该日结束）
     * @param dateTime 指定日期时间
     * @return 时间范围对象
     */
    private TimeRange getTimeRange(LocalDateTime dateTime) {
        LocalDateTime start = dateTime.with(LocalTime.MIN);
        LocalDateTime end = dateTime;
        return new TimeRange(start, end);
    }

    /**
     * 复制基础数据
     * @param result 结果对象
     * @param source 源对象
     */
    private void copyBaseData(WorkbenchInterfaceDTO result, WorkbenchInterfaceDTO source) {
        result.setOrderQuantity(source.getOrderQuantity());
        result.setOrderPaymentQuantity(source.getOrderPaymentQuantity());
        result.setAlreadyCredited(source.getAlreadyCredited());
        result.setNumberRecordedOrders(source.getNumberRecordedOrders());
        result.setSalesVolume(source.getSalesVolume());
        result.setAgentId(source.getAgentId());
        result.setEndTime(source.getEndTime());
    }

    /**
     * 计算并设置变化比率
     * @param result 结果对象
     * @param today 今日数据对象
     * @param yesterday 昨日数据对象
     */
    private void calculateAndSetRatios(WorkbenchInterfaceDTO result, WorkbenchInterfaceDTO today, WorkbenchInterfaceDTO yesterday) {
        calculateAndSetRatio(result, today, yesterday,
                WorkbenchInterfaceDTO::getOrderQuantity,
                WorkbenchInterfaceDTO::setOrderQuantitysize);
        calculateAndSetRatio(result, today, yesterday,
                WorkbenchInterfaceDTO::getOrderPaymentQuantity,
                WorkbenchInterfaceDTO::setOrderPaymentQuantitysize);
        calculateAndSetRatio(result, today, yesterday,
                WorkbenchInterfaceDTO::getAlreadyCredited,
                WorkbenchInterfaceDTO::setAlreadyCreditedsize);
        calculateAndSetRatio(result, today, yesterday,
                WorkbenchInterfaceDTO::getNumberRecordedOrders,
                WorkbenchInterfaceDTO::setNumberRecordedOrderssize);
        calculateAndSetRatio(result, today, yesterday,
                WorkbenchInterfaceDTO::getSalesVolume,
                WorkbenchInterfaceDTO::setSalesVolumesize);
    }

    /**
     * 计算并设置单个属性的变化比率
     * @param result 结果对象
     * @param today 今日数据对象
     * @param yesterday 昨日数据对象
     * @param getter 属性获取方法
     * @param setter 属性设置方法
     * @param <T> 属性类型
     */
    private <T extends Number> void calculateAndSetRatio(WorkbenchInterfaceDTO result, WorkbenchInterfaceDTO today, WorkbenchInterfaceDTO yesterday,
                                                         java.util.function.Function<WorkbenchInterfaceDTO, T> getter,
                                                         java.util.function.BiConsumer<WorkbenchInterfaceDTO, Double> setter) {
        T todayValue = getter.apply(today);
        T yesterdayValue = getter.apply(yesterday);

        if (todayValue == null && yesterdayValue == null) {
            setter.accept(result, 0.0);
            return;
        }

        double todayDouble = todayValue != null ? todayValue.doubleValue() : 0.0;
        double yesterdayDouble = yesterdayValue != null ? yesterdayValue.doubleValue() : 0.0;

        double ratio = calculateRatio(todayDouble - yesterdayDouble, yesterdayDouble);

        setter.accept(result, ratio);
    }


    // 封装计算比率的方法
    private double calculateRatio(Double dayValue, Double yesterdayValue) {
        if (yesterdayValue == null || yesterdayValue == 0) {
            return 0.0;
        }
        return (dayValue != null ? dayValue : 0.0) / yesterdayValue;
    }

    /**
     * 时间范围类
     */
    private static class TimeRange {
        LocalDateTime start;
        LocalDateTime end;

        TimeRange(LocalDateTime start, LocalDateTime end) {
            this.start = start;
            this.end = end;
        }
    }







}
