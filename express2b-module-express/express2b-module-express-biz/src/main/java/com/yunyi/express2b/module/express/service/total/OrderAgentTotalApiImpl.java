package com.yunyi.express2b.module.express.service.total;

import com.yunyi.express2b.module.express.api.ordertotal.OrderAgentTotalApi;
import com.yunyi.express2b.module.express.dal.mysql.order.OrderMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

/**
 * 查询所有已下单代理商的数量
 *
 * <AUTHOR>
 * @since 17
 * @version 1.0
 * @since 2025/5/26 下午2:42
 */

@Service
public class OrderAgentTotalApiImpl implements OrderAgentTotalApi {

    @Resource
    private OrderMapper orderMapper;
    /**
     * 查询出所有已下单的代理商数量
     * @return
     */
    @Override
    public Long getAgentCount() {
        return orderMapper.selectAgentCount();
    }

    /**
     * 查询出代理商某天的订单总数量
     * @param date
     * @return
     */
    @Override
    public Long getOrderCount(Long agentId,LocalDate date,LocalDate dateTomarry) {
        return orderMapper.selectOrderCountByDate(agentId,date.toString(),dateTomarry.toString());
    }

    /**
     * 查询出代理商某天的订单收入
     * @param agentId
     * @param date
     * @param dateTomarrow
     * @return
     */
    @Override
    public Long getOrderIncome(Long agentId, LocalDate date, LocalDate dateTomarrow) {
        return orderMapper.selectOrderIncomeByDate(agentId,date.toString(),dateTomarrow.toString());
    }

    /**
     * 查询当前代理商的订单数量
     * @param agentId
     * @return
     */
    @Override
    public Long getAgentOrderCount(Long agentId) {
        return orderMapper.selectOrderCountByAgentId(agentId);
    }

    /**
     * 根据时间查询代理商的订单数量
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public Long getAgentOrderCountByDate(LocalDate startTime, LocalDate endTime) {
        return orderMapper.selectOrderCountByDate(startTime,endTime);
    }

    /**
     * 根据日期查找平台订单当日收入
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public Long getOrderIncomeByDate(LocalDate startTime, LocalDate endTime) {
        return orderMapper.selectOrderIncomeByAdminDate(startTime,endTime);
    }
}