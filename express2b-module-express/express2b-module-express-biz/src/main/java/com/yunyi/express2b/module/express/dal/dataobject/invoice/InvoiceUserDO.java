package com.yunyi.express2b.module.express.dal.dataobject.invoice;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import com.yunyi.express2b.framework.mybatis.core.dataobject.BaseDO;

/**
 * 用户开票信息登记 DO
 *
 * <AUTHOR>
 */
@TableName("express2_invoice_user")
@KeySequence("express2_invoice_user_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceUserDO extends BaseDO {

    /**
     * 记录ID
     */
    @TableId
    private Long id;
    /**
     * 用户ID对应统一登录平台的ssoUserId
     */
    private Long memberId;
    /**
     * 发票抬头（单位或个人名称）
     */
    private String invoiceTitle;
    /**
     * 纳税人识别号
     */
    private String taxNumber;
    /**
     * 单位地址
     */
    private String companyAddress;
    /**
     * 单位电话
     */
    private String companyPhone;
    /**
     * 开户行
     */
    private String bankName;
    /**
     * 银行基本户账号
     */
    private String bankAccount;
    /**
     * 接收邮箱
     */
    private String email;
    /**
     * 租户编号
     */
    private Long tenantId;

}