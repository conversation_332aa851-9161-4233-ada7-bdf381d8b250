package com.yunyi.express2b.module.express.service.order;

import com.alibaba.fastjson.JSONObject;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.module.express.controller.admin.order.vo.OfficialAccountResponseVO;
import com.yunyi.express2b.module.express.controller.admin.order.vo.ReceiveMessageRequest;
import com.yunyi.express2b.module.express.controller.app.v1.order.vo.PosterTemplateVO;
import com.yunyi.express2b.module.express.controller.app.v1.order.vo.UpdateUserRequest;
import com.yunyi.express2b.module.express.dal.dataobject.order.OrderDO;
import com.yunyi.framework.api.login.api.cs.vo.CustomerServicesRequest;
import com.yunyi.framework.api.login.api.cs.vo.CustomerServicesResponse;
import com.yunyi.framework.api.login.api.message.vo.TemplatePageResponse;
import com.yunyi.framework.api.login.api.qrcode.vo.PushRequest;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * 订单推广服务接口
 * <p>
 * 负责与推广平台、公众号、客服等外部集成的逻辑
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/4/16 16:58
 */
public interface OrderPromotionService {

    /**
     * 订单推送到统一推广平台
     *
     * @return 是否推送成功
     */
    Boolean createOrderToPromo();

    /**
     * 获取在线客服数据
     *
     * @param request 客服请求
     * @return 客服响应
     */
    CustomerServicesResponse getOnlineCustomerData(CustomerServicesRequest request);

    /**
     * 下载推广海报
     *
     * @return 海报模板列表
     */
    List<PosterTemplateVO> downloadPoster();

    /**
     * 分享小程序
     *
     * @return JSON对象
     */
    JSONObject generateApplets();

    /**
     * 创建二维码
     *
     * @return JSON对象
     */
    JSONObject createQrCode();

    /**
     * 更新个人数据
     *
     * @param updateUserRequest 更新用户请求
     * @return JSON对象
     */
    JSONObject updatePersonal(UpdateUserRequest updateUserRequest);

    /**
     * 获取公众号链接
     *
     * @return 公众号响应
     */
    OfficialAccountResponseVO officialAccountLink();

    /**
     * 公众号关注或取关回调
     *
     * @param pushRequest 推送请求
     * @return 影响行数
     */
    Long officialAccountAttentionOrClearance(PushRequest pushRequest);

    /**
     * 获取开放平台代理
     *
     * @return JSON对象
     */
    JSONObject openPlatformAgent();

    /**
     * 异步通知推广平台更新订单状态
     *
     * @param orderDO  订单信息
     * @param status   目标状态
     * @param executor 线程池
     * @return 操作结果
     */
    CompletableFuture<Boolean> asyncNotifyUpdateOrderStatus(OrderDO orderDO, Integer status, Executor executor);

    /**
     * 异步通知推广平台更新订单金额
     *
     * @param orderDO  订单信息
     * @param executor 线程池
     * @return 操作结果
     */
    CompletableFuture<Boolean> asyncNotifyUpdateOrderAmount(OrderDO orderDO, Executor executor);

    /**
     * 异步通知推广平台取消订单
     *
     * @param orderDO  订单信息
     * @return 操作结果
     */
    CompletableFuture<Boolean> asyncNotifyCancelOrder(OrderDO orderDO);

    /**
     * 接收并处理订单消息
     *
     * @param request 消息接收请求参数对象
     * @return 消息处理结果标识字符串
     */
    String receiveOrderMessage(ReceiveMessageRequest request);

    /**
     * 订阅列表
     *
     * @return
     */
    TemplatePageResponse sendtemplete();
}
