package com.yunyi.express2b.module.express.dal.mysql.paymentorder;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.util.date.LocalDateTimeUtils;
import com.yunyi.express2b.framework.mybatis.core.mapper.BaseMapperX;
import com.yunyi.express2b.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yunyi.express2b.framework.mybatis.core.query.MPJLambdaWrapperX;
import com.yunyi.express2b.module.agent.api.dto.AgentActivityDTO;
import com.yunyi.express2b.module.agent.dal.dataobject.agentprofile.AgentProfileDO;
import com.yunyi.express2b.module.express.controller.admin.paymentorder.vo.PaymentOrderPageQueryVO;
import com.yunyi.express2b.module.express.api.order.dto.WorkbenchInterfaceDTO;
import com.yunyi.express2b.module.express.controller.admin.paymentorder.vo.PaymentOrderPageReqVO;
import com.yunyi.express2b.module.express.dal.dataobject.order.OrderDO;
import com.yunyi.express2b.module.express.dal.dataobject.paymentorder.PaymentOrderDO;
import com.yunyi.express2b.module.express.enums.PaymentStatusTypeEnum;
import org.apache.ibatis.annotations.Mapper;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.yunyi.express2b.module.agent.enums.ConstValue.*;

/**
 * 支付订单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PaymentOrderMapper extends BaseMapperX<PaymentOrderDO> {

    default PageResult<PaymentOrderDO> selectPage(PaymentOrderPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PaymentOrderDO>()
                .eqIfPresent(PaymentOrderDO::getTransactionNo, reqVO.getTransactionNo())
                .eqIfPresent(PaymentOrderDO::getOutTransactionNo, reqVO.getOutTransactionNo())
                .eqIfPresent(PaymentOrderDO::getOutOutTransactionNo, reqVO.getOutOutTransactionNo())
                .eqIfPresent(PaymentOrderDO::getOrderId, reqVO.getOrderId())
                .eqIfPresent(PaymentOrderDO::getAmount, reqVO.getAmount())
                .eqIfPresent(PaymentOrderDO::getStatus, reqVO.getStatus())
                .eqIfPresent(PaymentOrderDO::getPaymentMethod, reqVO.getPaymentMethod())
                .eqIfPresent(PaymentOrderDO::getContent, reqVO.getContent())
                .eqIfPresent(PaymentOrderDO::getFee, reqVO.getFee())
                .eqIfPresent(PaymentOrderDO::getPaymentNumber, reqVO.getPaymentNumber())
                .betweenIfPresent(PaymentOrderDO::getPaymentSubmitTime, reqVO.getPaymentSubmitTime())
                .betweenIfPresent(PaymentOrderDO::getLastStatusUpdateTime, reqVO.getLastStatusUpdateTime())
                .betweenIfPresent(PaymentOrderDO::getLastRefundTime, reqVO.getLastRefundTime())
                .eqIfPresent(PaymentOrderDO::getReconciliationId, reqVO.getReconciliationId())
                .betweenIfPresent(PaymentOrderDO::getReconciliationTime, reqVO.getReconciliationTime())
                .betweenIfPresent(PaymentOrderDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(PaymentOrderDO::getIp, reqVO.getIp())
                .eqIfPresent(PaymentOrderDO::getClientId, reqVO.getClientId())
                .eqIfPresent(PaymentOrderDO::getBody, reqVO.getBody())
                .eqIfPresent(PaymentOrderDO::getMemberId, reqVO.getMemberId())
                .orderByDesc(PaymentOrderDO::getId));
    }

    /**
     * 根据查询对象（包含快递单号）分页数据
     * @param queryVO
     * @return
     */
    default PageResult<PaymentOrderDO> selectPage(PaymentOrderPageQueryVO queryVO){
        MPJLambdaWrapper<PaymentOrderDO> paymentOrderDOMPJLambdaWrapper = new MPJLambdaWrapperX<PaymentOrderDO>()
                .leftJoin(OrderDO.class, OrderDO::getId, PaymentOrderDO::getOrderId)
                .eqIfExists(OrderDO::getTrackingNumber, queryVO.getTrackingNumber())
                .eqIfExists(PaymentOrderDO::getOrderId, queryVO.getOrderId())
                .eqIfExists(PaymentOrderDO::getStatus, queryVO.getStatus())
                .eqIfExists(PaymentOrderDO::getPaymentMethod, queryVO.getPaymentMethod());
        //注意 between不会自动检测null，并且参数优先执行会触发空指针，先手动判断
        if (ObjectUtil.isNotEmpty(queryVO.getPaymentSubmitTime()) && queryVO.getPaymentSubmitTime().length >= 2) {
            paymentOrderDOMPJLambdaWrapper.between(PaymentOrderDO::getPaymentSubmitTime,queryVO.getPaymentSubmitTime()[0],queryVO.getPaymentSubmitTime()[1]);
        }
        if (ObjectUtil.isNotEmpty(queryVO.getLastRefundTime()) && queryVO.getLastRefundTime().length >= 2) {
            paymentOrderDOMPJLambdaWrapper.between(PaymentOrderDO::getLastRefundTime,queryVO.getLastRefundTime()[0],queryVO.getLastRefundTime()[1]);
        }
        if (ObjectUtil.isNotEmpty(queryVO.getCreateTime()) && queryVO.getCreateTime().length >= 2) {
            paymentOrderDOMPJLambdaWrapper.between(PaymentOrderDO::getCreateTime,queryVO.getCreateTime()[0],queryVO.getCreateTime()[1]);
        }
        return selectPage(queryVO, paymentOrderDOMPJLambdaWrapper);
    }
    //根据交易号查询支付订单号
//    default PaymentOrderDO selectByTransactionNo(String transactionNo){
//        return selectOne(new LambdaQueryWrapperX<PaymentOrderDO>().eq(PaymentOrderDO::getTransactionNo,transactionNo));
//    }

    default PaymentOrderDO selectByTransactionNo(String transactionNo) {
        List<PaymentOrderDO> list = selectList(new LambdaQueryWrapper<PaymentOrderDO>()
                .eq(PaymentOrderDO::getTransactionNo, transactionNo)
                .last("LIMIT 1"));
        return list.isEmpty() ? null : list.get(0);
    }

    default List<PaymentOrderDO> selectListByTransactionNo(String payOrderSn) {
        return selectList(new LambdaQueryWrapper<PaymentOrderDO>()
                .in(PaymentOrderDO::getTransactionNo, payOrderSn));
    }

    default Integer updateStatus(Long id, String status) {
        return update(new PaymentOrderDO().setId(id).setStatus(status), new LambdaQueryWrapperX<PaymentOrderDO>().eq(PaymentOrderDO::getId, id));
    }

    /**
     * 根据支付单id列表查询支付成功的支付订单
     */
    default List<PaymentOrderDO> selectListByOrderId(List<Long> longs) {
        return selectList(new LambdaQueryWrapper<PaymentOrderDO>()
                .in(PaymentOrderDO::getId, longs)
                .eq(PaymentOrderDO::getStatus, PaymentStatusTypeEnum.SUCCESS.getStatus()));
    }

    default List<PaymentOrderDO> selectListByIds(List<Long> longs) {
        return selectList(new LambdaQueryWrapper<PaymentOrderDO>()
                .in(PaymentOrderDO::getOrderId, longs)
                .eq(PaymentOrderDO::getStatus, PaymentStatusTypeEnum.SUCCESS.getStatus()));
    }

    default List<PaymentOrderDO> selectListById(Long id) {
        return selectList(new LambdaQueryWrapper<PaymentOrderDO>()
                .eq(PaymentOrderDO::getOrderId, id)
                .eq(PaymentOrderDO::getStatus, PaymentStatusTypeEnum.SUCCESS.getStatus()));
    }

    /**
     * 查询超时而且未支付状态的支付订单
     */
    default List<PaymentOrderDO> selectTimeoutPaymentOrders(Duration outTimeDuration) {
        return selectList(new LambdaQueryWrapper<PaymentOrderDO>()
                .select(PaymentOrderDO::getId)
                .eq(PaymentOrderDO::getStatus, PaymentStatusTypeEnum.NOTPAY.getStatus())
                .le(PaymentOrderDO::getPaymentSubmitTime, LocalDateTimeUtils.minusTime(outTimeDuration)));
    }

    default PaymentOrderDO getPaymentOrder(Long orderId) {
        return selectOne(new LambdaQueryWrapper<PaymentOrderDO>().eq(PaymentOrderDO::getOrderId, orderId));
    }

    //根据订单表的id查询支付订单
    default Long selectOrderCountByOrderId(Long orderId) {
        return selectCount(new LambdaQueryWrapper<PaymentOrderDO>().eq(PaymentOrderDO::getOrderId, orderId));
    }

    //根据订单表的id查询支付订单(会有多条)
    default List<PaymentOrderDO> getPaymentOrders(Long id) {
        return selectList(new LambdaQueryWrapper<PaymentOrderDO>().eq(PaymentOrderDO::getOrderId, id));
    }

    //查询支付成功的支付订单
    default List<PaymentOrderDO> selectPaymentOrdersListByOrderId(Long paymentOrderId) {
        return selectList(new LambdaQueryWrapper<PaymentOrderDO>()
                .eq(PaymentOrderDO::getOrderId, paymentOrderId)
                .eq(PaymentOrderDO::getStatus, PaymentStatusTypeEnum.SUCCESS.getStatus()));
    }
//根据退款编号查询支付单信息
    default PaymentOrderDO selectPaymentOrderInfo(Long paymentOrderId) {
        return selectOne(new LambdaQueryWrapper<PaymentOrderDO>()
                .eq(PaymentOrderDO::getId, paymentOrderId));
    }

    WorkbenchInterfaceDTO getWorkbenchInterface(WorkbenchInterfaceDTO workbenchInterfaceDTO);
//查询退款单中

    /**
     * 获取代理商未支付与支付订单数量活跃度
     * @param agentActivityDTO
     * @return
     */
    default AgentActivityDTO getAgentUnpaidOrderActivity(AgentActivityDTO agentActivityDTO){

        //统计本周代理商创建的订单数据
        List<PaymentOrderDO> weekOrderList = selectList(new MPJLambdaWrapperX<PaymentOrderDO>()
                .select(PaymentOrderDO::getId, PaymentOrderDO::getStatus)
                .leftJoin(AgentProfileDO.class, AgentProfileDO::getMemberId, PaymentOrderDO::getMemberId)
                .eq(AgentProfileDO::getId, agentActivityDTO.getAgentId())
                .between(PaymentOrderDO::getCreateTime, WEEK_START, NOW_TIME)
        );
        //统计订单状态
        Map<String, Long> weekOrderMap = weekOrderList.stream()
                .collect(Collectors.groupingBy(PaymentOrderDO::getStatus, Collectors.counting()));
        Long weekUnpaidOrderCount = weekOrderMap.getOrDefault(PaymentStatusTypeEnum.NOTPAY.getStatus(), DEFAULT_ACTIVITY_COUNT);
        Long weekPaidOrderCount = weekOrderMap.getOrDefault(PaymentStatusTypeEnum.SUCCESS.getStatus(), DEFAULT_ACTIVITY_COUNT);

        //统计上周代理商创建的订单数据
        List<PaymentOrderDO> lastWeekOrderList = selectList(new MPJLambdaWrapperX<PaymentOrderDO>()
                .select(PaymentOrderDO::getId,
                        PaymentOrderDO::getStatus)
                .leftJoin(AgentProfileDO.class,AgentProfileDO::getMemberId,PaymentOrderDO::getMemberId)
                .eq(AgentProfileDO::getId,agentActivityDTO.getAgentId())
                .between(PaymentOrderDO::getCreateTime, LAST_WEEK_START, LAST_WEEK_END)
        );
        //统计订单状态
        Map<String, Long> lastWeekOrderMap = lastWeekOrderList.stream()
                .collect(Collectors.groupingBy(PaymentOrderDO::getStatus, Collectors.counting()));
        Long lastWeekUnpaidOrderCount = lastWeekOrderMap.getOrDefault(PaymentStatusTypeEnum.NOTPAY.getStatus(), DEFAULT_ACTIVITY_COUNT);
        Long lastWeekPaidOrderCount = lastWeekOrderMap.getOrDefault(PaymentStatusTypeEnum.SUCCESS.getStatus(), DEFAULT_ACTIVITY_COUNT);

        //统计本月代理商创建的订单数据
        List<PaymentOrderDO> monthOrderList = selectList(new MPJLambdaWrapperX<PaymentOrderDO>()
                .select(PaymentOrderDO::getId,
                        PaymentOrderDO::getStatus)
                .leftJoin(AgentProfileDO.class,AgentProfileDO::getMemberId,PaymentOrderDO::getMemberId)
                .eq(AgentProfileDO::getId,agentActivityDTO.getAgentId())
                .between(PaymentOrderDO::getCreateTime, MONTH_START, NOW_TIME)
        );
        //统计订单状态
        Map<String, Long> monthOrderMap = monthOrderList.stream()
                .collect(Collectors.groupingBy(PaymentOrderDO::getStatus, Collectors.counting()));
        Long monthUnpaidOrderCount = monthOrderMap.getOrDefault(PaymentStatusTypeEnum.NOTPAY.getStatus(), DEFAULT_ACTIVITY_COUNT);
        Long monthPaidOrderCount = monthOrderMap.getOrDefault(PaymentStatusTypeEnum.SUCCESS.getStatus(), DEFAULT_ACTIVITY_COUNT);

        //统计上月代理商创建的订单数据
        List<PaymentOrderDO> lastMonthOrderList = selectList(new MPJLambdaWrapperX<PaymentOrderDO>()
                .select(PaymentOrderDO::getId,
                        PaymentOrderDO::getStatus)
                .leftJoin(AgentProfileDO.class,AgentProfileDO::getMemberId,PaymentOrderDO::getMemberId)
                .eq(AgentProfileDO::getId,agentActivityDTO.getAgentId())
                .between(PaymentOrderDO::getCreateTime, LAST_MONTH_START, LAST_MONTH_END)
        );
        //统计订单状态
        Map<String, Long> lastMonthOrderMap = lastMonthOrderList.stream()
                .collect(Collectors.groupingBy(PaymentOrderDO::getStatus, Collectors.counting()));
        Long lastMonthUnpaidOrderCount = lastMonthOrderMap.getOrDefault(PaymentStatusTypeEnum.NOTPAY.getStatus(), DEFAULT_ACTIVITY_COUNT);
        Long lastMonthPaidOrderCount = lastMonthOrderMap.getOrDefault(PaymentStatusTypeEnum.SUCCESS.getStatus(), DEFAULT_ACTIVITY_COUNT);

        //封装数据
        return agentActivityDTO
                .setWeekUnpaidOrderCount(weekUnpaidOrderCount)
                .setMonthUnpaidOrderCount(monthUnpaidOrderCount)
                .setLastWeekUnpaidOrderCount(lastWeekUnpaidOrderCount)
                .setLastMonthUnpaidOrderCount(lastMonthUnpaidOrderCount)
                .setWeekPaidOrderCount(weekPaidOrderCount)
                .setMonthPaidOrderCount(monthPaidOrderCount)
                .setLastWeekPaidOrderCount(lastWeekPaidOrderCount)
                .setLastMonthPaidOrderCount(lastMonthPaidOrderCount);
    }

    /**
     * 获取代理商团队支付与未支付订单数量
     */
    default AgentActivityDTO getTeamUnpaidOrderActivity(AgentActivityDTO agentActivityDTO,List<Long> teamIds){

        //统计本周代理商直属团队创建的订单数据
        List<PaymentOrderDO> weekTeamOrderList = selectList(new MPJLambdaWrapperX<PaymentOrderDO>()
                .select(
                        PaymentOrderDO::getId,
                        PaymentOrderDO::getStatus
                )
                .leftJoin(AgentProfileDO.class,AgentProfileDO::getMemberId,PaymentOrderDO::getMemberId)
                .in(AgentProfileDO::getId,teamIds)
                .between(PaymentOrderDO::getCreateTime, WEEK_START, NOW_TIME)
        );
        //统计订单状态
        Map<String, Long> weekTeamOrderMap = weekTeamOrderList.stream()
                .collect(Collectors.groupingBy(PaymentOrderDO::getStatus, Collectors.counting()));
        Long weekTeamUnpaidOrderCount = weekTeamOrderMap.getOrDefault(PaymentStatusTypeEnum.NOTPAY.getStatus(), DEFAULT_ACTIVITY_COUNT);
        Long weekTeamPaidOrderCount = weekTeamOrderMap.getOrDefault(PaymentStatusTypeEnum.SUCCESS.getStatus(), DEFAULT_ACTIVITY_COUNT);

        //统计上周代理商直属团队创建的订单数据
        List<PaymentOrderDO> lastWeekTeamOrderList = selectList(new MPJLambdaWrapperX<PaymentOrderDO>()
                .select(
                        PaymentOrderDO::getId,
                        PaymentOrderDO::getStatus
                )
                .leftJoin(AgentProfileDO.class,AgentProfileDO::getMemberId,PaymentOrderDO::getMemberId)
                .in(AgentProfileDO::getId,teamIds)
                .between(PaymentOrderDO::getCreateTime, LAST_WEEK_START, LAST_WEEK_END)
        );
        //统计订单状态
        Map<String, Long> lastWeekTeamOrderMap = lastWeekTeamOrderList.stream()
                .collect(Collectors.groupingBy(PaymentOrderDO::getStatus, Collectors.counting()));
        Long lastWeekTeamUnpaidOrderCount = lastWeekTeamOrderMap.getOrDefault(PaymentStatusTypeEnum.NOTPAY.getStatus(), DEFAULT_ACTIVITY_COUNT);
        Long lastWeekTeamPaidOrderCount = lastWeekTeamOrderMap.getOrDefault(PaymentStatusTypeEnum.SUCCESS.getStatus(), DEFAULT_ACTIVITY_COUNT);

        //统计本月代理商直属团队创建的订单数据
        List<PaymentOrderDO> monthTeamOrderList = selectList(new MPJLambdaWrapperX<PaymentOrderDO>()
                .select(
                        PaymentOrderDO::getId,
                        PaymentOrderDO::getStatus
                )
                .leftJoin(AgentProfileDO.class,AgentProfileDO::getMemberId,PaymentOrderDO::getMemberId)
                .in(AgentProfileDO::getId,teamIds)
                .between(PaymentOrderDO::getCreateTime, MONTH_START, NOW_TIME)
        );
        //统计订单状态
        Map<String, Long> monthTeamOrderMap = monthTeamOrderList.stream()
                .collect(Collectors.groupingBy(PaymentOrderDO::getStatus, Collectors.counting()));
        Long monthTeamUnpaidOrderCount = monthTeamOrderMap.getOrDefault(PaymentStatusTypeEnum.NOTPAY.getStatus(), DEFAULT_ACTIVITY_COUNT);
        Long monthTeamPaidOrderCount = monthTeamOrderMap.getOrDefault(PaymentStatusTypeEnum.SUCCESS.getStatus(), DEFAULT_ACTIVITY_COUNT);

        //统计上月代理商直属团队创建的订单数据
        List<PaymentOrderDO> lastMonthTeamOrderList = selectList(new MPJLambdaWrapperX<PaymentOrderDO>()
                .select(
                        PaymentOrderDO::getId,
                        PaymentOrderDO::getStatus
                )
                .leftJoin(AgentProfileDO.class,AgentProfileDO::getMemberId,PaymentOrderDO::getMemberId)
                .in(AgentProfileDO::getId,teamIds)
                .between(PaymentOrderDO::getCreateTime, LAST_MONTH_START, LAST_MONTH_END)
        );
        //统计订单状态
        Map<String, Long> lastMonthTeamOrderMap = lastMonthTeamOrderList.stream()
                .collect(Collectors.groupingBy(PaymentOrderDO::getStatus, Collectors.counting()));
        Long lastMonthTeamUnpaidOrderCount = lastMonthTeamOrderMap.getOrDefault(PaymentStatusTypeEnum.NOTPAY.getStatus(), DEFAULT_ACTIVITY_COUNT);
        Long lastMonthTeamPaidOrderCount = lastMonthTeamOrderMap.getOrDefault(PaymentStatusTypeEnum.SUCCESS.getStatus(), DEFAULT_ACTIVITY_COUNT);

        //封装数据
        return agentActivityDTO
                .setWeekTeamUnpaidOrderCount(weekTeamUnpaidOrderCount)
                .setLastWeekTeamUnpaidOrderCount(lastWeekTeamUnpaidOrderCount)
                .setMonthTeamUnpaidOrderCount(monthTeamUnpaidOrderCount)
                .setLastMonthTeamUnpaidOrderCount(lastMonthTeamUnpaidOrderCount)
                .setWeekTeamPaidOrderCount(weekTeamPaidOrderCount)
                .setLastWeekTeamPaidOrderCount(lastWeekTeamPaidOrderCount)
                .setMonthTeamPaidOrderCount(monthTeamPaidOrderCount)
                .setLastMonthTeamPaidOrderCount(lastMonthTeamPaidOrderCount);
    }

}