package com.yunyi.express2b.module.express.controller.app.v1.invoice.dto.invoiceorderrelation;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yunyi.express2b.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.yunyi.express2b.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 发票订单关联分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InvoiceOrderRelationPageDTO extends PageParam {

    @Schema(description = "发票ID", example = "18102")
    private Long invoiceId;

    @Schema(description = "订单编号")
    private String orderNo;

    @Schema(description = "订单金额")
    private Double orderAmount;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}