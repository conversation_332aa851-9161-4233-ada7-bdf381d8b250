package com.yunyi.express2b.module.express.service.order;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.util.io.FileUtils;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.framework.excel.core.util.ExcelUtils;
import com.yunyi.express2b.framework.security.core.util.SecurityFrameworkUtils;
import com.yunyi.express2b.module.crm.api.address.AddressQueryApi;
import com.yunyi.express2b.module.crm.api.address.vo.AddressRespApiVO;
import com.yunyi.express2b.module.express.controller.admin.order.vo.CancelOrderCallBackVO;
import com.yunyi.express2b.module.express.controller.admin.order.vo.CancelOrderResponseVO;
import com.yunyi.express2b.module.express.controller.admin.order.vo.OrderCreateRequest;
import com.yunyi.express2b.module.express.controller.admin.order.vo.TrackCallBackVO;
import com.yunyi.express2b.module.express.controller.app.v1.order.vo.*;
import com.yunyi.express2b.module.express.dal.dataobject.batchorder.BatchOrderDO;
import com.yunyi.express2b.module.express.dal.dataobject.order.OrderDO;
import com.yunyi.express2b.module.express.dal.mysql.batchorder.BatchOrderMapper;
import com.yunyi.express2b.module.express.dal.mysql.order.OrderMapper;
import com.yunyi.express2b.module.express.enums.ErrorCodeConstants;
import com.yunyi.express2b.module.express.enums.ImportTypeEnum;
import com.yunyi.express2b.module.express.enums.social.ExpressLogRecordConstants;
import com.yunyi.express2b.module.express.mapstruct.OrderMapStructMapper;
import com.yunyi.express2b.module.express.utils.BatchOrderNumberUtil;
import com.yunyi.express2b.module.express.utils.OrderConstants;
import com.yunyi.framework.api.base.config.OrderConfig;
import com.yunyi.framework.api.login.api.image.ImageApi;
import com.yunyi.framework.api.login.api.image.vo.UploadImageRequest;
import com.yunyi.framework.api.login.api.image.vo.UploadImageResponse;
import com.yunyi.framework.api.logistics.api.express.ExpressApi;
import com.yunyi.framework.api.logistics.api.express.vo.*;
import com.yunyi.framework.api.logistics.api.express.vo.AddressListResponse;
import com.yunyi.framework.api.logistics.api.order.OrderApi;
import com.yunyi.framework.api.logistics.api.order.vo.CancelOrderRequest;
import com.yunyi.framework.api.logistics.api.order.vo.CancelOrderResponse;
import com.yunyi.framework.api.logistics.api.order.vo.CreateOrderRequest;
import com.yunyi.framework.api.logistics.api.order.vo.CreateOrderResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.alibaba.druid.util.StringUtils.isEmpty;
import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;

@Service
@Validated
@Slf4j
public class OrderLogisticsServiceImpl implements OrderLogisticsService {

    private final OrderMapper orderMapper;
    private final BatchOrderMapper batchOrderMapper;
    private final ExpressApi expressApi;
    private final OrderApi orderApi;
    private final ImageApi imageApi;
    private final AddressQueryApi addressQueryApi;
    private final RedisTemplate<String, Object> redisTemplate;
    private final Executor executor;
    private final OrderConfig orderConfig;
    private final ObjectMapper objectMapper;

    public OrderLogisticsServiceImpl(OrderMapper orderMapper,
                                     BatchOrderMapper batchOrderMapper,
                                     ExpressApi expressApi,
                                     OrderApi orderApi,
                                     ImageApi imageApi,
                                     AddressQueryApi addressQueryApi,
                                     RedisTemplate<String, Object> redisTemplate,
                                     @Qualifier("myTaskExecutor") Executor executor,
                                     OrderConfig orderConfig,
                                     ObjectMapper objectMapper) {
        this.orderMapper = orderMapper;
        this.batchOrderMapper = batchOrderMapper;
        this.expressApi = expressApi;
        this.orderApi = orderApi;
        this.imageApi = imageApi;
        this.addressQueryApi = addressQueryApi;
        this.redisTemplate = redisTemplate;
        this.executor = executor;
        this.orderConfig = orderConfig;
        this.objectMapper = objectMapper;
    }


    @Override
    @LogRecord(type = ExpressLogRecordConstants.CREATE_TRACKING,
            fail = "运力下单失败，失败原因：{{#_errorMsg}}",
            success = "运力下单所需参数{{#createTracking}},运力下单返回的结果{{#createOrderResponse}}",
            bizNo = "{{#userId}}")
    public CreateOrderResponse createTracking(OrderCreateRequest request) throws Exception {
        log.info("【开始执行向运力下单的方法，请求参数为】----------：{}", request);
        LogRecordContext.putVariable("createTracking", request);
        LogRecordContext.putVariable("userId", SecurityFrameworkUtils.getLoginUserId());

        //查询地址簿取出code
        Long senderId = request.getSenderAddressId();
        Long receiverId = request.getReceiverAddressId();
        List<Long> ids = Arrays.asList(senderId, receiverId);
        Map<Long, AddressRespApiVO> addressMap = getAddressMap(ids);
        AddressRespApiVO sender = addressMap.get(senderId);
        AddressRespApiVO receiver = addressMap.get(receiverId);
        if (sender == null || receiver == null) {
            throw exception(sender == null ? ErrorCodeConstants.SENDER_ADDRESS_NOT_EXISTS :
                    ErrorCodeConstants.RECEIVER_ADDRESS_NOT_EXISTS);
        }
        String senderAfterDistrict = sender.getAddress();
        String receiverAfterDistrict = receiver.getAddress();
        String scheduledStartTime = request.getScheduledStartTime().toString().substring(0, 5);
        String scheduledEndTime = request.getScheduledEndTime().toString().substring(0, 5);
        CreateOrderRequest createOrderRequest = OrderMapStructMapper.INSTANCE.orderSaveReqVOToRequest(request);
        log.info("【转换后的请求参数】：{}", createOrderRequest);
        createOrderRequest.setDayType(determineDayType(request.getAppointmentTime()));
        createOrderRequest.setCallBackUrl(orderConfig.getCallBackUrl());
        createOrderRequest.setChannelId(OrderConstants.channelId);
        createOrderRequest.setSenderCity(sender.getCityName());
        createOrderRequest.setSenderCityCode(sender.getCityCode());
        createOrderRequest.setSenderDistrict(sender.getDistrictName());
        createOrderRequest.setSenderDistrictCode(sender.getDistrictCode());
        createOrderRequest.setSenderProvince(sender.getProvinceName());
        createOrderRequest.setSenderProvinceCode(sender.getProvinceCode());
        createOrderRequest.setReceiverCity(receiver.getCityName());
        createOrderRequest.setReceiverCityCode(receiver.getCityCode());
        createOrderRequest.setReceiverDistrict(receiver.getDistrictName());
        createOrderRequest.setReceiverDistrictCode(receiver.getDistrictCode());
        createOrderRequest.setReceiverProvince(receiver.getProvinceName());
        createOrderRequest.setReceiverProvinceCode(receiver.getProvinceCode());
        createOrderRequest.setSenderAddress(senderAfterDistrict);
        createOrderRequest.setReceiverAddress(receiverAfterDistrict);
        createOrderRequest.setPickupStartTime(scheduledStartTime);
        createOrderRequest.setPickupEndTime(scheduledEndTime);
        CommonResult<CreateOrderResponse> result;
        CreateOrderResponse data;
        try {
            result = orderApi.createOrder(createOrderRequest);
            log.info("[调用运力下单接口返回信息]：{}", result);
            LogRecordContext.putVariable("createOrderResponse", result);
            if (result == null || result.getData() == null) {
                throw exception(ErrorCodeConstants.ADDRESS_PARSE_IS_EMPTY);
            }
            if (result.getCode() == OrderConstants.CODE) {
                log.info("【已成功下单，开始更新订单状态】");
            } else {
                throw exception(ErrorCodeConstants.ORDER_INFO_ERROR, result.getMsg());
            }
            data = objectMapper.convertValue(result.getData(), CreateOrderResponse.class);
            log.info("【接收返回参数，并进行参数转换】:{}", data);
            // 更新订单数据,同时更新订单状态为待接单
            int row = orderMapper.updateOrder(createOrderRequest.getOrderSn(), data.getUcmOrderSn());
            if (row > 0) {
                log.info("【更新订单数据成功】");
            }
            // 查询订单信息
            OrderDO orderDO = orderMapper.selectByOrderNo(request.getOrderNo());
            if (orderDO == null) {
                throw exception(ErrorCodeConstants.ORDER_NOT_EXISTS);
            }
        } catch (Exception e) {
            log.error("【运力下单异常】订单 {} 失败原因：{}", request.getOrderNo(), e.getMessage(), e);
            throw exception(ErrorCodeConstants.ORDER_CREATE_FAILURE, e.getMessage());
        }
        log.info("【结束执行向运力下单的方法，返回信息】----------：{}", data);
        LogRecordContext.putVariable("createOrderResponse", data);

        return data;
    }

    @Override
    public CancelOrderResponse ucmCancelOrder(CancelOrderRequest cancelOrderRequest) {
        CommonResult<CancelOrderResponse> cancelOrderResult;
        // 调用运力取消订单接口
        try {
            cancelOrderResult = orderApi.cancelOrder(cancelOrderRequest);
        } catch (Exception e) {
            throw exception(ErrorCodeConstants.CALLING_INTERFACE_ERROR, e.getMessage());
        }
        if (cancelOrderResult == null) {
            log.error("【调用运力取消订单接口返回结果为空】：{}", cancelOrderResult);
            throw exception(ErrorCodeConstants.ADDRESS_PARSE_IS_EMPTY);
        }
        //根据响应code值判断
        if (!OrderConstants.CODE.equals(cancelOrderResult.getCode())) {
            log.error("订单取消失败");
            throw exception(ErrorCodeConstants.CANCEL_ORDER_FAILED);
        }
        log.info("【运力取消订单成功】统一订单号：{}", cancelOrderRequest.getUcmOrderSn());
        return objectMapper.convertValue(cancelOrderResult.getData(), CancelOrderResponse.class);
    }

    @Override
    @LogRecord(type = ExpressLogRecordConstants.TRACKING,
            fail = "获取物流信息失败，失败原因：{{#_errorMsg}}",
            success = "运单号{{#trackingNumber}}，查询物流结果:{{#orderTrack}}",
            bizNo = "{{#trackingNumber}}")
    public List<ExpressTrackingData> getOrderLogistics(String trackingNumber, Long id) {
        log.info("【开始执行获取物流信息的方法，接收参数】---------------------:运单号：{}", trackingNumber);
        LogRecordContext.putVariable("trackingNumber", trackingNumber);
        if (trackingNumber == null || trackingNumber.isEmpty()) {
            return Collections.emptyList();
        }
        String key = trackingNumber + ":logistics"; // 缓存键
        ListOperations<String, Object> listOperations = redisTemplate.opsForList();
        try {
            List<ExpressTrackingData> orderTrack;
            // 检查缓存键是否存在
            Boolean hasKey = redisTemplate.hasKey(key);
            if (hasKey) {
                orderTrack = (List<ExpressTrackingData>) (List<?>) listOperations.range(key, 0, -1); // 从缓存中获取数据
                if (orderTrack != null && !orderTrack.isEmpty()) {
                    return orderTrack;
                }
            }

            // 缓存为空，重新获取物流信息数据
            synchronized (this) { // 防止缓存击穿
                hasKey = redisTemplate.hasKey(key);
                if (hasKey) {
                    orderTrack = (List<ExpressTrackingData>) (List<?>) listOperations.range(key, 0, -1);
                    if (orderTrack != null && !orderTrack.isEmpty()) {
                        return orderTrack;
                    }
                }
                // 如果缓存中没有数据，重新获取
                orderTrack = fetchDataFromSource(trackingNumber);
                LogRecordContext.putVariable("orderTrack", orderTrack);
                log.info("重新获取物流信息数据: {}", orderTrack);
                if (orderTrack != null && !orderTrack.isEmpty()) {
                    // 将数据存入缓存
                    listOperations.leftPushAll(key, orderTrack);
                    // 设置缓存过期时间
                    redisTemplate.expire(key, 2, TimeUnit.HOURS);
                }
            }

            return orderTrack != null ? orderTrack : Collections.emptyList();
        } catch (Exception e) {
            log.error("获取物流信息失败，异常信息: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<AddressRespVO> contentParse(AddressListRequest content) {
        log.info("【开始执行地址解析的方法，接收参数为】：{}", content);
        // 调用智能解析接口
        CommonResult<List<AddressListResponse>> listCommonResult;
        try {
            listCommonResult = expressApi.listAddressBatch(content);
        } catch (Exception e) {
            throw exception(ErrorCodeConstants.ADDRESS_PARSE_EMPTY, e.getMessage());
        }
        // 检查解析结果
        if (listCommonResult == null || listCommonResult.getData() == null) {
            log.info("【调用运力解析接口返回结果为空】：{},【错误信息】", listCommonResult, listCommonResult.getMsg());
            return new ArrayList<>();
        }
        List<AddressListResponse> list = listCommonResult.getData();
        List<AddressParseVO> addressParseVOS = BeanUtils.toBean(list, AddressParseVO.class);

        List<AddressRespVO> addressRespVOList = new ArrayList<>();
        for (AddressParseVO addressParseVO : addressParseVOS) {
            AddressRespVO addressRespVO = new AddressRespVO();
            addressRespVO.setAddress(addressParseVO.getDetail_address());
            addressRespVO.setPhone(addressParseVO.getPhone());
            addressRespVO.setName(addressParseVO.getName());
            addressRespVO.setProvinceCode(addressParseVO.getProvinceCode());
            addressRespVO.setCityCode(addressParseVO.getCityCode());
            addressRespVO.setDistrictCode(addressParseVO.getCountyCode());
            addressRespVO.setCityName(addressParseVO.getCity());
            addressRespVO.setDistrictName(addressParseVO.getCounty());
            addressRespVO.setProvinceName(addressParseVO.getProvince());
            addressRespVOList.add(addressRespVO);
        }
        return addressRespVOList;
    }

    @Override
    @LogRecord(type = ExpressLogRecordConstants.COPE_PASTE_ADDRESS_RESOLUTION,
            fail = "地址解析失败，失败原因：{{#_errorMsg}}",
            success = "接收参数{{#content}}，解析后的结果{{#addressParse}}",
            bizNo = "{{#userId}}")
    public AddressResponse manualParseAddress(AddressListRequest content) {
        log.info("【开始执行用户手动进行复制粘贴多个地址的方法，请求参数为】：{}", content);
        LogRecordContext.putVariable("userId", SecurityFrameworkUtils.getLoginUserId());
        //调用解析接口
        List<AddressRespVO> addressParseVOS = contentParse(content);
        log.info("【手动解析-调用解析接口返回数据】-----------------：{}", addressParseVOS);
        LogRecordContext.putVariable("addressParse", addressParseVOS);
        // 如果 addressParseVOS 为空，直接返回失败状态
        if (addressParseVOS == null || addressParseVOS.isEmpty()) {
            AddressResponse response = new AddressResponse();
            response.setIsSuccess("0");
            return response;
        }
        String address = content.getAddress();
        AddressResponse addressResponse = new AddressResponse();
        addressResponse.setParsedAddresses(addressParseVOS);
        if (addressParseVOS.size() >= 2) {
            //判断解析后的省市区地址返回数据是否为空
            for (AddressRespVO addressParseVO : addressParseVOS) {
                if (addressParseVO.getProvinceName() == null ||
                        addressParseVO.getProvinceName().isEmpty() ||
                        addressParseVO.getCityName() == null ||
                        addressParseVO.getCityName().isEmpty() ||
                        addressParseVO.getDistrictName() == null ||
                        addressParseVO.getDistrictName().isEmpty()) {

                    return addressResponse.setIsSuccess("0");
                }
                // 插入手动解析的批量记录
                BatchOrderDO batchOrderDO = new BatchOrderDO();
                batchOrderDO.setBatchNumber(BatchOrderNumberUtil.generate());
                batchOrderDO.setType(ImportTypeEnum.MANUAL.getValue());
                batchOrderDO.setData(address);
                batchOrderDO.setPersonalId(SecurityFrameworkUtils.getLoginUserId());

                batchOrderMapper.insert(batchOrderDO);
                addressResponse.setBatchId(batchOrderDO.getId());
            }
        }
        log.info("【用户手动进行复制粘贴多个地址-调用解析接口返回数据】-----------------：{}", addressResponse);
        return addressResponse;
    }

    @Override
    @LogRecord(type = ExpressLogRecordConstants.EXCEL_ADDRESS_PARSING,
            fail = "Excel地址解析失败，失败原因：{{#_errorMsg}}",
            success = "接收参数{{#file}}，解析后的结果:{{#addressRespVOList}},如果是批量导入批次编号为:{{#batchOrderId}}," +
                    "异步上传文件到SSO服务器,返回url{{#url}}",
            bizNo = "{{#file}}")
    @Transactional(rollbackFor = Exception.class)
    public AddressResponse processExcelAndSyncToSSO(MultipartFile file) {
        log.info("【开始处理Excel文件并同步到SSO服务器的方法】-----------------:{}", file);
        LogRecordContext.putVariable("file", file);
        if (file == null || file.isEmpty() || ObjectUtils.isEmpty(file.getOriginalFilename())) {
            throw exception(ErrorCodeConstants.EXCEL_UPLOAD_ERROR);
        }
        //解析Excel
        List<AddressImportReqVO> importList;
        try {
            importList = ExcelUtils.read(file, AddressImportReqVO.class);
        } catch (IOException e) {
            throw exception(ErrorCodeConstants.READ_EXCEL_ERROR, e.getMessage());
        }
        //拼接字符串
        StringBuilder str = new StringBuilder();
        for (AddressImportReqVO address : importList) {
            str.append(address.getName()).append(address.getPhone()).append(address.getAddress()).append('\n');
        }
        //调用解析地址的接口
        AddressListRequest addressListRequest = new AddressListRequest();
        addressListRequest.setAddress(String.valueOf(str));
        List<AddressRespVO> addressRespVOList = contentParse(addressListRequest);
        LogRecordContext.putVariable("addressRespVOList", addressRespVOList);
        BatchOrderDO batchOrderDO = BatchOrderDO.builder()
                .batchNumber(BatchOrderNumberUtil.generate())
                .personalId(SecurityFrameworkUtils.getLoginUserId())
                .type(ImportTypeEnum.EXCEL.getValue())
                .build();
        batchOrderMapper.insert(batchOrderDO);
        Long batchOrderId = batchOrderDO.getId();
        log.info("【批量订单id】:--------------------{}", batchOrderDO.getId());
        LogRecordContext.putVariable("batchOrderId", batchOrderId);
        //上传excel文件返回url
        // 异步上传Excel文件，并在上传完成后通过回调更新URL
        CompletableFuture<String> uploadFuture = asyncUploadFile(file);
        uploadFuture.thenAccept(url -> {
            log.info("异步上传文件成功，URL: {}", url);
            LogRecordContext.putVariable("url", url);
            // 更新批量订单，根据batchOrderId更新订单data
            try {
                batchOrderMapper.updateDataById(url, Math.toIntExact(batchOrderId));
            } catch (Exception e) {
                log.error("更新批量订单记录失败，订单ID: {}, 异常信息: {}", batchOrderId, e.getMessage(), e);
            }
            log.info("异步上传完成后更新URL, url: {}", url);
        }).exceptionally(ex -> {
            log.error("异步上传文件失败，异常信息: {}", ex.getMessage(), ex);
            return null;
        });
        AddressResponse addressResponse = new AddressResponse();
        addressResponse.setParsedAddresses(addressRespVOList);
        addressResponse.setBatchId(batchOrderId);
        log.info("【处理Excel文件并同步到SSO服务器的方法结束，响应信息】-----------------:{}", addressResponse);
        return addressResponse;
    }

    @Override
    public ExpressListResponseVO getFastTrackLogistics(ExpressListRequestVO request) {
        request.setName("");
        ExpressListRequest expressListRequest = BeanUtils.toBean(request, ExpressListRequest.class);
        //调用快递公司接口
        CommonResult<ExpressListResponse> expressList;
        try {
            expressList = expressApi.listExpress(expressListRequest);
        } catch (Exception e) {
            throw exception(ErrorCodeConstants.CALLING_INTERFACE_ERROR, e.getMessage());
        }
        return BeanUtils.toBean(expressList, ExpressListResponseVO.class);
    }

    //TODO 暂时没有用,可以删除.
    @Override
    public void trackCallback(TrackCallBackVO trackCallBackVO) {
        if (ObjectUtils.isEmpty(trackCallBackVO)) {
            throw exception(ErrorCodeConstants.PARAMETER_IS_EMPTY);
        }
        //根据快递单号进行查询
        if (orderMapper.selectOrderByTrackingNumber(trackCallBackVO.getExpressSn())) {
            throw exception(ErrorCodeConstants.ORDER_NOT_EXISTS);
        }
    }

    @Override
    public CancelOrderResponseVO cancelOrderCallBack(CancelOrderCallBackVO cancelOrderCallBackVO) {
        System.out.println("cancelOrderCallBackVO" + cancelOrderCallBackVO);
        // 判断回调请求是否为空
        if (cancelOrderCallBackVO == null || isEmpty(cancelOrderCallBackVO.toString())) {
            throw exception(ErrorCodeConstants.PARAMETER_IS_EMPTY);
        }
        return null;
    }


    // ----------------- 私有辅助方法 -----------------

    private Map<Long, AddressRespApiVO> getAddressMap(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return new HashMap<>();
        }
        List<AddressRespApiVO> address = addressQueryApi.getAddress(ids);
        if (address == null || address.isEmpty()) {
            return new HashMap<>();
        }
        // 返回一个新的map, 使用address的id作为key,将address对象作为value
        return address.stream()
                .collect(Collectors.toMap(AddressRespApiVO::getId, Function.identity()));
    }

    private String determineDayType(String appointmentTime) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-M-d");
        if (appointmentTime.contains("今天")) return "今天";
        if (appointmentTime.contains("明天")) return "明天";
        if (appointmentTime.contains("后天")) return "后天";
        return appointmentTime;
    }

    private List<ExpressTrackingData> fetchDataFromSource(String trackingNumber) {
        log.info("【开始调用物流接口获取物流信息】---------------------:运单号：{}", trackingNumber);
        OrderTrackRequest orderTrackRequest = new OrderTrackRequest();
        orderTrackRequest.setExpressSn(trackingNumber);
        CommonResult<OrderTrackResponse> expressTrack;
        try {
            expressTrack = expressApi.queryExpressTrack(orderTrackRequest);
            if (expressTrack.getCode().equals(OrderConstants.CODE)) {
                log.info("【调用物流接口返回物流信息】：" + expressTrack.getData());
            }
        } catch (Exception e) {
            throw exception(ErrorCodeConstants.CALLING_INTERFACE_ERROR, e.getMessage());
        }
        if (expressTrack.getData() == null) {
            return Collections.emptyList();
        }

        ArrayList<Object> trackingObjects = new ArrayList<>();
        trackingObjects.add(expressTrack.getData());
        Object object = trackingObjects.get(0);
        List<ExpressTrackingData> data = (List<ExpressTrackingData>) object;
        log.info("【执行调用物流接口获取物流信息结束】---------------------:运单号：{}", trackingNumber);
        return data;
    }


    private String getLatestStatus(List<ExpressTrackingData> orderTrack) {
        //定义最新一条的数据
        ExpressTrackingData latestRecord = null;
        LocalDateTime latestTime = null;

        for (ExpressTrackingData record : orderTrack) {
            LocalDateTime currentTime = LocalDateTime.parse(record.getCreateTime());
            if (latestTime == null || currentTime.isAfter(latestTime)) {
                latestTime = currentTime;
                latestRecord = record;
            }
        }
        return latestRecord != null ? latestRecord.getStatus() : "";
    }

    private CompletableFuture<String> asyncUploadFile(MultipartFile file) {
        try {
            File excel = FileUtils.createTempFile(file.getBytes());
            UploadExcelVO uploadExcelVO = new UploadExcelVO();
            uploadExcelVO.setApplicationId(5);
            uploadExcelVO.setFile(excel);
            UploadImageRequest uploadFile = BeanUtils.toBean(uploadExcelVO, UploadImageRequest.class);
            // 使用自定义线程池执行异步任务
            return CompletableFuture.supplyAsync(() -> {
                log.info("开始上传文件");
                try {
                    CommonResult<List<UploadImageResponse>> result = imageApi.uploadImage(uploadFile);
                    log.info("文件上传成功");
                    if (result == null || result.getData() == null || result.getData().isEmpty()) {
                        log.error("文件上传失败，返回结果为空");
                        throw exception(ErrorCodeConstants.UPLOAD_ERROR);
                    }
                    log.info(result.getData().get(0).getImgUrl());
                    return result.getData().get(0).getImgUrl();
                } catch (Exception e) {
                    throw exception(ErrorCodeConstants.UPLOAD_ERROR, e.getMessage());
                }
            }, executor);

        } catch (IOException e) {
            throw exception(ErrorCodeConstants.FILE_TRANSFER_ERROR, e.getMessage());
        }
    }
} 