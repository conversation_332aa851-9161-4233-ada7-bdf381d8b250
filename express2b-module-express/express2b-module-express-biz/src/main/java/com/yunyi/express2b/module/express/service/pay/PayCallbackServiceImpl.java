package com.yunyi.express2b.module.express.service.pay;

import cn.hutool.extra.spring.SpringUtil;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.yunyi.express2b.framework.security.core.util.SecurityFrameworkUtils;
import com.yunyi.express2b.module.crm.api.card.cardstream.CardStreamApi;
import com.yunyi.express2b.module.crm.api.card.cardstream.vo.CardUserVo;
import com.yunyi.express2b.module.express.controller.admin.receivepayment.vo.PayCallBackRespVO;
import com.yunyi.express2b.module.express.dal.dataobject.order.OrderDO;
import com.yunyi.express2b.module.express.dal.dataobject.paymentorder.PaymentOrderDO;
import com.yunyi.express2b.module.express.dal.dataobject.receivepayment.PayCallback;
import com.yunyi.express2b.module.express.dal.mysql.order.OrderMapper;
import com.yunyi.express2b.module.express.dal.mysql.paymentorder.PaymentOrderMapper;
import com.yunyi.express2b.module.express.enums.OrderStatusTypeEnum;
import com.yunyi.express2b.module.express.enums.PaymentStatusTypeEnum;
import com.yunyi.express2b.module.express.enums.social.ExpressLogRecordConstants;
import com.yunyi.express2b.module.express.mq.producer.OrderStatusProducer;
import com.yunyi.express2b.module.express.service.paymentorder.PaymentOrderService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.module.express.constants.OrderStatusConstants.BAD_DEBT_ORDER_PREFIX;
import static com.yunyi.express2b.module.express.enums.ErrorCodeConstants.*;

/**
 * <AUTHOR>
 * @date 2025-03-27 11:49:24
 */
@Service
@Slf4j
public class PayCallbackServiceImpl implements PayCallbackService {
    @Resource
    private PaymentOrderService paymentorderService;

    @Resource
    private PaymentOrderMapper paymentOrderMapper;
    @Resource
    private CardStreamApi cardStreamApi;
    @Resource
    private StringRedisTemplate stringRedisTemplate;


    @Resource
    private OrderMapper orderMapper;

    /**
     * 支付回调
     * @param payCallback
     * @return
     */
    @Override
    @LogRecord(type = ExpressLogRecordConstants.PAY_CALL_BACK,
            fail = "支付回调失败，失败原因：【{{#_errorMsg}}】",
            success = "支付回调，支付单号:{{#payCallback.payOrderSn}}，接收参数:{{#payCallback}}",
            bizNo = "{{#userId}}")
    @Transactional
    public PayCallBackRespVO payCallback(PayCallback payCallback) {
        log.info("开始处理支付回调，支付单号：{},接收参数：{}", payCallback.getPayOrderSn(), payCallback);
        LogRecordContext.putVariable("payCallback", payCallback);
        LogRecordContext.putVariable("userId", SecurityFrameworkUtils.getLoginUserId());
        //校验交易编号合法性
        if (StringUtils.isEmpty(payCallback.getPayOrderSn())) {
            throw exception(TRANSACTIONNO_VERIFY_FAIL);
        }
        //查询持卡用户表
        CardUserVo cardUserVo = cardStreamApi.selectpayOrderSn(payCallback.getPayOrderSn());
        if (cardUserVo != null) {
            cardUserVo.setStatus(0);
            cardStreamApi.updatepayOrderSn(payCallback.getPayOrderSn());
            return new PayCallBackRespVO(1);
        }

        List<PaymentOrderDO> paymentOrderDOS = paymentorderService.selectPaymentOrdersByTransactionNo(payCallback.getPayOrderSn());
        log.info("查询本地支付单:{}", paymentOrderDOS);
        if (paymentOrderDOS == null) {
            throw exception(PAYMENT_ORDER_NOT_EXISTS);
        }
        for (PaymentOrderDO paymentOrderDO : paymentOrderDOS) {
            Integer amount = paymentOrderDO.getAmount();
            Long paymentOrderId = paymentOrderDO.getId();

            //校验金额合法性
            if (!amount.equals(payCallback.getAmount())) {
                throw exception(PAYMENT_AMOUNT_VERIFY_FAIL);
            }
            //组装参数
            PaymentOrderDO paymentOrder = PaymentOrderDO.builder()
                    .id(paymentOrderId)
                    .paymentNumber(payCallback.getThirdOrderSn())
                    .fee(payCallback.getFee())
                    .outTransactionNo(payCallback.getWxTransactionId())
                    .outOutTransactionNo(payCallback.getWxTradeNo())
                    .lastStatusUpdateTime(LocalDateTime.now())
                    // 设置状态为支付成功
                    .status(PaymentStatusTypeEnum.SUCCESS.getStatus())
                    .build();
            //更新状态和订单信息
            paymentOrderMapper.updateById(paymentOrder);
            //根据订单id更新订单状态
            OrderDO orderDO = orderMapper.selectById(paymentOrderDO.getOrderId());
            if (orderDO == null) {
                log.warn("支付回调处理失败，无法找到支付单 {} 对应的订单，订单ID: {}", paymentOrder.getId(), paymentOrderDO.getOrderId());
                continue;
            }

            orderDO.setStatus(OrderStatusTypeEnum.PAID.getStatus());
            orderMapper.updateById(orderDO);
            log.info("更新订单状态:{}", orderDO);

            // 【新增逻辑】清理坏账订单标记
            try {
                String cacheKey = BAD_DEBT_ORDER_PREFIX + orderDO.getOrderNo();
                Boolean deleted = stringRedisTemplate.delete(cacheKey);
                if (deleted) {
                    log.info("订单 {} 已支付，其对应的坏账标记已成功移除。", orderDO.getOrderNo());
                }
            } catch (Exception e) {
                // 如果 Redis 操作失败，只需记录错误日志，不应向上抛出异常，
                // 避免影响正常的支付成功流程。
                log.error("尝试为已支付订单 {} 移除坏账标记时发生异常，但这不影响支付结果。", orderDO.getOrderNo(), e);
            }
            
            //获取消息生产者
            OrderStatusProducer orderStatusProducer = SpringUtil.getBean(OrderStatusProducer.class);
            orderStatusProducer.sendRoleRefreshMessage(
                    Math.toIntExact(orderDO.getId()),
                    orderDO.getOrderNo(),
                    OrderStatusTypeEnum.CREATED.getStatus(),
                    OrderStatusTypeEnum.PAID.getStatus(), null);


        }
        return new PayCallBackRespVO(1);
    }
}
