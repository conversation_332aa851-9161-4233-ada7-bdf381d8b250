package com.yunyi.express2b.module.express.service.price;

import cn.hutool.core.bean.BeanUtil;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.security.core.LoginUser;
import com.yunyi.express2b.framework.security.core.util.SecurityFrameworkUtils;
import com.yunyi.express2b.module.express.controller.app.v1.price.vo.*;
import com.yunyi.express2b.module.express.dal.dataobject.locations.LocationsDO;
import com.yunyi.express2b.module.express.dal.dataobject.pricing.PricingDO;
import com.yunyi.express2b.module.express.dal.mysql.pricing.PricingMapper;
import com.yunyi.express2b.module.express.service.countpaymoney.IBaseCount;
import com.yunyi.express2b.module.express.service.countpaymoney.decorator.LadderCountDecorator;
import com.yunyi.express2b.module.express.service.countpaymoney.decorator.SubCardCountDecorator;
import com.yunyi.express2b.module.express.service.countpaymoney.dto.OrderDetailDTO;
import com.yunyi.express2b.module.express.service.countpaymoney.factory.CountDecoratorFactory;
import com.yunyi.express2b.module.express.service.locations.LocationsService;
import com.yunyi.express2b.module.express.service.order.OrderLifecycleService;
import com.yunyi.express2b.module.express.service.order.OrderQueryService;
import com.yunyi.express2b.module.express.utils.ProvinceUtils;
import com.yunyi.framework.api.base.enums.CommonCodeConstants;
import com.yunyi.framework.api.logistics.api.express.ExpressApi;
import com.yunyi.framework.api.logistics.api.express.vo.ExpressListRequest;
import com.yunyi.framework.api.logistics.api.express.vo.ExpressListResponse;
import com.yunyi.framework.api.logistics.api.express.vo.QueryExpressPriceRequest;
import com.yunyi.framework.api.logistics.api.express.vo.QueryExpressPriceResponse;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.yunyi.express2b.framework.common.exception.enums.GlobalErrorCodeConstants.UNAUTHORIZED;
import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.module.express.enums.ErrorCodeConstants.EXPRESS_PRICE_QUERY_FAIL;

/**
 * 价格管理service实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/4/2 下午4:44
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PriceServiceImpl implements PriceService {
    // 2kg
    private final Integer TWO_KG = 2000;
    // 省份parent编码
    private final String PARENT_CODE_OF_PROVINCE = "100000";

    private final ExpressApi expressApi;

    private final CountDecoratorFactory countDecoratorFactory;

    private final IBaseCount baseCount;

    private final OrderQueryService orderQueryService;
    private final LocationsService locationsService;

    private final PricingMapper pricingMapper;

    @Autowired
    @Qualifier("myTaskExecutor")
    private Executor taskExecutor;

    /**
     * 价格查询
     * @param priceQueryReqVO
     * @return
     */
    @Override
    public List<PriceQueryRespVO> queryExpressPrice(PriceQueryReqVO priceQueryReqVO) {
        log.info("【价格查询请求参数】：{}", priceQueryReqVO);
        log.info("priceQueryReqVO：{}", priceQueryReqVO);
        //获取用户信息
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (loginUser == null) {
            throw exception(UNAUTHORIZED);
        }
        Long personId = loginUser.getId();
        //获取发单量
        Integer count = Math.toIntExact(orderQueryService.todayCountOrdersCount());
        ;
        //存入今日发单量
        priceQueryReqVO.setTodayCount(count);

        if (priceQueryReqVO.getReceiverPlaces() != null
                && !priceQueryReqVO.getReceiverPlaces().isEmpty()) {
            //调用批量询价方法
            return batchQueryExpressPrice(priceQueryReqVO);

        }

        //价格查询
        return queryExpressPrice0(priceQueryReqVO);
    }

    /**
     * 价格批量查询
     * @param priceListReqVo
     * @return
     */
    @Override
    public List<PriceListRespVO> getPriceList(PriceListReqVO priceListReqVo) {
        //修正省名
        priceListReqVo.setSenderProvince(fixProvince(priceListReqVo.getSenderProvince()));

        PriceQueryReqVO priceQueryReq = BeanUtil.toBean(priceListReqVo, PriceQueryReqVO.class);
        priceQueryReq.setTodayCount(priceListReqVo.getMinQuantity());
        //不用次卡优惠
        priceQueryReq.setUseSubCard("0");
        //设置重量为2kg，查出首重（1kg）和续重标准价格
        priceQueryReq.setWeight(TWO_KG);
        //设置发单量(-1因为 计算阶梯优惠时会拿今日发单量+1)
        priceQueryReq.setTodayCount(priceListReqVo.getMinQuantity() - 1);

        //全部省的价格查询
        if (priceListReqVo.getReceiverProvince() == null) {
            return getAllProvincePriceList(priceQueryReq);
        }

        //有省份的价格查询
        List<PriceListRespVO> respList = new ArrayList<>();
        List<PriceQueryRespVO> priceQueryRespList = queryExpressPrice0(priceQueryReq);
        PriceQueryRespVO priceQueryRespVO = priceQueryRespList.get(0);

        PriceListRespVO priceListRespVo = PriceListRespVO.builder()
                .expressCode(priceListReqVo.getExpressCode())
                .firstPrice(priceQueryRespVO.getFirstPrice())
                .overPrice(priceQueryRespVO.getOverPrice())
                .receiverProvince(priceListReqVo.getReceiverProvince())
                .receiverCity(priceListReqVo.getReceiverCity())
                .ladderDiscountAmount(priceQueryRespVO.getLadderDiscountAmount())
                .build();
        //添加到集合中
        respList.add(priceListRespVo);

        return respList;
    }

    /**
     * 获取省价格列表
     * @param brandKey
     * @return
     */
    @Override
    public List<VolumeTierRespVo> getVolumeTier(String brandKey) {
        //查询最大最小单量
        List<PricingDO> pricingList = pricingMapper.selectQuantityByBrandKey(brandKey);

        //组装响应
        List<VolumeTierRespVo> respList = new ArrayList<>();
        for (PricingDO pricing : pricingList) {
            VolumeTierRespVo resp = new VolumeTierRespVo(pricing.getMinQuantity(), pricing.getMaxQuantity());
            respList.add(resp);
        }

        return respList;
    }


    /**
     * 批量询价方法
     *
     * @param priceQueryReqVO
     * @return
     */
    public List<PriceQueryRespVO> batchQueryExpressPrice(PriceQueryReqVO priceQueryReqVO) {
        List<PriceQueryReqVO> requests = new ArrayList<>();

        //创建请求集合
        for (ReceiverPlace place : priceQueryReqVO.getReceiverPlaces()) {
            PriceQueryReqVO req = BeanUtil.toBean(priceQueryReqVO, PriceQueryReqVO.class);
            place.setSenderAddress(priceQueryReqVO.getSenderAddress());
            place.setSenderDistrict(priceQueryReqVO.getSenderDistrict());
            BeanUtil.copyProperties(place, req);
            requests.add(req);
        }

        // 创建Future集合保存异步任务
        List<CompletableFuture<List<PriceQueryRespVO>>> futures = new ArrayList<>(requests.size());

        // 提交异步任务
        for (PriceQueryReqVO req : requests) {
            CompletableFuture<List<PriceQueryRespVO>> future = CompletableFuture.supplyAsync(
                    () -> queryExpressPrice0(req),
                    taskExecutor
            );
            futures.add(future);
        }

        // 合并结果
        List<PriceQueryRespVO> results = new ArrayList<>();
        for (CompletableFuture<List<PriceQueryRespVO>> future : futures) {
            try {
                //todo-张洋 处理结果
                // 获取异步任务的结果
                List<PriceQueryRespVO> respList = future.get();
                // 将结果添加到最终结果列表中
                results.addAll(respList);

            } catch (Exception e) {
                throw exception(EXPRESS_PRICE_QUERY_FAIL);
            }
        }
        //按照快递公司编码筛选 把所有相同公司的快递编码 其他字段值 相加
        // 按 expressCode 分组并合并字段值
        Map<String, PriceQueryRespVO> groupedResults = results.stream()
                // 过滤掉 null 对象
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(PriceQueryRespVO::getExpressCode))
                .entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> {
                            List<PriceQueryRespVO> list = entry.getValue();
                            PriceQueryRespVO merged = new PriceQueryRespVO();
                            // 假设需要合并的字段，根据实际情况修改
                            merged.setExpressCode(list.get(0).getExpressCode());
                            merged.setProductCode(list.get(0).getProductCode());
                            merged.setLogoUrl(list.get(0).getLogoUrl());
                            merged.setName(list.get(0).getName());
                            merged.setReceiverCity(list.get(0).getReceiverCity());
                            merged.setReceiverProvince(list.get(0).getReceiverProvince());
                            // 合并字段值
                            merged.setRetailAmount(list.stream().mapToInt(PriceQueryRespVO::getRetailAmount).sum());
                            merged.setFirstPrice(list.stream().mapToInt(PriceQueryRespVO::getFirstPrice).sum());
                            merged.setOverPrice(list.stream().mapToInt(PriceQueryRespVO::getOverPrice).sum());
                            merged.setPayMoney(list.stream().mapToInt(PriceQueryRespVO::getPayMoney).sum());
                            merged.setLadderDiscountAmount(list.stream().mapToInt(PriceQueryRespVO::getLadderDiscountAmount).sum());
                            merged.setDiscountAmount(list.stream().filter(Objects::nonNull).mapToInt(PriceQueryRespVO::getDiscountAmount).sum());
                            merged.setRoutePrice(list.stream()
                                    .filter(Objects::nonNull)
                                    .mapToInt(PriceQueryRespVO::getRoutePrice)
                                    .sum());

                            merged.setProfit(list.stream()
                                    .filter(Objects::nonNull)
                                    .mapToInt(PriceQueryRespVO::getProfit)
                                    .sum());
                            return merged;
                        },
                        // 合并函数，处理重复键
                        (existing, replacement) -> existing
                ));
        // 将 Map 转换为 List
        List<PriceQueryRespVO> finalResults = new ArrayList<>(groupedResults.values());
        // 按照 payMoney 从小到大排序
        finalResults = finalResults.stream()
                .sorted(Comparator.comparingInt(PriceQueryRespVO::getPayMoney))
                .collect(Collectors.toList());
        return finalResults;
    }


    public List<PriceQueryRespVO> queryExpressPrice0(PriceQueryReqVO priceQueryReqVO) {
        //数据转换
        QueryExpressPriceRequest request = BeanUtil.toBean(priceQueryReqVO, QueryExpressPriceRequest.class);
        request.setSenderProvince(fixProvince(request.getSenderProvince()));
        request.setSenderCity(fixProvince(request.getSenderCity()));
        request.setReceiverProvince(fixProvince(request.getReceiverProvince()));
        request.setReceiverCity(fixProvince(request.getReceiverCity()));
        request.setSenderAddress(request.getSenderAddress());
        request.setSenderDistrict(request.getSenderDistrict());
        request.setReceiverAddress(request.getReceiverAddress());
        request.setReceiverDistrict(request.getReceiverDistrict());
        request.setWeight(request.getWeight());
        log.info("【开始调用运力询价，最终请求参数】-----------------：{}", request);
        List<QueryExpressPriceResponse> data = new ArrayList<>();

        if (Objects.equals(request.getExpressCode() , "")) {
            request.setExpressCode(null);
        }

        //调用快递询价接口,获取返回数据
        CommonResult<List<QueryExpressPriceResponse>> result = expressApi.queryExpressPrice(request);
        if (result.getCode().equals(CommonCodeConstants.SUCCESS)) {
            //请求不成功，返回空列表
            data = result.getData();
        }
        log.info("【调用运力询价成功】----------------：{}", result);
        //调用快递公司接口
        CommonResult<ExpressListResponse> expressList = null;
        ExpressListRequest expressListRequest = new ExpressListRequest();

        //计算金额（根据返回list大小）
        List<PriceQueryRespVO> resp = new ArrayList<>();
        Integer todayCount = priceQueryReqVO.getTodayCount();
        if (data == null) {
            //统一运力响应为空，不展示价格，仅展示公司和收件地
            resp.add(PriceQueryRespVO.builder()
                    .expressCode(priceQueryReqVO.getExpressCode())
                    .receiverProvince(priceQueryReqVO.getReceiverProvince())
                    .receiverCity(priceQueryReqVO.getReceiverCity()).build());
        } else {
            for (QueryExpressPriceResponse expressPriceItem : data) {
                //获取快递公司信息 传递数据 code
                expressListRequest.setCode(expressPriceItem.getExpressCode());
                expressList = expressApi.listExpress(expressListRequest);
                log.info("【查询快递公司信息】-----------------：{}", expressList);
                //获取快递公司名称
                String resultString = expressList.toString();
                // 编译用于匹配名称的正则表达式
                Pattern namepattern = Pattern.compile("name=([^,]+)");
                // 编译用于匹配logo的正则表达式
                Pattern logopattern = Pattern.compile("logo=([^,]+)");
                // 创建匹配名称的Matcher对象
                Matcher namematcher = namepattern.matcher(resultString);
                // 创建匹配logo的Matcher对象
                Matcher logomatcher = logopattern.matcher(resultString);
                String name = "";
                String logn = "";
                // 如果名称匹配器找到了匹配项，则执行以下代码块
                if (namematcher.find()) {
                    name = namematcher.group(1);
                }
                if (logomatcher.find()) {
                    logn = logomatcher.group(1);
                }
                //组装orderDetail
                OrderDetailDTO orderDetail = getOrderDetail(todayCount, expressPriceItem);
                //计算金额
                Integer payMoney = countAmount(priceQueryReqVO, orderDetail);
                priceQueryReqVO.setExpressCode(expressPriceItem.getExpressCode());
                priceQueryReqVO.setName(name);
                priceQueryReqVO.setLogoUrl(logn);
                priceQueryReqVO.setProductCode(expressPriceItem.getProductCode());
                resp.add(buildPriceQueryRespVO(orderDetail, payMoney, priceQueryReqVO));

            }
        }

        return resp;
    }

    /**
     * 计算金额
     */
    public Integer countAmount(PriceQueryReqVO priceQueryReqVO, OrderDetailDTO orderDetail) {
        //计算价格
        Boolean isSameProvince = priceQueryReqVO.getSenderProvince().equals(priceQueryReqVO.getReceiverProvince());
        LadderCountDecorator ladderDecorator = null;
        if ("1".equals(priceQueryReqVO.getUseSubCard())) {
            //使用次卡
            SubCardCountDecorator subCardDecorator = countDecoratorFactory.createSubCardDecorator(baseCount);
            ladderDecorator = countDecoratorFactory.createLadderDecorator(subCardDecorator);
        } else {
            //不使用次卡
            ladderDecorator = countDecoratorFactory.createLadderDecorator(baseCount);
        }
        //返回支付金额
        return ladderDecorator.countCouponPayMoneyNoId(isSameProvince, priceQueryReqVO.getExpressCode(), orderDetail);
    }

    /**
     * 组装OrderDetailDTO
     */
    public OrderDetailDTO getOrderDetail(Integer todayCount, QueryExpressPriceResponse expressPriceItem) {
        //组装OrderDetailDTO
        OrderDetailDTO orderDetail = new OrderDetailDTO();
        //今日发单量
        orderDetail.setTodayCount(todayCount);
        orderDetail.setCostAmount(expressPriceItem.getPrice());
        orderDetail.setFirstPrice(expressPriceItem.getFirstPrice());
        orderDetail.setOverPrice(expressPriceItem.getOverPrice());
        orderDetail.setWeight(expressPriceItem.getWeight().toString());
        orderDetail.setExpressCode(expressPriceItem.getExpressCode());

        orderDetail.setProfit(expressPriceItem.getProfit());
        orderDetail.setRoutePrice(expressPriceItem.getRoutePrice());

        return orderDetail;
    }

    /**
     * 组装PriceQueryRespVO
     */
    public PriceQueryRespVO buildPriceQueryRespVO(OrderDetailDTO orderDetail, Integer payMoney, PriceQueryReqVO priceQueryReqVO) {
        //组装响应
        PriceQueryRespVO respVO = PriceQueryRespVO
                .builder()
                .retailAmount(orderDetail.getRetailAmount() * 100)
                .firstPrice(orderDetail.getFirstPrice())
                .overPrice(orderDetail.getOverPrice())
                .ladderDiscountAmount(orderDetail.getLadderDiscountAmount())
                .membersLevelDiscountAmount(orderDetail.getMembersLevelDiscountAmount())
                .subCardDiscountAmount(orderDetail.getSubCardDiscountAmount())
                .discountAmount(orderDetail.getRetailAmount() * 100 - payMoney)
                .payMoney(payMoney)
                .expressCode(priceQueryReqVO.getExpressCode())
                .receiverProvince(priceQueryReqVO.getReceiverProvince())
                .receiverCity(priceQueryReqVO.getReceiverCity())
                .name(priceQueryReqVO.getName())
                .logoUrl(priceQueryReqVO.getLogoUrl())
                .productCode(priceQueryReqVO.getProductCode())
                .costPrice(orderDetail.getCostPrice())
                .routePrice(orderDetail.getRoutePrice())
                .profit(orderDetail.getProfit())
                .build();
        return respVO;
    }

    /**
     * 修正省名
     * （如"湖南省" -> "湖南"）
     *
     * @param provinceName 原始省名（如"湖南省"）
     * @return 去除尾缀后的名称（如"湖南"），若不含"省"则返回原值
     */
    public String fixProvince(String provinceName) {
        if (provinceName == null || provinceName.isEmpty()) {
            return provinceName;
        }

        // 处理以"省"结尾的情况
        if (provinceName.endsWith("省")) {
            return provinceName.substring(0, provinceName.length() - 1);
        }

        // 处理特殊直辖市（如"北京市" -> "北京"）
        if (provinceName.endsWith("市")) {
            return provinceName.substring(0, provinceName.length() - 1);
        }

        // 处理自治区（如"广西壮族自治区" -> "广西"）
        if (provinceName.endsWith("自治区")) {
            return provinceName.substring(0, 2);
        }

        // 需要返回空字符串的特殊地区（完整官方名称）
        List<String> specialRegions = Arrays.asList(
                "香港特别行政区",
                "澳门特别行政区",
                "台湾省"
        );

        // 港澳台地区直接返回空
        if (specialRegions.contains(provinceName)) {
            return null;
        }

        // 其他情况返回原值
        return provinceName;
    }

    /**
     * 全省份的价格表查询
     */
    public List<PriceListRespVO> getAllProvincePriceList(PriceQueryReqVO priceQueryReq) {
        List<PriceListRespVO> respList = new ArrayList<>();

        //创建请求集合
        List<PriceQueryReqVO> requests = new ArrayList<>();
        List<LocationsDO> locations = locationsService.getLocations(PARENT_CODE_OF_PROVINCE);
        for (LocationsDO locationsDO : locations) {
            //修正省份名称
            String receiverProvince = fixProvince(locationsDO.getName());
            //省份名称为空，跳过
            if (receiverProvince == null) {
                continue;
            }

            //获取城市名称
            String receiverCity = ProvinceUtils.getCapitalCity(receiverProvince);

            // 创建新对象，并拷贝模板属性
            PriceQueryReqVO newReq = new PriceQueryReqVO();
            BeanUtil.copyProperties(priceQueryReq, newReq);

            //更新参数
            newReq.setReceiverProvince(receiverProvince);
            newReq.setReceiverCity(receiverCity);

            requests.add(newReq);
        }

        // 创建Future集合保存异步任务
        List<CompletableFuture<List<PriceQueryRespVO>>> futures = new ArrayList<>();

        // 提交异步任务
        for (PriceQueryReqVO req : requests) {
            CompletableFuture<List<PriceQueryRespVO>> future = CompletableFuture.supplyAsync(
                    () -> queryExpressPrice0(req),
                    taskExecutor
            );
            futures.add(future);
        }

        // 合并结果
        for (CompletableFuture<List<PriceQueryRespVO>> future : futures) {
            try {
                //处理结果
                PriceQueryRespVO priceQueryRespVO = future.get().get(0);
                PriceListRespVO priceListRespVo = PriceListRespVO.builder()
                        .expressCode(priceQueryRespVO.getExpressCode())
                        .firstPrice(priceQueryRespVO.getFirstPrice())
                        .overPrice(priceQueryRespVO.getOverPrice())
                        .receiverProvince(priceQueryRespVO.getReceiverProvince())
                        .receiverCity(priceQueryRespVO.getReceiverCity())
                        .ladderDiscountAmount(priceQueryRespVO.getLadderDiscountAmount())
                        .build();
                //添加到集合中
                respList.add(priceListRespVo);
            } catch (Exception e) {
                throw exception(EXPRESS_PRICE_QUERY_FAIL);
            }
        }
        return respList;
    }
}
