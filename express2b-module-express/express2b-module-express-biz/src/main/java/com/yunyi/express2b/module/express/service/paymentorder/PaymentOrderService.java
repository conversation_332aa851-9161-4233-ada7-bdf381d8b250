package com.yunyi.express2b.module.express.service.paymentorder;

import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.module.express.controller.admin.paymentorder.vo.PaymentOrderPageQueryVO;
import com.yunyi.express2b.module.express.controller.admin.paymentorder.vo.PaymentOrderPageReqVO;
import com.yunyi.express2b.module.express.controller.admin.paymentorder.vo.PaymentOrderSaveReqVO;
import com.yunyi.express2b.module.express.dal.dataobject.paymentorder.PaymentOrderDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 支付订单 Service 接口
 *
 * <AUTHOR>
 */
public interface PaymentOrderService {

    /**
     * 创建支付订单
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPaymentOrder(@Valid PaymentOrderSaveReqVO createReqVO);

    /**
     * 更新支付订单
     *
     * @param updateReqVO 更新信息
     */
    void updatePaymentOrder(@Valid PaymentOrderSaveReqVO updateReqVO);

    /**
     * 删除支付订单
     *
     * @param id 编号
     */
    void deletePaymentOrder(Long id);

    /**
     * 获得支付订单
     *
     * @param id 编号
     * @return 支付订单
     */
    PaymentOrderDO getPaymentOrder(Long id);

    /**
     * 获得支付订单分页
     *
     * @param pageReqVO 分页查询
     * @return 支付订单分页
     */
    PageResult<PaymentOrderDO> getPaymentOrderPage(PaymentOrderPageReqVO pageReqVO);

    /**
     * 分页查询支付订单（包含快递号查询）
     * @param pageReqVO
     * @return
     */
    PageResult<PaymentOrderDO> getPaymentOrderPageIncludeTrackingNumber(PaymentOrderPageQueryVO pageReqVO);



    /**
     * 根据交易编号获得支付订单
     *
     * @param transactionNo
     * @return 支付订单
     */
    PaymentOrderDO getPaymentOrderByTransactionNo(String transactionNo);
    /**
     *更新订单状态
     *
     *
     * @param Status
     * @return
     */
    Integer updatePaymentOrderStatus(Long id, String Status);

    List<PaymentOrderDO> selectPaymentOrdersByTransactionNo(String payOrderSn);

    List<PaymentOrderDO> getPaymentOrders(Long paymentOrderId);

    /**
     * 查询超时订单
     */
    String selectTimeoutPaymentOrders();
}