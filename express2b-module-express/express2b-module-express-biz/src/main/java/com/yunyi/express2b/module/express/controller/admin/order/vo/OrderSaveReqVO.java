package com.yunyi.express2b.module.express.controller.admin.order.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yunyi.express2b.module.express.controller.app.v1.price.vo.ReceiverPlace;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.List;

/**
 * 向运力下单的Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 订单管理新增/修改 Request VO")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderSaveReqVO {
    @Schema(description = "订单ID（主键）", requiredMode = Schema.RequiredMode.REQUIRED, example = "20591")
    private Long id;

    @Schema(description = "订单编号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String orderNo;

    @Schema(description = "批量订单主键", example = "32377")
    private Long batchId;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "18936")
    private Long personalId;

    @Schema(description = "快递品牌ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "8521")
    @NotNull(message = "快递品牌ID不能为空")
    private Long brandId;

    @Schema(description = "阶梯价策略标志", requiredMode = Schema.RequiredMode.REQUIRED)
    private String pricingKey;

    @Schema(description = "分润策略标志")
    private String profitKey;

    @Schema(description = "订单状态（如 CREATED, PAID, COMPLETED）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String status;

    @Schema(description = "寄件人姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @NotEmpty(message = "寄件人姓名不能为空")
    private String senderName;

    @Schema(description = "寄件人电话", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "寄件人电话不能为空")
    private String senderPhone;

    @Schema(description = "寄件人地址", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "寄件人地址不能为空")
    private String senderAddress;

    @Schema(description = "寄件人地址ID", example = "5494")
    private Long senderAddressId;

    @Schema(description = "收件人姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    private String receiverName;

    @Schema(description = "收件人电话", requiredMode = Schema.RequiredMode.REQUIRED)
    private String receiverPhone;

    @Schema(description = "收件人地址", requiredMode = Schema.RequiredMode.REQUIRED)
    private String receiverAddress;

    @Schema(description = "收件人地址ID", example = "7106")
    private Long receiverAddressId;

    @Schema(description = "成本金额")
    private Integer costAmount;

    @Schema(description = "零售金额")
    private Integer retailAmount;

    @Schema(description = "实收金额|订单金额")
    private Integer receivedAmount;

    @Schema(description = "优惠总金额")
    private Integer discountAmount;

    @Schema(description = "代理商佣金")
    private Integer agentCommission;

    @Schema(description = "团长佣金")
    private Integer groupLeaderCommission;

    @Schema(description = "快递运单号")
    private String trackingNumber;

    @Schema(description = "物品名称", example = "李四")
    private String itemName;

    @Schema(description = "物品分类")
    private String itemCategory;

    @Schema(description = "物品数量")
    private Integer itemQuantity;

    @Schema(description = "物品重量（kg）")
    private Integer itemWeight;

    @Schema(description = "物品价值（元）")
    private Integer itemValue;

    @Schema(description = "包装详情")
    private String packagingDetails;

    @Schema(description = "是否保价", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean isInsured;

    @Schema(description = "保价金额（元）")
    private BigDecimal insuredAmount;



    @Schema(description = "收件员姓名", example = "赵六")
    private String receiverStaffName;

    @Schema(description = "支付类型", example = "1")
    private String payType;

    @Schema(description = "用户地址")
    private String memberIp;

    @Schema(description = "类型（手动:2，excel:1）")
    private Integer type;

    @Schema(description = "卡id")
    private String cardId;

    @Schema(description = "长")
    private Integer height;

    @Schema(description = "宽")
    private Integer width;

    @Schema(description = "高")
    private Integer length;

    @Schema(description = "预约取件起止时间")
    @JsonFormat(pattern = "HH:mm")
    private LocalTime scheduledStartTime;

    @Schema(description = "预约取件结束时间")
    @JsonFormat(pattern = "HH:mm")
    private LocalTime scheduledEndTime;

    @Schema(description = "统一运力订单的唯一标识")
    private String ucmOrderSn;

    @Schema(description = "取件的预约日期")
    private String appointmentTime;

    @Schema(description = "取件的预约开始和结束时间")
    private String scheduledTime;

    @Schema(description = "快递公司code，和统一运力对应（expressCode），对应brand表中brand_key")
    private String expressCode;

    @Schema(description = "产品类型")
    private String productCode;

    @Schema(description = "次卡编号")
    private String cardNumber;

    @Schema(description = "是否使用次卡 0否")
    private String useSubCard;

    @Schema(description = "快递员手机号")
    private String receiverStaffMobile;

    @Schema(description = "取件码")
    private String receiverStaffPickupCode;

    @Schema(description = "代理商ID")
    private Long agentId;

    @Schema(description = "询价返回的分润金额")
    private Integer shareAmount;

    @Schema(description = "开票状态")
    private String invoiceStatus;



    @Schema(description = "待补运费金额")
    private Integer prepaidAmount;


    private List<ReceiverPlace> receiverPlacesList;

    /**
     * 收件城市名
     * 必需
     */
    private String receiverCity;

    /**
     * 收件城市Code
     * 必需
     */
    private String receiverCityCode;

    /**
     * 收件区名
     * 必需
     */
    private String receiverDistrict;

    /**
     * 收件区Code
     * 必需
     */
    private String receiverDistrictCode;

    /**
     * 收件省名称
     * 必需
     */
    private String receiverProvince;

    /**
     * 收件省Code
     * 必需
     */
    private String receiverProvinceCode;

    /**
     * 发件区Code
     * 必需
     */
    private String senderDistrictCode;

    /**
     * 发件区名称
     * 必需
     */
    private String senderDistrict;
    /**
     * 发件省名称
     * 必需
     */
    private String senderProvince;

    /**
     * 发件省Code
     * 必需
     */
    private String senderProvinceCode;

    /**
     * 发件市Code
     * 必需
     */
    private Integer senderCityCode;

    /**
     * 发件市
     * 必需
     */
    private String senderCity;


}