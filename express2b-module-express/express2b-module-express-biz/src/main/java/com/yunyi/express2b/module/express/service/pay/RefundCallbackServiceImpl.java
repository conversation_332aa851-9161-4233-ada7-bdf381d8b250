package com.yunyi.express2b.module.express.service.pay;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.framework.security.core.util.SecurityFrameworkUtils;
import com.yunyi.express2b.module.agent.api.AgentApi;
import com.yunyi.express2b.module.agent.api.dto.AgentInfoDTO;
import com.yunyi.express2b.module.agent.api.dto.RelationShipDTO;
import com.yunyi.express2b.module.commission.api.CommissionApi;
import com.yunyi.express2b.module.commission.api.dto.DetailDTO;
import com.yunyi.express2b.module.commission.api.vo.DetailRequestVO;
import com.yunyi.express2b.module.commission.api.vo.DetailSaveReqVO;
import com.yunyi.express2b.module.crm.api.card.paycard.vo.PayCardVo;
import com.yunyi.express2b.module.crm.api.card.refundcard.RefundCardApi;
import com.yunyi.express2b.module.express.controller.admin.refundcallback.vo.RefundCallBackReqVO;
import com.yunyi.express2b.module.express.dal.dataobject.order.OrderDO;
import com.yunyi.express2b.module.express.dal.dataobject.paymentorder.PaymentOrderDO;
import com.yunyi.express2b.module.express.dal.dataobject.refundorder.RefundOrderDO;
import com.yunyi.express2b.module.express.dal.mysql.order.OrderMapper;
import com.yunyi.express2b.module.express.dal.mysql.paymentorder.PaymentOrderMapper;
import com.yunyi.express2b.module.express.dal.mysql.refundorder.RefundOrderMapper;
import com.yunyi.express2b.module.express.enums.ErrorCodeConstants;
import com.yunyi.express2b.module.express.enums.OrderStatusTypeEnum;
import com.yunyi.express2b.module.express.enums.RefundStatusTypeEnum;
import com.yunyi.express2b.module.express.enums.social.ExpressLogRecordConstants;
import com.yunyi.express2b.module.express.mq.producer.OrderStatusProducer;
import com.yunyi.express2b.module.express.utils.OrderConstants;
import com.yunyi.express2b.module.pricing.api.PricingUtilsApiService;
import com.yunyi.express2b.module.pricing.api.TemplateApi;
import com.yunyi.express2b.module.pricing.api.dto.DetailShareReqDTO;
import com.yunyi.express2b.module.pricing.api.dto.TemplateApiDTO;
import com.yunyi.express2b.module.pricing.api.vo.PricingRuleApiPageReqVO;
import com.yunyi.express2b.module.wallet.api.TransationsApi.TransactionsApi;
import com.yunyi.express2b.module.wallet.api.TransationsApi.dto.TransactionsDetailsDTO;
import com.yunyi.express2b.module.wallet.api.WalletApi.WalletApi;
import com.yunyi.express2b.module.wallet.api.WalletApi.dto.TransactionsSaveReqDO;
import com.yunyi.express2b.module.wallet.enums.WalletTransactionTypeEnum;
import com.yunyi.framework.api.login.api.discount.DiscountApi;
import com.yunyi.framework.api.login.utils.TransactionNoGenerator;
import com.yunyi.express2b.module.express.service.sendmessage.MessageUtils;
import com.yunyi.framework.api.login.utils.TransactionNoUtil;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.openxmlformats.schemas.spreadsheetml.x2006.main.impl.CTPrintOptionsImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yunyi.express2b.module.express.enums.ErrorCodeConstants.REFUND_AMOUNT_VERIFY_FAIL;
import static com.yunyi.express2b.module.express.enums.ErrorCodeConstants.TRANSACTIONNO_VERIFY_FAIL;
import static com.yunyi.express2b.module.wallet.enums.ErrorCodeConstants.WALLET_TRANSACTIONS_FAIL;

/**
 * 退款回调服务实现类
 *
 * <AUTHOR>
 * @date 2025-03-29 14:00:44
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RefundCallbackServiceImpl implements RefundCallbackService {

    private final RefundOrderMapper refundOrderMapper;

    @Resource
    private OrderMapper orderMapper;

    @Resource
    private RefundCardApi refundCardApi;
@Resource
    private MessageUtils messageUtils;
    @Resource
    private PricingUtilsApiService pricingUtilsApiService;

    @Resource
    private AgentApi agentApi;

    @Resource
    private CommissionApi commissionApi;

    @Resource
    private TransactionsApi transactionsApi;

    @Resource
    private WalletApi walletApi;
    @Resource
    private PaymentOrderMapper paymentOrderMapper;
/**
     * 退款回调
     * @param refundCallBackReqVo
     * @return
     */
    @LogRecord(type = ExpressLogRecordConstants.REFUND_CALL_BACK,
            fail = "退款回调失败，失败原因：【{{#_errorMsg}}】",
            success = "退款回调，退款单号:{{#refundCallBackReqVo.refundOrderSn}}，接收参数:{{#refundCallBackReqVo}}",
            bizNo = "{{#userId}}")
    @Override
    @Transactional(rollbackFor = Exception.class)
    public RefundCallBackReqVO refundBack(RefundCallBackReqVO refundCallBackReqVo) {
        log.info("开始处理退款回调，退款单号：{},接收参数：{}", refundCallBackReqVo.getRefundOrderSn(), refundCallBackReqVo);
        LogRecordContext.putVariable("refundCallBackReqVo", refundCallBackReqVo);
        LogRecordContext.putVariable("userId", SecurityFrameworkUtils.getLoginUserId());
        //校验退款单号和金额
        RefundOrderDO refundOrder = refundOrderMapper.selectOne(RefundOrderDO::getRefundOrderSn, refundCallBackReqVo.getRefundOrderSn());
        log.info("【根据退款单号查询退款单信息】{}", refundOrder);
        if (TransactionNoUtil.validate(refundCallBackReqVo.getRefundOrderSn())) {
            throw exception(TRANSACTIONNO_VERIFY_FAIL);
        }
        if (!refundCallBackReqVo.getAmount().equals(refundOrder.getAmount())) {
            throw exception(REFUND_AMOUNT_VERIFY_FAIL);
        }

        //更新退款单
        refundOrder.setRefundStatus(RefundStatusTypeEnum.REFUNDED.getCode());
        refundOrder.setRefundStatusUpdateTime(LocalDateTime.now());
        refundOrder.setRefundCompleteTime(LocalDateTime.now());
        //查询订单信息
        OrderDO orderDO1 = orderMapper.selectOrderByPaymentOrderId(refundOrder.getPaymentOrderId());
        if (orderDO1 == null) {
            log.error("未找到对应退款的订单信息，paymentOrderId: {}", refundOrder.getPaymentOrderId());
            throw exception(ErrorCodeConstants.ORDER_NOT_EXISTS);
        }
        //退还次卡
        log.info("【次卡编号】: {}", orderDO1.getCardNumber());
        if (orderDO1.getCardNumber() != null) {
            log.info("【准备退还次卡】");
            PayCardVo payCardVo = new PayCardVo();
            payCardVo.setOrderId(orderDO1.getId());
            payCardVo.setCardNumber(orderDO1.getCardNumber());
            refundCardApi.refundCard(payCardVo);
        }
        //先查询支付订单表成功支付了几次
        List<PaymentOrderDO> paymentOrderDOList = paymentOrderMapper.selectPaymentOrdersListByOrderId(orderDO1.getId());
        if (CollectionUtil.isEmpty(paymentOrderDOList)) {
            log.info("未找到对应支付订单信息，orderId: {},orderNo:{}", orderDO1.getId(), orderDO1.getOrderNo());
            throw exception(ErrorCodeConstants.PAYMENT_ORDER_NOT_EXISTS);
        }
        //如果仅支付一次，说明没有续重，或者只支付了首重，没有支付续重
        AgentInfoDTO agentInfoDTO = agentApi.getAgentByMemberId(orderDO1.getPersonalId());
        if (paymentOrderDOList.size() == 1) {
            log.info("【该订单仅支付一次】");
            //退还利润
            //获取上级代理商信息
            RelationShipDTO relationShipDTO = agentApi.getAgentProfileByAgentIdAndLevelId(agentInfoDTO.getId(), agentInfoDTO.getLevelId() + 1);
            //上级代理商id
            Long ancestorId = relationShipDTO.getNearestSpecificLevelAncestorId();
            //构建参数
            DetailRequestVO detailRequestVO = new DetailRequestVO();
            detailRequestVO.setOrderNo(orderDO1.getOrderNo());
            detailRequestVO.setTargetId(ancestorId);
            List<DetailDTO> detailDTOList = commissionApi.selectDetailInfo(detailRequestVO);
            if (CollectionUtil.isNotEmpty(detailDTOList) && detailDTOList.size() == 1) {
                //需要退还的金额
                Integer amount = detailDTOList.get(0).getAmount();
                //调用退还利润的api（目前还没有这个接口）
                //TODO
                //流水表插入一条负数的数据

            }
            //退还差价
            DetailRequestVO detailRequestVO1 = new DetailRequestVO();
            detailRequestVO.setOrderNo(orderDO1.getOrderNo());
            detailRequestVO.setTargetId(agentInfoDTO.getId());
            //退还差价（只有当前代理商自己有差价）
            List<DetailDTO> detailDTOList1 = commissionApi.selectDetailInfo(detailRequestVO1);
            //需要退还的差价
            Integer amount = detailDTOList1.get(0).getAmount();
        }



        //生成交易编号
//        String payOutsNumber = TransactionNoGenerator.payOutsNumber("TX", "wx", "eb2", new Date(),Math.abs(lastTransaction.getAmount()));
//        log.info("payOutsNumber:{}", payOutsNumber);
//        //然后把拿到的DTO转成VO返回
//        TransactionsSaveReqDO transactionsSaveReqVO = BeanUtils.toBean(lastTransaction, TransactionsSaveReqDO.class);
//        log.info("transactionsSaveReqVO:{}", transactionsSaveReqVO);
//        transactionsSaveReqVO.setId(null);
//        transactionsSaveReqVO.setTransactionId(payOutsNumber);
//        transactionsSaveReqVO.setTransactionType(WalletTransactionTypeEnum.COMMISSION_RECOVERY.getStatus());
//        transactionsSaveReqVO.setAmount(transactionsSaveReqVO.getAmount()+(-transactionsSaveReqVO.getAmount()));
//        transactionsSaveReqVO.setBalanceBeforeTransaction(transactionsSaveReqVO.getBalanceBeforeTransaction()+(-transactionsSaveReqVO.getBalanceBeforeTransaction()));
//        transactionsSaveReqVO.setBalanceAfterTransaction(transactionsSaveReqVO.getBalanceAfterTransaction()+(-transactionsSaveReqVO.getBalanceAfterTransaction()));
//        transactionsSaveReqVO.setAvailableBalanceAfterTransaction(transactionsSaveReqVO.getAvailableBalanceAfterTransaction()+(-transactionsSaveReqVO.getAvailableBalanceAfterTransaction()));
//        transactionsSaveReqVO.setFrozenBalanceAfterTransaction(transactionsSaveReqVO.getFrozenBalanceAfterTransaction()+(-transactionsSaveReqVO.getFrozenBalanceAfterTransaction()));
//        log.info("去掉id字段后的vo对象:{}", transactionsSaveReqVO);
//        // 6. 将差价添加到钱包流水表（需调用钱包服务接口）
//        try {
//            walletApi.createTransactions(transactionsSaveReqVO);
//            log.info("【添加钱包流水成功】");
//        } catch (Exception e) {
//            throw exception(WALLET_TRANSACTIONS_FAIL);
//        }

        //更新订单状态已退款
        OrderDO orderDO = new OrderDO();
        orderDO.setId(orderDO1.getId());
        orderDO.setStatus(OrderStatusTypeEnum.REFUNDED.getStatus());
        int i = orderMapper.updateById(orderDO);
        if (i > 0) {
            log.info("更新订单状态成功,受影响行数:{}", i);
        }
        //订单状态记录
        OrderStatusProducer orderStatusProducer = SpringUtil.getBean(OrderStatusProducer.class);
        orderStatusProducer.sendRoleRefreshMessage(Math.toIntExact(orderDO.getId()),
                orderDO.getOrderNo(),
                OrderStatusTypeEnum.REFUND_PROCESSING.getStatus(),
                OrderStatusTypeEnum.REFUNDED.getStatus(),
                null);
        refundOrderMapper.updateById(refundOrder);

        //调用退款成功发送消息通知
        OrderDO orderDO2 = new OrderDO();
        orderDO2.setReceivedAmount(orderDO1.getReceivedAmount());
        orderDO2.setOrderNo(orderDO1.getOrderNo());
          messageUtils.sendrefundUpdateMessage(orderDO2);

        return refundCallBackReqVo;
    }
}
