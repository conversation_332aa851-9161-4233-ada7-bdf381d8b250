package com.yunyi.express2b.module.express.controller.app.v1.invoice.dto.invoiceorder;

import com.yunyi.express2b.module.express.controller.app.v1.invoice.dto.invoiceorderrelation.InvoiceOrderRelationDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import jakarta.validation.constraints.*;

import java.util.List;

@Schema(description = "用户 APP - 订单发票新增/修改 Request VO")
@Data
public class InvoiceOrderDTO {

    @Schema(description = "主键id集合", example = "[1,2]")
    private List<Long> ids;

    @Schema(description = "订单编号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String orderNo;

    @Schema(description = "发票code", requiredMode = Schema.RequiredMode.REQUIRED)
    private String invoiceCode;

    @Schema(description = "发票编号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String invoiceNumber;

    @Schema(description = "发票url", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    private String invoiceUrl;

    @Schema(description = "发票金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "发票金额不能为空")
    private Double amount;

    @Schema(description = "接收邮箱", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "接收邮箱不能为空")
    private String email;

    @Schema(description = "开票状态(APPROVAL-待审核, INVOICING-开票中, INVOICED-已开票, INVOICE_FAILED-开票失败, REJECTED-已驳回, CANCELLED-已作废)", example = "1")
    private String invoiceStatus;

    @Schema(description = "驳回/失败原因", example = "不香")
    private String rejectReason;

    @Schema(description = "审核人ID", example = "432")
    private String reviewerId;

    @Schema(description = "客服工号")
    private String customerServiceNo;

    @Schema(description = "创建者")
    private String creator;

    @Schema(description = "更新者")
    private String updater;

    @Schema(description = "发票订单关联")
    private List<InvoiceOrderRelationDTO> invoiceOrderRelations;

}