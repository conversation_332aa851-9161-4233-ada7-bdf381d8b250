package com.yunyi.express2b.module.express.service.order;

import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.yunyi.express2b.framework.security.core.util.SecurityFrameworkUtils;
import com.yunyi.express2b.module.express.controller.admin.order.vo.OrderCancelVO;
import com.yunyi.express2b.module.express.controller.admin.order.vo.OrderInfoResponseVO;
import com.yunyi.express2b.module.express.controller.admin.paymentorder.vo.UnifiedOrderResponseNew;
import com.yunyi.express2b.module.express.controller.admin.refundorder.vo.RefundOrderSaveReqVO;
import com.yunyi.express2b.module.express.controller.app.v1.order.vo.AgreePayVo;
import com.yunyi.express2b.module.express.controller.app.v1.paymenyorder.vo.PayOrderReqVO;
import com.yunyi.express2b.module.express.dal.dataobject.order.OrderDO;
import com.yunyi.express2b.module.express.dal.dataobject.paymentorder.PaymentOrderDO;
import com.yunyi.express2b.module.express.dal.mysql.order.OrderMapper;
import com.yunyi.express2b.module.express.dal.mysql.paymentorder.PaymentOrderMapper;
import com.yunyi.express2b.module.express.enums.ErrorCodeConstants;
import com.yunyi.express2b.module.express.enums.social.ExpressLogRecordConstants;
import com.yunyi.express2b.module.express.service.pay.PayService;
import com.yunyi.express2b.module.express.service.pay.RefundService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 订单支付与退款服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/4/16 16:58
 */
@Service
@Slf4j
public class OrderPaymentServiceImpl implements OrderPaymentService {

    @Resource
    private OrderMapper orderMapper;
    @Resource
    private PaymentOrderMapper paymentOrderMapper;
    @Resource
    private PayService payService;
    @Resource
    private RefundService refundService;

    @Override
    @LogRecord(type = ExpressLogRecordConstants.PAY_ALL_ORDERS,
            fail = "统一支付失败，失败原因：「{{#_errorMsg}}」",
            success = "接收参数:{{#agreePayVo}}，统一支付的订单编号：{{#orderNos}}，最终微信支付总金额：{{#agreePayVo.receivedAmount}}",
            bizNo = "{{#userId}}")
    @Transactional(rollbackFor = Exception.class)
    public UnifiedOrderResponseNew paycreateOrder(AgreePayVo agreePayVo, List<String> orderNos) {
        log.info("createOrder:{},{}", agreePayVo, orderNos);
        LogRecordContext.putVariable("agreePayVo", agreePayVo);
        LogRecordContext.putVariable("orderNos", orderNos);
        if (orderNos == null || orderNos.isEmpty()) {
            throw exception(ErrorCodeConstants.ORDER_ERROR);
        }
        //根据订单编号order_no集合查询出来所有的订单详情 拿到orderDOS 传到pay方法中
        List<OrderDO> orderDOS = orderMapper.selectOrderListByOrderNos(orderNos);
        UnifiedOrderResponseNew unifiedOrderResponse = null;
        //支付的时候传递支付的钱数和基本数据信息
        unifiedOrderResponse = pay(orderDOS, agreePayVo.getReceivedAmount()); //优惠总金额
        log.info("响应unifiedOrderResponse:{}", unifiedOrderResponse);
        LogRecordContext.putVariable("userId", SecurityFrameworkUtils.getLoginUserId());
        return unifiedOrderResponse;
    }

    @Override
    @LogRecord(type = ExpressLogRecordConstants.CREATE_ORDER,
            fail = "发起微信支付失败，失败原因：「{{#_errorMsg}}」",
            success = "发起微信支付，支付金额:{{#money}}，返回响应:{{#wx}}",
            bizNo = "{{#userId}}")
    public UnifiedOrderResponseNew pay(List<OrderDO> list, Integer money) {
        log.info("【即将发起微信支付，需要支付金额为】：{}", money);
        LogRecordContext.putVariable("money", money);
        LogRecordContext.putVariable("userId", SecurityFrameworkUtils.getLoginUserId());
        OrderDO orderDO = list.get(0);
        List<Long> ids = list.stream().map(OrderDO::getId).collect(Collectors.toList());

        // 构造支付请求参数
        PayOrderReqVO payOrderReqVO = PayOrderReqVO.builder()
                //.orderId(ids.get(0))
                //加上了ids字段得数据 ids就是多个订单的
                .orderIds(ids)
                .amount(money)
                .paymentMethod(orderDO.getPayType())
                .body(orderDO.getPackagingDetails())
                .cardNumber(orderDO.getCardNumber())
                .content(orderDO.getPackagingDetails())
                .build();
        log.info("【构建微信支付请求参数】：{}", payOrderReqVO);

        // 校验支付请求参数
        if (payOrderReqVO == null || payOrderReqVO.getOrderIds() == null || payOrderReqVO.getAmount() == null) {
            log.error("支付请求参数不完整: {}", payOrderReqVO);
            throw exception(ErrorCodeConstants.PARAMETER_IS_EMPTY, "支付请求参数不完整");
        }
        UnifiedOrderResponseNew unifiedOrderResponse;
        try {
            unifiedOrderResponse = payService.payOrder(payOrderReqVO);
            log.info("支付结果：{}", unifiedOrderResponse);
            // 校验支付接口返回值
            if (unifiedOrderResponse == null) {
                log.error("支付接口返回空响应，支付参数: {}", payOrderReqVO);
                throw exception(ErrorCodeConstants.QT_ORDER_PAY_FAIL, "支付接口返回空响应");
            }
            //支付回调方法已经留存更新状态的代码（原因是支付回调更新状态未及时）
        } catch (Exception e) {
            log.error("支付失败，支付参数: {}, 异常信息: {}", payOrderReqVO, e.getMessage(), e);
            throw exception(ErrorCodeConstants.QT_ORDER_PAY_FAIL, "支付失败，请稍后重试");
        }
        //返回订单编号
        List<String> orderNos = new ArrayList<>();
        List<OrderInfoResponseVO> senderOrderList = new ArrayList<>();
        List<OrderInfoResponseVO> receiverOrderList = new ArrayList<>();
        log.info("订单编号：{}", orderNos);
        for (OrderDO order : list) {
            orderNos.add(order.getOrderNo());
            // 创建发件人信息
            OrderInfoResponseVO senderVO = new OrderInfoResponseVO();
            senderVO.setSenderName(order.getSenderName());
            senderVO.setSenderPhone(order.getSenderPhone());
            senderVO.setSenderAddress(order.getSenderAddress());
            senderOrderList.add(senderVO);

            // 创建收件人信息
            OrderInfoResponseVO receiverVO = new OrderInfoResponseVO();
            receiverVO.setReceiverName(order.getReceiverName());
            receiverVO.setReceiverPhone(order.getReceiverPhone());
            receiverVO.setReceiverAddress(order.getReceiverAddress());
            receiverOrderList.add(receiverVO);
        }
        // 构造最终响应
        UnifiedOrderResponseNew unifiedOrder = UnifiedOrderResponseNew.builder()
                .id(ids)
                .paySign(unifiedOrderResponse.getPaySign())
                .aPackage(unifiedOrderResponse.getAPackage())
                .appId(unifiedOrderResponse.getAppId())
                .signType(unifiedOrderResponse.getSignType())
                .timeStamp(unifiedOrderResponse.getTimeStamp())
                .nonceStr(unifiedOrderResponse.getNonceStr())
                .orderNo(orderNos)
                .senderOrderList(senderOrderList)
                .receiverOrderList(receiverOrderList)
                .build();
        log.info("【响应支付信息】:{}", unifiedOrder);
        LogRecordContext.putVariable("wx", unifiedOrder);
        return unifiedOrder;
    }

    @Override
    public OrderCancelVO refundcancelOrder(OrderCancelVO orderCancelVO) {
        //根据订单编号查询支付单信息
        OrderDO order = orderMapper.selectOrder(orderCancelVO.getOrderNo());
        PaymentOrderDO paymentOrderDO = paymentOrderMapper.getPaymentOrder(order.getId());
        //需要通知退款的订单(所需参数)
        RefundOrderSaveReqVO saveReqVO = new RefundOrderSaveReqVO();
        saveReqVO.setPaymentOrderId(paymentOrderDO.getId());
        saveReqVO.setAmount(paymentOrderDO.getAmount());
        saveReqVO.setRefundReason(saveReqVO.getRefundReason());

        if (saveReqVO == null) {
            log.error("【退款失败：所需参数为空】：{}", saveReqVO);
            throw exception(ErrorCodeConstants.REFUND_CREATE_FAIL);
        }
        try {
            refundService.createRefundOrderInfo(saveReqVO);
            log.info("退款成功");
        } catch (Exception e) {
            log.error("退款失败");
            throw exception(ErrorCodeConstants.REQUEST_REFUND_FAIL);
        }
        return orderCancelVO;
    }
} 