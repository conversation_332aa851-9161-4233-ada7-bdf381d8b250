package com.yunyi.express2b.module.express.controller.app.v1.invoice.vo.invoiceuser;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yunyi.express2b.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.yunyi.express2b.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 用户开票信息登记分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InvoiceUserPageReqVO extends PageParam {

    @Schema(description = "用户ID对应统一登录平台的ssoUserId", example = "9189")
    private Long memberId;

    @Schema(description = "发票抬头（单位或个人名称）")
    private String invoiceTitle;

    @Schema(description = "纳税人识别号")
    private String taxNumber;

    @Schema(description = "单位地址")
    private String companyAddress;

    @Schema(description = "单位电话")
    private String companyPhone;

    @Schema(description = "开户行", example = "王五")
    private String bankName;

    @Schema(description = "银行基本户账号", example = "8473")
    private String bankAccount;

    @Schema(description = "接收邮箱")
    private String email;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}