package com.yunyi.express2b.module.express.controller.app.v1.invoice.vo.invoiceorder;

import com.yunyi.express2b.module.express.controller.app.v1.invoice.vo.invoiceorderrelation.InvoiceOrderRelationVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;

import com.alibaba.excel.annotation.*;

@Schema(description = "用户 APP - 订单发票 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InvoiceOrderVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "8470")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "订单编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("订单编号")
    private String orderNo;

    @Schema(description = "发票code", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("发票code")
    private String invoiceCode;

    @Schema(description = "发票编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("发票编号")
    private String invoiceNumber;

    @Schema(description = "发票url", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    @ExcelProperty("发票url")
    private String invoiceUrl;

    @Schema(description = "发票金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("发票金额")
    private Double amount;

    @Schema(description = "接收邮箱", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("接收邮箱")
    private String email;

    @Schema(description = "开票状态(APPROVAL-待审核, INVOICING-开票中, INVOICED-已开票, INVOICE_FAILED-开票失败, REJECTED-已驳回, CANCELLED-已作废)", example = "1")
    @ExcelProperty("开票状态(APPROVAL-待审核, INVOICING-开票中, INVOICED-已开票, INVOICE_FAILED-开票失败, REJECTED-已驳回, CANCELLED-已作废)")
    private String invoiceStatus;

    @Schema(description = "驳回/失败原因", example = "不香")
    @ExcelProperty("驳回/失败原因")
    private String rejectReason;

    @Schema(description = "审核人ID", example = "432")
    @ExcelProperty("审核人ID")
    private String reviewerId;

    @Schema(description = "审核时间")
    @ExcelProperty("审核时间")
    private LocalDateTime reviewTime;

    @Schema(description = "开票时间")
    @ExcelProperty("开票时间")
    private LocalDateTime invoiceTime;

    @Schema(description = "客服工号")
    @ExcelProperty("客服工号")
    private String customerServiceNo;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "发票订单关联")
    private List<InvoiceOrderRelationVO> invoiceOrderRelations;

}