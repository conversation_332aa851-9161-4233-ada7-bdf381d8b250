package com.yunyi.express2b.module.express.service.invoice.invoiceorderrelation;

import com.yunyi.express2b.module.express.controller.app.v1.invoice.dto.invoiceorderrelation.InvoiceOrderRelationPageDTO;
import com.yunyi.express2b.module.express.controller.app.v1.invoice.dto.invoiceorderrelation.InvoiceOrderRelationDTO;
import com.yunyi.express2b.module.express.dal.dataobject.invoice.InvoiceOrderRelationDO;
import com.yunyi.express2b.module.express.dal.mysql.invoice.InvoiceOrderRelationMapper;
import com.yunyi.express2b.module.express.enums.ErrorCodeConstants;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;


import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;


/**
 * 发票订单关联 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InvoiceOrderRelationServiceImpl implements InvoiceOrderRelationService {

    @Resource
    private InvoiceOrderRelationMapper invoiceOrderRelationMapper;

    @Override
    public Long createInvoiceOrderRelation(InvoiceOrderRelationDTO createReqVO) {
        // 插入
        InvoiceOrderRelationDO invoiceOrderRelation = BeanUtils.toBean(createReqVO, InvoiceOrderRelationDO.class);
        invoiceOrderRelationMapper.insert(invoiceOrderRelation);
        // 返回
        return invoiceOrderRelation.getId();
    }

    @Override
    public void updateInvoiceOrderRelation(InvoiceOrderRelationDTO updateReqVO) {
        // 校验存在
        validateInvoiceOrderRelationExists(updateReqVO.getId());
        // 更新
        InvoiceOrderRelationDO updateObj = BeanUtils.toBean(updateReqVO, InvoiceOrderRelationDO.class);
        invoiceOrderRelationMapper.updateById(updateObj);
    }

    @Override
    public void deleteInvoiceOrderRelation(Long id) {
        // 校验存在
        validateInvoiceOrderRelationExists(id);
        // 删除
        invoiceOrderRelationMapper.deleteById(id);
    }

    private void validateInvoiceOrderRelationExists(Long id) {
        if (invoiceOrderRelationMapper.selectById(id) == null) {
            throw exception(ErrorCodeConstants.PARAMETER_IS_EMPTY);
        }
    }

    @Override
    public InvoiceOrderRelationDO getInvoiceOrderRelation(Long id) {
        return invoiceOrderRelationMapper.selectById(id);
    }

    @Override
    public PageResult<InvoiceOrderRelationDO> getInvoiceOrderRelationPage(InvoiceOrderRelationPageDTO pageReqVO) {
        return invoiceOrderRelationMapper.selectPage(pageReqVO);
    }

}