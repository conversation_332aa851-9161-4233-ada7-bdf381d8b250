package com.yunyi.express2b.module.express.job;

import com.yunyi.express2b.framework.quartz.core.handler.JobHandler;
import com.yunyi.express2b.framework.tenant.core.aop.TenantIgnore;
import com.yunyi.express2b.module.express.dal.dataobject.paymentorder.PaymentOrderDO;
import com.yunyi.express2b.module.express.dal.mysql.paymentorder.PaymentOrderMapper;
import com.yunyi.express2b.module.express.enums.PaymentStatusTypeEnum;
import com.yunyi.express2b.module.express.service.paymentorder.PaymentOrderService;
import com.yunyi.express2b.module.infra.api.config.ConfigApi;
import com.yunyi.express2b.module.pricing.api.PricingUtilsApiService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 支付单超时扫描job
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/4/17 下午5:16
 */
@Component
@Slf4j
public class PaymentOrderTimeoutJob implements JobHandler {
    @Resource
    PaymentOrderService paymentOrderService;
    @Override
    @TenantIgnore
    public String execute(String param) throws Exception {
        String result = paymentOrderService.selectTimeoutPaymentOrders();
        return String.format("扫描未支付超时时间的定时任务执行成功");
    }
}
