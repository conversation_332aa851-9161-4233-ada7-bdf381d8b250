package com.yunyi.express2b.module.express.service.invoice;

import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.module.express.controller.app.v1.invoice.vo.invoiceuser.InvoiceUserPageReqVO;
import com.yunyi.express2b.module.express.controller.app.v1.invoice.dto.InvoiceUserDTO;
import com.yunyi.express2b.module.express.controller.app.v1.invoice.vo.invoiceuser.InvoiceUserSaveRespVO;
import com.yunyi.express2b.module.express.dal.dataobject.invoice.InvoiceUserDO;
import jakarta.validation.*;

import com.yunyi.express2b.framework.common.pojo.PageResult;

/**
 * 用户开票信息登记 Service 接口
 *
 * <AUTHOR>
 */
public interface InvoiceUserService {

    /**
     * 创建用户开票信息登记
     *
     * @param invoiceUserDTO 创建信息
     * @return 编号
     */
    CommonResult<InvoiceUserSaveRespVO> createInvoiceUser(InvoiceUserDTO invoiceUserDTO);

    /**
     * 更新用户开票信息登记
     *
     * @param updateReqVO 更新信息
     */
    void updateInvoiceUser(@Valid InvoiceUserDTO updateReqVO);

    /**
     * 删除用户开票信息登记
     *
     * @param id 编号
     */
    void deleteInvoiceUser(Long id);

    /**
     * 获得用户开票信息登记
     *
     * @param id 编号
     * @return 用户开票信息登记
     */
    InvoiceUserDO getInvoiceUser(Long id);

    /**
     * 获得用户开票信息登记分页
     *
     * @param pageReqVO 分页查询
     * @return 用户开票信息登记分页
     */
    PageResult<InvoiceUserDO> getInvoiceUserPage(InvoiceUserPageReqVO pageReqVO);

}