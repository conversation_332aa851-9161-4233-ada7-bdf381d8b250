package com.yunyi.express2b.module.express.service.invoice.invoiceorder;

import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.module.express.controller.app.v1.invoice.dto.invoiceorder.InvoiceOrderPageDTO;
import com.yunyi.express2b.module.express.controller.app.v1.invoice.dto.invoiceorder.InvoiceOrderDTO;
import com.yunyi.express2b.module.express.controller.app.v1.invoice.vo.invoiceorder.InvoiceOrderVO;
import com.yunyi.express2b.module.express.dal.dataobject.invoice.InvoiceOrderDO;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.*;

import com.yunyi.express2b.framework.common.pojo.PageResult;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 订单发票 Service 接口
 *
 * <AUTHOR>
 */
public interface InvoiceOrderService {

    /**
     * 创建订单发票
     *
     * @param invoiceOrderDTO 创建信息
     * @return 编号
     */
    CommonResult<String> createInvoiceOrder(@Valid InvoiceOrderDTO invoiceOrderDTO);

    /**
     * 更新订单发票
     *
     * @param invoiceOrderDTO 更新信息
     */
    void updateInvoiceOrder(InvoiceOrderDTO invoiceOrderDTO);

    /**
     * 删除订单发票
     *
     * @param id 编号
     */
    void deleteInvoiceOrder(Long id);

    /**
     * 获得订单发票
     *
     * @param id 编号
     * @return 订单发票
     */
    InvoiceOrderVO getInvoiceOrder(Long id);

    /**
     * 获得订单发票分页
     *
     * @param invoiceOrderPageDTO 分页查询
     * @return 订单发票分页
     */
    PageResult<InvoiceOrderDO> getInvoiceOrderPage(InvoiceOrderPageDTO invoiceOrderPageDTO);

    /**
     * 获取发票下载链接
     */
    CommonResult<String> downloadInvoiceOrder(Long id);

    void exportInvoices(HttpServletResponse response, List<Long> ids);

    CommonResult<String> importInvoices(MultipartFile file);
}