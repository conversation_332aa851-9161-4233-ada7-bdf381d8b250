package com.yunyi.express2b.module.express.controller.admin.order;

import com.yunyi.express2b.framework.apilog.core.annotation.ApiAccessLog;
import com.yunyi.express2b.framework.common.pojo.CallBackResult;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.module.express.api.order.dto.WorkbenchInterfaceDTO;
import com.yunyi.express2b.module.express.controller.admin.order.dto.OrderCallBackRequestDTO;
import com.yunyi.express2b.module.express.controller.admin.order.vo.*;
import com.yunyi.framework.api.login.api.qrcode.vo.PushRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;

import static com.yunyi.express2b.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.yunyi.express2b.framework.common.pojo.CommonResult.success;
import static com.yunyi.express2b.module.express.utils.FileTypeUtils.writeAttachment;

/**
 * 订单管理 controller层
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 订单管理")
@RestController
@RequestMapping("/express/order")
@Validated
@Slf4j
public class OrderController {

    @Resource
    private AdminOrderFacade adminOrderFacade;

    /**
     * 订单数 订单付款数 已入账订单数（） 带入账订单数（） 销售额 实时对比数据下方的对比昨日
     *
     * @param workbenchInterfaceDTO
     * @return
     */
    @GetMapping("/workbench-interface")
    @Operation(summary = "订单数.订单付款数.已入账订单数. 带入账订单数")
    public CommonResult<WorkbenchInterfaceDTO> workbenchInterface(WorkbenchInterfaceDTO workbenchInterfaceDTO) {
        return success(adminOrderFacade.workbenchInterface(workbenchInterfaceDTO));
    }

    @PostMapping("/order-callback")
    @Operation(summary = "向运力下单回调")
    @PermitAll
    public CommonResult<Boolean> orderCallback(@Valid @RequestBody OrderCallBackRequestDTO orderCallBackRequest) {
        adminOrderFacade.orderCallback(orderCallBackRequest);
        return success(true);
    }

    @PostMapping("/concern")
    @Operation(summary = "公众号关注/取关推送通知回调接口")
    @PermitAll
    public CommonResult<Long> officialAccountAttentionOrClearance(@RequestBody PushRequest pushRequest) {
        return success(adminOrderFacade.officialAccountAttentionOrClearance(pushRequest));
    }


    @PostMapping("/track-callback")
    @Operation(summary = "物流轨迹回调")
    @PermitAll
    public CallBackResult<Boolean> trackCallback(@Valid @RequestBody TrackCallBackVO trackCallBackVO) {
        adminOrderFacade.trackCallback(trackCallBackVO);
        return CallBackResult.success(true);
    }

    /**
     * 取消订单
     *
     * @param
     * @return
     */
    @PostMapping("/cancel-order")
    @Operation(summary = "取消订单")
    public CommonResult<OrderCancelVO> cancel(@Valid @RequestBody OrderCancelVO orderCancelVO) {
        return success(adminOrderFacade.cancelOrder(orderCancelVO));
    }

    /**
     * 取消订单
     *
     * @param
     * @return
     */
    @PostMapping("/cancel-order-callback")
    @Operation(summary = "取消订单回调")
    public CommonResult<CancelOrderResponseVO> cancelOrderCallBack(@Valid @RequestBody CancelOrderCallBackVO cancelOrderCallBackVO) {
        return success(adminOrderFacade.cancelOrderCallBack(cancelOrderCallBackVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新订单")
    @PreAuthorize("@ss.hasPermission('express:order:update')")
    public CommonResult<Boolean> updateOrder(@Valid @RequestBody OrderSaveReqVO updateReqVO) {
        adminOrderFacade.updateOrder(updateReqVO);
        return success(true);
    }

    @PutMapping("/update-by-order-id")
    @Operation(summary = "根据订单编号更新订单状态")
    @PreAuthorize("@ss.hasPermission('express:order:update')")
    public CommonResult<Boolean> updateByOrderId(@Valid @RequestBody OrderSaveReqVO updateReqVO) {
        adminOrderFacade.updateOrder(updateReqVO);
        return success(true);
    }

    @GetMapping("/get-order-status")
    @Operation(summary = "根据订单编号获取订单状态")
    @Parameter(name = "orderNo", description = "订单编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('express:order:query')")
    public CommonResult<String> getOrderStatus(@RequestParam("orderNo") String orderNo) {
        String orderStatus = adminOrderFacade.getOrderStatus(orderNo);
        return success(orderStatus);
    }

    @GetMapping("/page")
    @Operation(summary = "获得订单管理分页")
    @PreAuthorize("@ss.hasPermission('express:order:query')")
    public CommonResult<PageResult<OrderRespVO>> getOrderPage(@Valid OrderPageReqVO pageReqVO) {
        PageResult<OrderRespVO> pageResult = adminOrderFacade.getOrderPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出订单管理 Excel")
    @PreAuthorize("@ss.hasPermission('express:order:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportOrderExcel(@Valid OrderPageReqVO pageReqVO, HttpServletResponse response) throws IOException {
        adminOrderFacade.exportOrderExcel(pageReqVO, response);
    }

    @GetMapping("/file-download")
    @Operation(summary = "导入模板下载")
    public void downloadFile(HttpServletResponse response) throws IOException {
        //使用类加载器读取文件
        InputStream inputStream = getClass().getClassLoader()
                .getResourceAsStream("files/example.xlsx");

        if (inputStream == null) {
            throw new FileNotFoundException("文件不存在于resources/files目录");
        }
        byte[] fileContent = inputStream.readAllBytes();
        writeAttachment(response, "example.xlsx", fileContent);
    }

    @GetMapping("/order-messages")
    @Operation(summary = "接收消息的通知")
    public CommonResult<String> receiveOrderMessage(@RequestBody ReceiveMessageRequest request) {
        return success(adminOrderFacade.receiveOrderMessage(request));
    }

    //-------------------------------------------
//    @PostMapping("/create-order-to-promotion-platform")
//    @Operation(summary = "订单推送到统一推广平台")
//    public CommonResult<Long> createOrderToPromo(@Valid @RequestBody PromoCreateOrderRequestVO requestVO) {
//        return success(orderService.createOrderToPromo(requestVO));
//    }

}