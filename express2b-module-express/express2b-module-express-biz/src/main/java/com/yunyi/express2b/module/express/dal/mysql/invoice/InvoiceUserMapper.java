package com.yunyi.express2b.module.express.dal.mysql.invoice;

import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yunyi.express2b.framework.mybatis.core.mapper.BaseMapperX;

import com.yunyi.express2b.module.express.controller.app.v1.invoice.vo.invoiceuser.InvoiceUserPageReqVO;
import com.yunyi.express2b.module.express.dal.dataobject.invoice.InvoiceUserDO;
import org.apache.ibatis.annotations.Mapper;


/**
 * 用户开票信息登记 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InvoiceUserMapper extends BaseMapperX<InvoiceUserDO> {

    default PageResult<InvoiceUserDO> selectPage(InvoiceUserPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InvoiceUserDO>()
                .eqIfPresent(InvoiceUserDO::getMemberId, reqVO.getMemberId())
                .eqIfPresent(InvoiceUserDO::getInvoiceTitle, reqVO.getInvoiceTitle())
                .eqIfPresent(InvoiceUserDO::getTaxNumber, reqVO.getTaxNumber())
                .eqIfPresent(InvoiceUserDO::getCompanyAddress, reqVO.getCompanyAddress())
                .eqIfPresent(InvoiceUserDO::getCompanyPhone, reqVO.getCompanyPhone())
                .likeIfPresent(InvoiceUserDO::getBankName, reqVO.getBankName())
                .eqIfPresent(InvoiceUserDO::getBankAccount, reqVO.getBankAccount())
                .eqIfPresent(InvoiceUserDO::getEmail, reqVO.getEmail())
                .betweenIfPresent(InvoiceUserDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(InvoiceUserDO::getId));
    }

    default InvoiceUserDO getInvoiceUserByMemberId(Long memberId) {
        return selectOne(new LambdaQueryWrapperX<InvoiceUserDO>()
                .eq(InvoiceUserDO::getMemberId, memberId));
    }
}