package com.yunyi.express2b.module.express.controller.app.v1.invoice.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Schema(description = "未开票订单")
public class UnInvoicedOrderVO {

    @Schema(description = "订单ID")
    private Long id;

    @Schema(description = "寄件人地址")
    private String senderAddress;

    @Schema(description = "收件人地址")
    private String receiverAddress;

    @Schema(description = "实收金额|订单金额")
    private Integer receivedAmount;

    @Schema(description = "订单状态,参见 OrderStatusTypeEnum 枚举", example = "1")
    private String status;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "订单编号")
    private String orderNo;
}
