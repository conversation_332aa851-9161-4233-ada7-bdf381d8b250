package com.yunyi.express2b.module.express.controller.admin.order.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/4/14 14:51
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderCallBackRequestDTO {
    public OrderCallbackData data;

    // 内层data对象
    @Data
    public static class OrderCallbackData {
        private String volume;
        private String courierName;
        private BigDecimal originalPrice;
        private String courierMobile;
        private String ucmOrderSn;
        private BigDecimal freight;
        private String thirdOrderSn;
        private Integer weight;
        private String pickupCode;
        private String expressSn;
        private Integer status;
        private String cancelMsg;
        private BigDecimal feePrice;
        private BigDecimal defPrice;
        // 新增字段：费用明细
        private List<FeeDetail> feeDetails;
    }
    @Data
    public static class FeeDetail{
        // 费用类型
        private String type;
        // 费用描述
        private String desc;
        // 费用价格
        private BigDecimal price;
        // 支付状态
        private String payStatus;
    }

}
