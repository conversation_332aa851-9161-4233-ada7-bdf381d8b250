package com.yunyi.express2b.module.express.job.job;

import cn.hutool.db.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.quartz.core.handler.JobHandler;
import com.yunyi.express2b.framework.security.core.util.SecurityFrameworkUtils;
import com.yunyi.express2b.framework.tenant.core.aop.TenantIgnore;
import com.yunyi.express2b.module.express.api.message.dto.OrderChangeDTO;
import com.yunyi.express2b.module.express.controller.admin.order.vo.OrderPageReqVO;
import com.yunyi.express2b.module.express.dal.dataobject.order.OrderDO;
import com.yunyi.express2b.module.express.dal.mysql.order.OrderMapper;
import com.yunyi.express2b.module.express.dal.mysql.products.ProductsMapper;
import com.yunyi.express2b.module.express.enums.ErrorCodeConstants;
import com.yunyi.express2b.module.express.enums.OrderStatusTypeEnum;
import com.yunyi.express2b.module.express.service.message.SmsService;
import com.yunyi.express2b.module.express.service.order.OrderQueryService;
import com.yunyi.express2b.module.express.service.sendmessage.MessageUtils;
import com.yunyi.express2b.module.express.utils.GenerateOrderNumberUtil;
import com.yunyi.framework.api.login.api.message.MessageApi;
import com.yunyi.framework.api.login.api.message.vo.MessageRequest;
import com.yunyi.framework.api.login.api.message.vo.MessageResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.yunyi.express2b.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/4/16 16:58
 */
@Slf4j
@Component
public class SelectUnPaidOrderJob implements JobHandler {

    @Resource
    private OrderQueryService orderQueryService;
    @Resource
    private MessageUtils messageutils;

    @Override
    @TenantIgnore
    public String execute(String param) throws Exception {
        log.info("【开始执行查询未付款的定时任务】----------------");
        OrderPageReqVO pageReqVO = new OrderPageReqVO();
        pageReqVO.setPageSize(50); // 每次查询50条
        pageReqVO.setPageNo(1); // 从第一页开始

        long processedCount = 0;
        while (true) {
            PageResult<OrderDO> pageResult = orderQueryService.unpaidOrders(pageReqVO);
            if (pageResult == null || pageResult.getList().isEmpty()) {
                log.info("【查询未付款的定时任务】查询结束，没有更多未支付的订单。");
                break; // 没有数据了，退出循环
            }

            log.info("【查询未支付的订单集合】查询到第 {} 页，共 {} 条数据，开始处理...", pageReqVO.getPageNo(), pageResult.getList().size());
            for (OrderDO orderDO : pageResult.getList()) {
                messageutils.sendGoodsStatusUpdateMessage(orderDO);
                processedCount++;
            }

            // 如果当前页的数据量小于页面大小，说明是最后一页
            if (pageResult.getList().size() < pageReqVO.getPageSize()) {
                break;
            }

            // 查询下一页
            pageReqVO.setPageNo(pageReqVO.getPageNo() + 1);
        }

        log.info("【查询未付款的定时任务执行完毕】总共处理了 {} 条订单。", processedCount);
        return String.format("定时任务执行成功，共处理 %d 条订单。", processedCount);
    }






}
