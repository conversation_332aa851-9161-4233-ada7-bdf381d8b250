package com.yunyi.express2b.module.express.controller.app.v1.invoice;

import com.yunyi.express2b.module.express.controller.app.v1.invoice.vo.invoiceuser.InvoiceUserPageReqVO;
import com.yunyi.express2b.module.express.controller.app.v1.invoice.vo.invoiceuser.InvoiceUserRespVO;
import com.yunyi.express2b.module.express.controller.app.v1.invoice.dto.InvoiceUserDTO;
import com.yunyi.express2b.module.express.controller.app.v1.invoice.vo.invoiceuser.InvoiceUserSaveRespVO;
import com.yunyi.express2b.module.express.dal.dataobject.invoice.InvoiceUserDO;
import com.yunyi.express2b.module.express.service.invoice.InvoiceUserService;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.yunyi.express2b.framework.common.pojo.PageParam;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import static com.yunyi.express2b.framework.common.pojo.CommonResult.success;

import com.yunyi.express2b.framework.excel.core.util.ExcelUtils;

import com.yunyi.express2b.framework.apilog.core.annotation.ApiAccessLog;
import static com.yunyi.express2b.framework.apilog.core.enums.OperateTypeEnum.*;



@Tag(name = "用户开票信息")
@RestController
@RequestMapping("/v1/express/invoice-user")
@Validated
public class InvoiceUserController {

    @Resource
    private InvoiceUserService invoiceUserService;

    @PostMapping("/create")
    @Operation(summary = "创建用户开票信息")
    public CommonResult<InvoiceUserSaveRespVO> createInvoiceUser(@RequestBody(required = false) InvoiceUserDTO invoiceUserDTO) {
        return invoiceUserService.createInvoiceUser(invoiceUserDTO);
    }

    @PutMapping("/update")
    @Operation(summary = "修改用户开票信息")
    public CommonResult<Boolean> updateInvoiceUser(@Valid @RequestBody InvoiceUserDTO invoiceUserDTO) {
        invoiceUserService.updateInvoiceUser(invoiceUserDTO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除用户开票信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('express2:invoice-user:delete')")
    public CommonResult<Boolean> deleteInvoiceUser(@RequestParam("id") Long id) {
        invoiceUserService.deleteInvoiceUser(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得用户开票信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('express2:invoice-user:query')")
    public CommonResult<InvoiceUserRespVO> getInvoiceUser(@RequestParam("id") Long id) {
        InvoiceUserDO invoiceUser = invoiceUserService.getInvoiceUser(id);
        return success(BeanUtils.toBean(invoiceUser, InvoiceUserRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得用户开票信息分页")
    @PreAuthorize("@ss.hasPermission('express2:invoice-user:query')")
    public CommonResult<PageResult<InvoiceUserRespVO>> getInvoiceUserPage(@Valid InvoiceUserPageReqVO pageReqVO) {
        PageResult<InvoiceUserDO> pageResult = invoiceUserService.getInvoiceUserPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InvoiceUserRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出用户开票信息 Excel")
    @PreAuthorize("@ss.hasPermission('express2:invoice-user:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportInvoiceUserExcel(@Valid InvoiceUserPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<InvoiceUserDO> list = invoiceUserService.getInvoiceUserPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "用户开票信息.xls", "数据", InvoiceUserRespVO.class,
                        BeanUtils.toBean(list, InvoiceUserRespVO.class));
    }

}