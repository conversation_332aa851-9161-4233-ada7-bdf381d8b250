package com.yunyi.express2b.module.express.dal.dataobject.order;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yunyi.express2b.framework.mybatis.core.dataobject.BaseDO;
import com.yunyi.express2b.module.express.dal.dataobject.products.ProductsDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

/**
 * 订单管理 DO
 *
 * <AUTHOR>
 */
@TableName("express2b_order")
@KeySequence("express2b_order_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderDO extends BaseDO {

    /**
     * 订单ID
     */
    @TableId
    private Long id;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 批量订单主键
     */
    private Long batchId;
    /**
     * 用户ID
     */
    private Long personalId;
    /**
     * 快递品牌ID
     */
    private Long brandId;
    /**
     * 阶梯价策略标志
     */
    private String pricingKey;
    /**
     * 分润策略标志
     */
    private String profitKey;
    /**
     * 订单状态
     */
    private String status;
    /**
     * 寄件人姓名
     */
    private String senderName;
    /**
     * 寄件人电话
     */
    private String senderPhone;
    /**
     * 寄件人地址
     */
    private String senderAddress;
    /**
     * 寄件人地址ID
     */
    private Long senderAddressId;
    /**
     * 收件人姓名
     */
    private String receiverName;
    /**
     * 收件人电话
     */
    private String receiverPhone;
    /**
     * 收件人地址
     */
    private String receiverAddress;
    /**
     * 收件人地址ID
     */
    private Long receiverAddressId;
    /**
     * 成本金额
     */
    private Integer costAmount;
    /**
     * 零售金额
     */
    private Integer retailAmount;
    /**
     * 实收金额|订单金额
     */
    private Integer receivedAmount;
    /**
     * 优惠总金额
     */
    private Integer discountAmount;
    /**
     * 代理商佣金
     */
    private Integer agentCommission;
    /**
     * 团长佣金
     */
    private Integer groupLeaderCommission;
    /**
     * 快递运单号
     */
    private String trackingNumber;
    /**
     * 物品名称
     */
    private String itemName;
    /**
     * 物品分类
     */
    private String itemCategory;
    /**
     * 物品数量
     */
    private Integer itemQuantity;
    /**
     * 物品重量（kg）
     */
    private Integer itemWeight;
    /**
     * 物品价值（元）
     */
    private BigDecimal itemValue;
    /**
     * 包装详情
     */
    private String packagingDetails;
    /**
     * 是否保价
     */
    private Boolean isInsured;
    /**
     * 保价金额（元）
     */
    private BigDecimal insuredAmount;
    /**
     * 预约日期
     */
    private LocalDate scheduledDate;
    /**
     * 收件员姓名
     */
    private String receiverStaffName;

    /**
     * 支付类型
     */
    private String payType;
    /**
     * 用户地址
     */
    private String memberIp;
    /**
     * 预约取件起止时间
     */
    private LocalTime scheduledStartTime;
    /**
     * 预约取件结束时间
     */
    private LocalTime scheduledEndTime;

//    /**
//     * 取件的预约开始和结束时间
//     */
//    private String scheduledTime;

    /**
     * 统一运力订单的唯一标识
     */
    private String ucmOrderSn;
    /**
     * 取件的预约日期
     */
    private String appointmentTime;
    /**
     * 快递公司code，和统一运力对应（expressCode），对应brand表中brand_key
     */
    private String expressCode;

    /**
     * 产品类型
     */
    private String productCode;

    /**
     * 次卡编号
     */
    private String cardNumber;

    /**
     * 快递员手机号
     */
    private String receiverStaffMobile;

    /**
     * 取件码
     */
    private String receiverStaffPickupCode;

    /**
     * 物品详情列表数据
     */
    @TableField(exist = false)
    List<ProductsDO> productsDOList;

    /**
     * 多租户编号
     */
    private Long tenantId;

    /**
     * 代理商ID
     */
    private Long agentId;

    /**
     * 待补运费金额
     */
    private Integer prepaidAmount;

    /**
     * 询价返回的分润金额
     */
    private Integer shareAmount;
    /**
     * 开票状态
     */
    private String invoiceStatus;

    /**
     * 标记是否已进行坏账处理
     * CommonStatusEnum.DISABLE = 1 = 已处理
     * CommonStatusEnum.ENABLE = 0 =未处理
     */
    private int badDebtChecked;
}