package com.yunyi.express2b.module.express.service.order;

import com.yunyi.express2b.framework.common.exception.ServiceException;
import com.yunyi.express2b.framework.security.core.util.SecurityFrameworkUtils;
import com.yunyi.express2b.framework.test.core.ut.BaseDbUnitTest;
import com.yunyi.express2b.module.agent.api.AgentApi;
import com.yunyi.express2b.module.crm.api.card.refundcard.RefundCardApi;
import com.yunyi.express2b.module.express.controller.admin.order.dto.OrderCallBackRequestDTO;
import com.yunyi.express2b.module.express.controller.admin.order.vo.OrderCancelVO;
import com.yunyi.express2b.module.express.dal.dataobject.order.OrderDO;
import com.yunyi.express2b.module.express.dal.dataobject.paymentorder.PaymentOrderDO;
import com.yunyi.express2b.module.express.dal.mysql.order.OrderMapper;
import com.yunyi.express2b.module.express.dal.mysql.paymentorder.PaymentOrderMapper;
import com.yunyi.express2b.module.express.enums.ErrorCodeConstants;
import com.yunyi.express2b.module.express.enums.OrderCallbackStatusEnum;
import com.yunyi.express2b.module.express.enums.OrderStatusTypeEnum;
import com.yunyi.express2b.module.express.mq.producer.OrderStatusProducer;
import com.yunyi.express2b.module.express.service.pay.RefundService;
import com.yunyi.express2b.module.express.service.sendmessage.MessageUtils;
import com.yunyi.express2b.module.infra.api.config.ConfigApi;
import com.yunyi.express2b.module.pricing.api.NewTemplateApi;
import com.yunyi.express2b.module.wallet.api.TransationsApi.TransactionsApi;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.MockedStatic;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.concurrent.Executor;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * OrderLifecycleServiceImpl 单元测试类
 * <p>
 * 测试订单生命周期服务的各种场景，包括订单取消、回调处理等核心功能
 *
 * <AUTHOR>
 */
@Import({OrderLifecycleServiceImpl.class, OrderLifecycleServiceImplTest.TestConfig.class})
class OrderLifecycleServiceImplTest extends BaseDbUnitTest {

    @Resource
    private OrderLifecycleServiceImpl orderLifecycleService;

    @MockitoBean
    private OrderStatusProducer orderStatusProducer;

    @MockitoBean
    private OrderMapper orderMapper;

    @MockitoBean
    private PaymentOrderMapper paymentOrderMapper;

    @MockitoBean
    private RefundService refundService;

    @MockitoBean
    private RefundCardApi refundCardApi;

    @MockitoBean
    private OrderPromotionService orderPromotionService;

    @MockitoBean
    private OrderLogisticsService orderLogisticsService;

    @MockitoBean
    private AgentApi agentApi;

    @MockitoBean
    private NewTemplateApi newTemplateApi;

    @MockitoBean
    private ConfigApi configApi;

    @MockitoBean
    private OrderPaymentService orderPaymentService;

    @MockitoBean
    private MessageUtils messageUtils;

    @MockitoBean
    private TransactionsApi transactionsApi;

    @BeforeEach
    void setUp() {
        // 设置通用的Mock行为
        when(configApi.getConfigValueByKey("profit")).thenReturn("0.1");
    }

    @Nested
    @DisplayName("订单取消测试")
    class CancelOrderTest {

        @Test
        @DisplayName("取消已创建订单 - 应该成功取消并更新状态")
        void cancelOrder_CreatedOrder_ShouldCancelSuccessfully() {
            // Arrange
            OrderCancelVO orderCancelVO = createOrderCancelVO("ORDER001");
            OrderDO order = createOrderDO("ORDER001", OrderStatusTypeEnum.CREATED.getStatus());

            when(orderMapper.selectOrder("ORDER001")).thenReturn(order);
            when(orderMapper.updateOrderByNos(anyString(), anyString())).thenReturn(1);

            try (MockedStatic<SecurityFrameworkUtils> mockedSecurity = mockStatic(SecurityFrameworkUtils.class)) {
                mockedSecurity.when(SecurityFrameworkUtils::getLoginUserId).thenReturn(1L);

                // Act
                OrderCancelVO result = orderLifecycleService.cancelOrder(orderCancelVO);

                // Assert
                assertNotNull(result, "返回结果不应为空");
                assertEquals(orderCancelVO.getOrderNo(), result.getOrderNo(), "订单号应该一致");

                // Verify
                verify(orderMapper).updateOrderByNos("ORDER001", OrderStatusTypeEnum.CANCELED.getStatus());
                verify(refundCardApi, never()).refundCard(any()); // 没有卡号，不应该退卡
                verify(orderStatusProducer).sendRoleRefreshMessage(eq(order.getId().intValue()), eq(order.getOrderNo()), any(), any(), any());
            }
        }

        @Test
        @DisplayName("取消已支付订单 - 应该创建退款订单")
        void cancelOrder_PaidOrder_ShouldCreateRefundOrder() {
            // Arrange
            OrderCancelVO orderCancelVO = createOrderCancelVO("ORDER002");
            OrderDO order = createOrderDO("ORDER002", OrderStatusTypeEnum.PAID.getStatus());
            PaymentOrderDO paymentOrder = createPaymentOrderDO(1L, 1000);

            when(orderMapper.selectOrder("ORDER002")).thenReturn(order);
            when(orderMapper.updateOrderByNos(anyString(), anyString())).thenReturn(1);
            when(paymentOrderMapper.selectListById(order.getId())).thenReturn(Arrays.asList(paymentOrder));
            when(refundService.createRefundOrderInfo(any())).thenReturn(1L);

            try (MockedStatic<SecurityFrameworkUtils> mockedSecurity = mockStatic(SecurityFrameworkUtils.class)) {
                mockedSecurity.when(SecurityFrameworkUtils::getLoginUserId).thenReturn(1L);

                // Act
                OrderCancelVO result = orderLifecycleService.cancelOrder(orderCancelVO);

                // Assert
                assertNotNull(result, "返回结果不应为空");

                // Verify
                verify(orderMapper, times(2)).updateOrderByNos(eq("ORDER002"), anyString());
                verify(refundService).createRefundOrderInfo(any());
                verify(orderPromotionService).asyncNotifyCancelOrder(eq(order));
                verify(orderStatusProducer).sendRoleRefreshMessage(eq(order.getId().intValue()),
                        eq(order.getOrderNo()),
                        eq(OrderStatusTypeEnum.PAID.getStatus()),
                        eq(OrderStatusTypeEnum.CANCELED_AFTER_PAYMENT.getStatus()),
                        any());
                verify(orderStatusProducer).sendRoleRefreshMessage(eq(order.getId().intValue()),
                        eq(order.getOrderNo()),
                        eq(OrderStatusTypeEnum.CANCELED_AFTER_PAYMENT.getStatus()),
                        eq(OrderStatusTypeEnum.REFUND_PROCESSING.getStatus()),
                        any());
            }
        }

        @Test
        @DisplayName("取消不存在的订单 - 应该抛出ORDER_NOT_EXISTS异常")
        void cancelOrder_OrderNotExists_ShouldThrowException() {
            // Arrange
            OrderCancelVO orderCancelVO = createOrderCancelVO("NONEXISTENT");
            when(orderMapper.selectOrder("NONEXISTENT")).thenReturn(null);

            try (MockedStatic<SecurityFrameworkUtils> mockedSecurity = mockStatic(SecurityFrameworkUtils.class)) {
                mockedSecurity.when(SecurityFrameworkUtils::getLoginUserId).thenReturn(1L);

                // Act & Assert
                ServiceException exception = assertThrows(ServiceException.class,
                        () -> orderLifecycleService.cancelOrder(orderCancelVO));

                assertEquals(ErrorCodeConstants.ORDER_NOT_EXISTS.getCode(), exception.getCode());
            }
        }

        @ParameterizedTest
        @ValueSource(strings = {"COMPLETED", "REFUNDED", "IN_TRANSIT"})
        @DisplayName("取消不可取消状态的订单 - 应该抛出ORDER_STATUS_INVALID异常")
        void cancelOrder_InvalidStatus_ShouldThrowException(String status) {
            // Arrange
            OrderCancelVO orderCancelVO = createOrderCancelVO("ORDER003");
            OrderDO order = createOrderDO("ORDER003", status);

            when(orderMapper.selectOrder("ORDER003")).thenReturn(order);

            try (MockedStatic<SecurityFrameworkUtils> mockedSecurity = mockStatic(SecurityFrameworkUtils.class)) {
                mockedSecurity.when(SecurityFrameworkUtils::getLoginUserId).thenReturn(1L);

                // Act & Assert
                ServiceException exception = assertThrows(ServiceException.class,
                        () -> orderLifecycleService.cancelOrder(orderCancelVO));

                assertEquals(ErrorCodeConstants.ORDER_STATUS_INVALID.getCode(), exception.getCode());
            }
        }

        @Test
        @DisplayName("取消有卡号的订单 - 应该退还卡片")
        void cancelOrder_OrderWithCard_ShouldRefundCard() {
            // Arrange
            OrderCancelVO orderCancelVO = createOrderCancelVO("ORDER004");
            OrderDO order = createOrderDO("ORDER004", OrderStatusTypeEnum.CREATED.getStatus());
            order.setCardNumber("CARD123456");

            when(orderMapper.selectOrder("ORDER004")).thenReturn(order);
            when(orderMapper.updateOrderByNos(anyString(), anyString())).thenReturn(1);

            try (MockedStatic<SecurityFrameworkUtils> mockedSecurity = mockStatic(SecurityFrameworkUtils.class)) {
                mockedSecurity.when(SecurityFrameworkUtils::getLoginUserId).thenReturn(1L);

                // Act
                orderLifecycleService.cancelOrder(orderCancelVO);

                // Assert & Verify
                verify(refundCardApi).refundCard(any());
                verify(orderStatusProducer).sendRoleRefreshMessage(eq(order.getId().intValue()), eq(order.getOrderNo()), any(), any(), any());
            }
        }
    }

    @Nested
    @DisplayName("订单回调处理测试")
    class OrderCallbackTest {

        @Test
        @DisplayName("处理下单成功回调 - 应该记录日志无需处理")
        void orderCallback_SuccessStatus_ShouldLogOnly() {
            // Arrange
            OrderCallBackRequestDTO callbackDTO = createCallbackDTO(OrderCallbackStatusEnum.SUCCESS.getCode(), "UCM001");
            OrderDO order = createOrderDO("ORDER001", OrderStatusTypeEnum.WAIT.getStatus());

            when(orderMapper.selectOrderBycallBackParameter("UCM001")).thenReturn(order);

            // Act
            assertDoesNotThrow(() -> orderLifecycleService.orderCallback(callbackDTO));

            // Assert
            // 验证只是记录日志，没有其他操作
            verify(orderMapper, never()).updateOrderBycallBackParameter(any());
        }

        @Test
        @DisplayName("处理已接单回调 - 应该更新订单状态为待取件")
        void orderCallback_OrderAccepted_ShouldUpdateToPickupPending() {
            // Arrange
            OrderCallBackRequestDTO callbackDTO = createCallbackDTO(OrderCallbackStatusEnum.ORDER_ACCEPTED.getCode(), "UCM002");
            OrderDO order = createOrderDO("ORDER002", OrderStatusTypeEnum.WAIT.getStatus());

            when(orderMapper.selectOrderBycallBackParameter("UCM002")).thenReturn(order);
            when(orderMapper.updateOrderBycallBackParameter(any())).thenReturn(1);

            // Act
            orderLifecycleService.orderCallback(callbackDTO);

            // Assert
            verify(orderMapper).updateOrderBycallBackParameter(argThat(orderUpdate ->
                    OrderStatusTypeEnum.PICKUP_PENDING.getStatus().equals(orderUpdate.getStatus())
            ));
            verify(orderStatusProducer).sendRoleRefreshMessage(eq(order.getId().intValue()), eq(order.getOrderNo()), any(), any(), any());
        }

        @Test
        @DisplayName("处理重量修改回调 - 需要补付费用时应该更新订单状态")
        void orderCallback_WeightModified_ShouldUpdateOrderForAdditionalFee() {
            // Arrange
            OrderCallBackRequestDTO callbackDTO = createWeightModifiedCallbackDTO("UCM003", 2000, 1500);
            OrderDO order = createOrderDO("ORDER003", OrderStatusTypeEnum.PICKUP_PENDING.getStatus());
            order.setReceivedAmount(1000); // 原金额1000分

            when(orderMapper.selectOrderBycallBackParameter("UCM003")).thenReturn(order);
            when(orderMapper.updateOrderInfo(any())).thenReturn(1);
            when(configApi.getConfigValueByKey("profit")).thenReturn("0.1");

            // Act
            orderLifecycleService.orderCallback(callbackDTO);

            // Assert
            verify(orderMapper).updateOrderInfo(argThat(orderUpdate ->
                    OrderStatusTypeEnum.PENDING_ADDITIONAL_FEE.getStatus().equals(orderUpdate.getStatus())
            ));
            verify(orderPromotionService).asyncNotifyUpdateOrderAmount(eq(order), any());
        }

        @Test
        @DisplayName("处理已签收回调 - 应该更新订单状态为已完成")
        void orderCallback_Signed_ShouldUpdateToCompleted() {
            // Arrange
            OrderCallBackRequestDTO callbackDTO = createCallbackDTO(OrderCallbackStatusEnum.SIGNED.getCode(), "UCM004");
            OrderDO order = createOrderDO("ORDER004", OrderStatusTypeEnum.IN_TRANSIT.getStatus());

            when(orderMapper.selectOrderBycallBackParameter("UCM004")).thenReturn(order);
            when(orderMapper.updateOrderByNos(anyString(), anyString())).thenReturn(1);

            // Act
            orderLifecycleService.orderCallback(callbackDTO);

            // Assert
            verify(orderMapper).updateOrderByNos("ORDER004", OrderStatusTypeEnum.COMPLETED.getStatus());
            verify(orderPromotionService).asyncNotifyUpdateOrderStatus(eq(order), anyInt(), any());
            verify(orderStatusProducer).sendRoleRefreshMessage(eq(order.getId().intValue()), eq(order.getOrderNo()), any(), any(), any());
        }

        @Test
        @DisplayName("处理空回调数据 - 应该抛出PARAMETER_IS_EMPTY异常")
        void orderCallback_NullData_ShouldThrowException() {
            // Act & Assert
            ServiceException exception = assertThrows(ServiceException.class,
                    () -> orderLifecycleService.orderCallback(null));

            assertEquals(ErrorCodeConstants.PARAMETER_IS_EMPTY.getCode(), exception.getCode());
        }

        @Test
        @DisplayName("处理不存在的订单回调 - 应该抛出ORDER_NOT_EXISTS异常")
        void orderCallback_OrderNotExists_ShouldThrowException() {
            // Arrange
            OrderCallBackRequestDTO callbackDTO = createCallbackDTO(OrderCallbackStatusEnum.SUCCESS.getCode(), "NONEXISTENT");
            when(orderMapper.selectOrderBycallBackParameter("NONEXISTENT")).thenReturn(null);

            // Act & Assert
            ServiceException exception = assertThrows(ServiceException.class,
                    () -> orderLifecycleService.orderCallback(callbackDTO));

            assertEquals(ErrorCodeConstants.ORDER_NOT_EXISTS.getCode(), exception.getCode());
        }
    }

    // 辅助方法：创建订单取消VO
    private OrderCancelVO createOrderCancelVO(String orderNo) {
        OrderCancelVO vo = new OrderCancelVO();
        vo.setOrderNo(orderNo);
        return vo;
    }

    // 辅助方法：创建订单DO
    private OrderDO createOrderDO(String orderNo, String status) {
        OrderDO order = new OrderDO();
        order.setId(1L);
        order.setOrderNo(orderNo);
        order.setStatus(status);
        order.setPersonalId(1L);
        order.setReceivedAmount(1000);
        order.setItemWeight(1000);
        return order;
    }

    // 辅助方法：创建支付订单DO
    private PaymentOrderDO createPaymentOrderDO(Long id, Integer amount) {
        PaymentOrderDO paymentOrder = new PaymentOrderDO();
        paymentOrder.setId(id);
        paymentOrder.setAmount(amount);
        return paymentOrder;
    }

    // 辅助方法：创建回调DTO
    private OrderCallBackRequestDTO createCallbackDTO(Integer statusCode, String ucmOrderSn) {
        OrderCallBackRequestDTO dto = new OrderCallBackRequestDTO();
        OrderCallBackRequestDTO.OrderCallbackData data = new OrderCallBackRequestDTO.OrderCallbackData();
        data.setStatus(statusCode);
        data.setUcmOrderSn(ucmOrderSn);
        data.setExpressSn("EXP123456");
        data.setCourierName("张三");
        data.setCourierMobile("13800138000");
        data.setPickupCode("PICKUP123");
        dto.setData(data);
        return dto;
    }

    // 辅助方法：创建重量修改回调DTO
    private OrderCallBackRequestDTO createWeightModifiedCallbackDTO(String ucmOrderSn, Integer newWeight, Integer freight) {
        OrderCallBackRequestDTO dto = createCallbackDTO(OrderCallbackStatusEnum.WEIGHT_MODIFIED.getCode(), ucmOrderSn);
        dto.getData().setWeight(newWeight);
        dto.getData().setFreight(BigDecimal.valueOf(freight));
        return dto;
    }

    /**
     * 测试配置类
     * 提供测试所需的Executor Bean
     */
    @Configuration
    static class TestConfig {

        @Bean("myUpdateOrderStatusExecutor")
        public Executor myUpdateOrderStatusExecutor() {
            ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
            executor.setCorePoolSize(1);
            executor.setMaxPoolSize(2);
            executor.setQueueCapacity(10);
            executor.setThreadNamePrefix("test-status-executor-");
            executor.initialize();
            return executor;
        }

        @Bean("myUpdatePriceExecutor")
        public Executor myUpdatePriceExecutor() {
            ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
            executor.setCorePoolSize(1);
            executor.setMaxPoolSize(2);
            executor.setQueueCapacity(10);
            executor.setThreadNamePrefix("test-price-executor-");
            executor.initialize();
            return executor;
        }
    }
}
