package com.yunyi.express2b.module.express.api.ordertotal;

import java.time.LocalDate;
import java.util.List;

/**
 * 查询所有已下单代理商的总数
 *
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/5/26 下午2:39
 */

public interface OrderAgentTotalApi {
    /**
     * 查询出所有已下单的代理商数量
     * @param
     * @return
     */
    Long getAgentCount();

    /**
     * 查询出指定日期的已下单代理商数量
     * @param date
     * @return
     */
    Long getOrderCount(Long agentId,LocalDate date,LocalDate dateTomarrow);

    /**
     * 查询出指定日期的已下单代理商收入
     * @param agentId
     * @param date
     * @param dateTomarrow
     * @return
     */
    Long getOrderIncome(Long agentId, LocalDate date, LocalDate dateTomarrow);

    /**
     * 查询出当前代理商的订单数量
     * @param agentId
     * @return
     */
    Long getAgentOrderCount(Long agentId);

    /**
     * 查询出指定日期的已下单代理商数量
     * @param startTime
     * @param endTime
     * @return
     */
    Long getAgentOrderCountByDate(LocalDate startTime, LocalDate endTime);

    /**
     * 获取指定时间段的代理商收入
     * @param startTime
     * @param endTime
     * @return
     */
    Long getOrderIncomeByDate(LocalDate startTime, LocalDate endTime);
}
