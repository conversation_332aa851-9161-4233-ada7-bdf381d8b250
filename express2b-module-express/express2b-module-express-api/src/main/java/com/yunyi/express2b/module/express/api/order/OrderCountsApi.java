package com.yunyi.express2b.module.express.api.order;

import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.module.agent.api.dto.AgentActivityDTO;
import com.yunyi.express2b.module.express.api.order.dto.OrderCountsDTO;
import com.yunyi.express2b.module.express.api.order.dto.OrderDTO;
import com.yunyi.express2b.module.express.api.vo.RequestOrderVO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/6/3 14:29
 */

public interface OrderCountsApi {

    /**
     * 暴露给投流的api接口，查询订单数量
     * @return
     */
    OrderCountsDTO selectOrderCount();

    PageResult<OrderDTO> selectOrderList(RequestOrderVO orderRequestVO);

    /**
     * 在某一时间范围通过代理商id查询订单数
     * @param agentId
     * @return
     */
    Long getAgentOrderSum(Long agentId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查询代理商订单总量
     * @param agentId
     * @return
     */
    List<AgentActivityDTO> getAgentActivity(List<Long> agentId);

}
