package com.yunyi.express2b.module.express.api.order;

import com.yunyi.express2b.module.express.api.order.dto.WorkbenchInterfaceDTO;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 17
 * @since 2025/4/8 14:36
 */

public interface OrderStatusApi {

    /**
     * 根据用户id和未完成的状态查询结果
     * @param personId
     * @return
     */
    Boolean isOrderStatus(Long personId);

    /**
     * 订单数 订单付款数 已入账订单数（） 带入账订单数（） 销售额 实时对比数据下方的对比昨日
     * @param workbenchInterfaceDTO
     * @return
     */
    WorkbenchInterfaceDTO workbenchInterface(WorkbenchInterfaceDTO workbenchInterfaceDTO);

}
