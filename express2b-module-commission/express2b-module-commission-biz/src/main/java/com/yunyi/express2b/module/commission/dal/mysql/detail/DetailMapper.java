package com.yunyi.express2b.module.commission.dal.mysql.detail;

import java.util.*;

import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yunyi.express2b.framework.mybatis.core.mapper.BaseMapperX;
import com.yunyi.express2b.module.commission.dal.dataobject.detail.DetailDO;
import org.apache.ibatis.annotations.Mapper;
import com.yunyi.express2b.module.commission.controller.admin.detail.vo.*;

/**
 * 分润明细 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DetailMapper extends BaseMapperX<DetailDO> {

    default PageResult<DetailDO> selectPage(DetailPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DetailDO>()
                .eqIfPresent(DetailDO::getOrderId, reqVO.getOrderId())
                .eqIfPresent(DetailDO::getOrderNo, reqVO.getOrderNo())
                .eqIfPresent(DetailDO::getTargetId, reqVO.getTargetId())
                .eqIfPresent(DetailDO::getTargetType, reqVO.getTargetType())
                .eqIfPresent(DetailDO::getAmount, reqVO.getAmount())
                .eqIfPresent(DetailDO::getStatus, reqVO.getStatus())
                .eqIfPresent(DetailDO::getReason, reqVO.getReason())
                .geIfPresent(DetailDO::getCreateTime, reqVO.getStartTime())
                .leIfPresent(DetailDO::getCreateTime, reqVO.getEndTime()));
    }

    PageResult<DetailSaveReqVO> selectcommissiondetailsyear(DetailSaveReqVO pageReqVO);

    default List<DetailDO> selectDetailInfo(DetailSaveReqVO createReqVO){
    return selectList(new LambdaQueryWrapperX<DetailDO>()
            .eq(DetailDO::getOrderId, createReqVO.getOrderId())
            .eq(DetailDO::getTargetId, createReqVO.getTargetId()));
    }
}