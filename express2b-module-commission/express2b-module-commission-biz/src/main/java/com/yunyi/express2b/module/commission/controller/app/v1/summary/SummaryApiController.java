package com.yunyi.express2b.module.commission.controller.app.v1.summary;


import com.yunyi.express2b.framework.common.pojo.CommonResult;
import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.framework.common.util.object.BeanUtils;
import com.yunyi.express2b.module.commission.controller.admin.summary.vo.SummaryPageReqVO;
import com.yunyi.express2b.module.commission.controller.admin.summary.vo.SummaryRespVO;
import com.yunyi.express2b.module.commission.dal.dataobject.summary.SummaryDO;
import com.yunyi.express2b.module.commission.service.summary.SummaryService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;

import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.yunyi.express2b.framework.common.pojo.CommonResult.success;


@Tag(name = "小程序 - 分润汇总")
@RestController
@RequestMapping("/v1/commission/summary")
@Validated
public class SummaryApiController {

    @Resource
    private SummaryService summaryService;


    /**
     * 获得分润汇总
     * @param pageReqVO
     * @return
     */
    @GetMapping("/get-summary-by")
    @Operation(summary = "获得分润汇总")
    public CommonResult<PageResult<SummaryRespVO>> getSummary(@Valid SummaryPageReqVO pageReqVO) {
        PageResult<SummaryDO> pageResult = summaryService.getSummaryPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, SummaryRespVO.class));
    }



}