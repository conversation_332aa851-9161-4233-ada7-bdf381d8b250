package com.yunyi.express2b.module.commission.api;

import com.yunyi.express2b.framework.common.pojo.PageResult;
import com.yunyi.express2b.module.commission.api.dto.CommissionDetailDTO;
import com.yunyi.express2b.module.commission.api.dto.DetailDTO;
import com.yunyi.express2b.module.commission.api.vo.DetailRequestVO;
import com.yunyi.express2b.module.commission.api.vo.DetailSaveReqVO;
import jakarta.validation.Valid;

import java.util.List;


public interface CommissionApi {


    /**
     * 创建分润明细
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createDetail(@Valid DetailSaveReqVO createReqVO);

    /**
     * 获取分润明细
     *
     * @param createReqVO 编号
     * @return 分润明细
     */
    List<DetailDTO> selectDetailInfo(@Valid DetailRequestVO createReqVO);

    /**
     * 更新润明细
     *
     * @param id 编号
     * @return 分润明细
     */

    /**
     * 查询分润明细
     * @param
     * @return
     */
    PageResult<CommissionDetailDTO> queryShareData(DetailSaveReqVO detailSaveReqVO);

}
