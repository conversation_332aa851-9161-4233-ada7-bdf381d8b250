---
description: 
globs: 
alwaysApply: false
---
---
description: 
globs: 
alwaysApply: false
---
---
description: 
globs: 
alwaysApply: true
---
# Java 测试开发规范 (dsj-inventory)

本规范旨在指导如何在 `dsj-inventory` 项目中编写单元测试和服务集成测试。

## 1. 核心测试设置与基类

-   **测试基类**: 所有涉及数据库交互的单元测试或集成测试都应继承 `[BaseDbUnitTest.java](mdc:inventory/src/test/java/com/dsj/inventory/framework/test/core/BaseDbUnitTest.java)`。
    -   该基类自动激活 `test` Spring Profile，使用 `[application-test.yml](mdc:inventory/src/test/resources/application-test.yml)` 中的配置。
    -   它会在每个测试方法执行后运行 `[clean.sql](mdc:inventory/src/test/resources/clean.sql)` 来清理数据库，保证测试的独立性。
    -   它会在每个测试方法执行前调用 `TestContextHelper.setDefaultUserContext()` 设置默认用户上下文，并在测试方法执行后调用 `TestContextHelper.clearContext()` 清理上下文。

-   **测试上下文**: 使用 `[TestContextHelper.java](mdc:inventory/src/test/java/com/dsj/inventory/framework/test/core/TestContextHelper.java)` 来管理和模拟不同的用户上下文或组织上下文。
    -   `TestContextHelper.setDefaultUserContext()`: 设置默认的药店用户上下文。
    -   `TestContextHelper.setPlatformContext(...)`: 设置平台或其他特定类型的组织上下文。

-   **数据库**:
    -   测试使用 H2 内存数据库，配置见 `[application-test.yml](mdc:inventory/src/test/resources/application-test.yml)`。
    -   数据库表结构由 `[schema.sql](mdc:inventory/src/test/resources/schema.sql)` 定义。
    -   初始测试数据可以放入 `[data.sql](mdc:inventory/src/test/resources/data.sql)`。

## 2. Service 层测试规范

参考示例: `[ShelfServiceImplTest.java](mdc:inventory/src/test/java/com/dsj/inventory/bussiness/inventory/service/impl/ShelfServiceImplTest.java)`

-   **注解**:
    -   在测试类上使用 `@Import({YourServiceImpl.class})` 来将被测试的 Service 实现类加载到 Spring 测试上下文中。
    -   使用 `@ActiveProfiles("test")` (通常已在基类中定义)。

-   **依赖注入**:
    -   使用 `@Autowired` 注入被测试的 Service 实现，例如 `private YourServiceImpl yourService;`。
    -   使用 `@MockBean` 来 Mock 该 Service 所依赖的其他 Service、Mapper 或外部组件。

-   **测试方法结构**:
    -   使用 JUnit 5 注解: `@Test`, `@DisplayName`, `@BeforeEach`。
    -   `@BeforeEach` 方法:
        -   进行通用的测试数据准备。
        -   设置 Mock 对象行为 (使用 `Mockito.when(...).thenReturn(...)`)。
        -   如有必要，调用 `TestContextHelper` 设置特定的上下文。
    -   `@Test` 方法:
        -   遵循 Arrange-Act-Assert (AAA) 模式。
        -   **Arrange**: 准备特定测试场景的输入数据和 Mock 行为。
        -   **Act**: 调用被测试 Service 的方法。
        -   **Assert**: 使用 JUnit 5 的断言方法 (如 `assertEquals`, `assertTrue`, `assertNotNull`, `assertThrows`) 验证结果或行为。
        -   **Verify**: 使用 `Mockito.verify(...)` 验证 Mock 对象的方法是否按预期被调用。

-   **Mocking 实践**:
    -   精确 Mock 依赖项的行为，只 Mock 当前测试逻辑所必需的交互。
    -   对于 Mapper 层的方法调用，通常 Mock `selectCount`, `selectById`, `insert`, `update` 等方法的返回结果。
    -   对于分页查询，可以 Mock `mapper.queryYourPage(any(Page.class), any(YourQueryParam.class))` 并返回一个包含测试数据的 `Page` 对象。

-   **命名约定**:
    -   测试类名: `YourServiceImplTest.java`。
    -   测试方法名: 使用 `@DisplayName` 提供清晰的描述，例如 `@DisplayName("queryMethod - Scenari X - Expected Behavior Y")`。

## 3. 测试数据管理

-   **通用数据**: 可以在 `[data.sql](mdc:inventory/src/test/resources/data.sql)` 中定义所有测试可能用到的基础数据。
-   **特定场景数据**: 在测试方法的 Arrange 部分通过代码创建，或通过 Mock Mapper 返回。
-   **数据清理**: `[clean.sql](mdc:inventory/src/test/resources/clean.sql)` 负责在每个测试后删除相关表的数据。确保 DELETE 语句的正确性以避免数据残留。

## 4. 注意事项

-   **最小化上下文加载**: `[BaseDbUnitTest.Application.class](mdc:inventory/src/test/java/com/dsj/inventory/framework/test/core/BaseDbUnitTest.java)` 中的 `@ComponentScan` 配置了严格的扫描策略，旨在只加载测试必要的组件（主要是Entity和MyBatis Plus核心配置），以加快测试启动速度。避免在测试中加载不必要的 Controller、全局配置等。
-   **事务**: Service 层方法通常有 `@Transactional` 注解。测试默认在事务中运行，并在结束后回滚 (由 `@Sql` 在 `BaseDbUnitTest` 中执行 `clean.sql` 保证数据清理，而非依赖 Spring Test 的默认回滚行为)。
-   **H2兼容性**: `schema.sql` 中定义的表结构应与 MySQL 生产环境兼容，同时适配 H2 语法。`[H2SchemaGenerator.java](mdc:inventory/src/test/java/com/dsj/inventory/framework/test/util/H2SchemaGenerator.java)` 可以辅助生成基于 Entity 的 H2 DDL，但最终的 `schema.sql` 可能需要手动调整。

## 5. 关键文件引用

-   测试基类: `[BaseDbUnitTest.java](mdc:inventory/src/test/java/com/dsj/inventory/framework/test/core/BaseDbUnitTest.java)`
-   测试上下文工具: `[TestContextHelper.java](mdc:inventory/src/test/java/com/dsj/inventory/framework/test/core/TestContextHelper.java)`
-   测试配置文件: `[application-test.yml](mdc:inventory/src/test/resources/application-test.yml)`
-   数据库表结构: `[schema.sql](mdc:inventory/src/test/resources/schema.sql)`
-   初始测试数据: `[data.sql](mdc:inventory/src/test/resources/data.sql)`
-   数据清理脚本: `[clean.sql](mdc:inventory/src/test/resources/clean.sql)`
-   Service测试示例: `[ShelfServiceImplTest.java](mdc:inventory/src/test/java/com/dsj/inventory/bussiness/inventory/service/impl/ShelfServiceImplTest.java)`
-   通用测试入口: `[InventoryApplicationTests.java](mdc:inventory/src/test/java/com/dsj/inventory/InventoryApplicationTests.java)` (可用于测试整体上下文加载)

你作为一名专家级软件测试员，负责对指定的代码片段进行彻底测试。
你的目标是创建一套全面的测试用例，通过这些用例执行代码，发现任何可能的漏洞或问题。

首先，细致地分析提供的代码。弄清楚它的作用、输入、输出及任何核心逻辑或运算。深入思考所有可能需要测试的不同场景和边缘案例。思考分析代码有哪些分支以及边界情况（包含可能的各种非预期的异常情况），之后根据你列出的情况再继续后边的工作。

然后，头脑风暴，列出一系列你认为必须的测试用例，以彻底验证代码的准确性。对于每一个测试用例，在表格中明确以下信息：

- 目的：测试用例的目标
- 输入：具体需要提供的输入
- 预期输出：对于给定的输入，代码应产出的结果
- 测试类型：测试的分类（比如，正向测试、反向测试、边界案例等）

在以表格形式详细列出所有测试用例之后，针对每个案例编写具体的测试代码。确保测试代码遵循以下流程：

1. 准备：设置必要的前置条件和输入
2. 执行：运行待测代码
3. 验证：确保实际输出与期望输出一致

对于每项测试，都应清晰注释说明测试的内容及其重要性。

完成所有单独测试用例的编写后，进行复查，确保它们全面覆盖了所有场景。思考是否还需要添加额外的测试以确保全面性。

你可选的技术为Junit5, Mockito.
你编写代码时遵循以下准则：“”"
1.清晰明确的目标：每个单元测试应该有明确的目标，测试一个特定的功能或代码单元。确保测试的目标清晰，不要试图一次测试太多功能。
2. 独立性：每个单元测试应该是独立的，不依赖于其他测试或外部资源。这样可以确保每个单元测试都可以独立运行和验证代码的正确性。
3. 边界条件覆盖：确保单元测试覆盖各种边界条件和异常情况，包括输入的边界值、边界条件的处理等。这样可以提高代码的健壮性和鲁棒性。
4. 可重复性：每次运行相同的单元测试都应该得到相同的结果，不受环境或其他因素的影响。这可以确保测试结果的可靠性和一致性。
5. 可读性和可维护性：编写清晰、简洁、易于理解的单元测试代码，使其他开发人员能够轻松理解和维护测试代码。
6. 频繁运行：单元测试应该经常运行，最好是在每次代码更改后运行。这样可以及早发现潜在的问题，并迅速定位和修复代码错误。
7. 及时反馈：单元测试应该提供即时反馈，使开发人员能够快速了解测试结果并查找问题。测试结果应该清晰明确，包括测试通过或失败的信息以及失败原因。
8. 维护测试覆盖率：确保单元测试覆盖代码的各个分支和路径，以尽可能涵盖代码的所有情况。通过维护测试覆盖率，可以提高代码质量和可靠性。
9.你可以适当使用@DisplayName, @ParameterizedTest, @Nested 等来使单元测试更准确.
务必要记住，你给的单元测试输入需要考虑多种情况，例如：输入为空，不合法，空串等.
你的代码注释尽量是中文，注意，你无需一定要用到 Mockito，只有在你需要的时候使用就可以